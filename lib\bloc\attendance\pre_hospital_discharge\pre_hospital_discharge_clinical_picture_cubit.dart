import 'package:ambulancia_app/models/pre_hospital_discharge_clinical_picture_model.dart';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'pre_hospital_discharge_clinical_picture_state.dart';

class PreHospitalDischargeClinicalPictureCubit
    extends Cubit<PreHospitalDischargeClinicalPictureState> {
  PreHospitalDischargeClinicalPictureCubit()
      : super(PreHospitalDischargeClinicalPictureInitial());

  List<PreHospitalDischargeClinicalPictureModel>? _listDischargeClinicalPicture;
  List<PreHospitalDischargeClinicalPictureModel>?
      getListDischargeClinicalPicture() => _listDischargeClinicalPicture;

  Future<void> getPreHospitalDischargeClinicalPicture(
      {required int codUnimed}) async {
    try {
      emit(LoadingPreHospitalDischargeClinicalPicturesState());

      _listDischargeClinicalPicture = await Locator.instance<GraphQlApi>()
          .getPreHospitalDischargeClinicalPicture(codUnimed: codUnimed);

      emit(LoadedPreHospitalDischargeClinicalPictureState(
        listPreHospitalDischargeClinicalPictureModel:
            _listDischargeClinicalPicture,
      ));
    } catch (ex) {
      emit(ErrorLoadPreHospitalDischargeClinicalPicturesState('$ex'));
    }
  }
}

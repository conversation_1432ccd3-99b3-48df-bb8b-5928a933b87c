import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/alert/alert.dart';
import 'package:flutter/material.dart';

import 'package:permission_handler/permission_handler.dart';

showAlertCameraPermission(
    {required BuildContext context,
    required PermissionStatus cameraStatus,
    required Function openBiometry,
    bool showValidadeAlert = false,
    Function? onClose}) async {
  const String baseTranslate = 'alert.biometria';
  //final logger = UnimedLogger(className: 'Biometry');

  if (cameraStatus == PermissionStatus.granted) {
    if (showValidadeAlert) {
      _showAlertBiometry(
          context: context,
          title: I18nHelper.translate(context, '$baseTranslate.validade.title'),
          text: I18nHelper.translate(context, '$baseTranslate.validade.text'),
          textButton:
              I18nHelper.translate(context, '$baseTranslate.validade.button'),
          onClick: () {
            //  logger.i('Abrindo câmera para biometria facial');
            Navigator.pop(context);
            openBiometry();
          });
    } else {
      // logger.i('Abrindo câmera para biometria facial');
      openBiometry();
    }
  } else {
    _showAlertBiometry(
      context: context,
      title: I18nHelper.translate(context, '$baseTranslate.acessCamera.title'),
      text: I18nHelper.translate(context, '$baseTranslate.acessCamera.text'),
      textButton:
          I18nHelper.translate(context, '$baseTranslate.acessCamera.button'),
      onClick: () {
        if (cameraStatus == PermissionStatus.permanentlyDenied) {
          openAppSettings();
        } else {
          Permission.camera.request().then((status) {
            Navigator.pop(context);
            if (status.isGranted) {
              // logger.i('Abrindo câmera para biometria facial');
              openBiometry();
            }
          });
        }
      },
    );
  }
}

_showAlertBiometry(
    {required BuildContext context,
    String? title,
    String? text,
    String? textButton,
    Function? onClick,
    Function? onClose}) {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
            canPop: true,
            child: AlertDialog(
                content: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green /* UnimedColors.green */),
                      ),
                      GestureDetector(
                        onTap: () => {
                          Navigator.of(context).pop(),
                          if (onClose != null) onClose()
                        },
                        child: const Icon(Icons.close,
                            size: 24, color: Colors.green),
                      )
                    ],
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.symmetric(vertical: 24),
                    child: Text(
                      text!,
                      textAlign: TextAlign.center,
                      // style: TextStyle(
                      //     fontWeight: FontWeight.bold,
                      //     color: UnimedColors.grayDark2),
                    )),
                Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: alertButton(
                        context: context,
                        text: textButton!,
                        color: Colors.green /* UnimedColors.green */,
                        onClick: onClick)),
              ],
            )));
      });
}

import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

class ConstantsTheme {
  static double padding = 10.0;
  static double doublePadding = 20.0;
  static double quadruplePadding = 40.0;
  static double borderRadius = 7.0;
  static BoxShadow boxShadow = BoxShadow(
    color: AmbulanceColors.grayDark.withOpacity(.8),
    blurRadius: 15.0,
    spreadRadius: -5.0,
    offset: Offset(0.0, 5.0),
  );

  static OutlineInputBorder enableBorder(
    BuildContext context,
  ) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: BorderSide(
        color: AmbulanceColors.border,
      ),
    );
  }

  static OutlineInputBorder focusedBorder(BuildContext context) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide: BorderSide(width: 1.4, color: AmbulanceColors.greenLight),
    );
  }

  static OutlineInputBorder errorBorder(BuildContext context) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(borderRadius),
      borderSide:
          BorderSide(width: 1.4, color: Theme.of(context).colorScheme.error),
    );
  }
}

class DBNames {
  static const DB = 'unimed_ambulancias.db';
  static const TB_TEAM_VEHICLE = 'team_vehicle';
  static const TB_ATTENDANCES = 'list_attendances';
}

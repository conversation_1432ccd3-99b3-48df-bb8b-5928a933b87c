import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/screens/attendance/attachment_photo/main.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../bloc/conduct/conduct_cubit.dart';

void openModalPhotoAttachmentRegister(
    {required BuildContext context, required int numAtendimento}) {
  const String _fontFamily = 'Icomoon';

  const IconData cancel_circle = IconData(0xea0d, fontFamily: _fontFamily);
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      int maxHeight = context
          .read<AuthCubit>()
          .configAppAmbulanciaConstants
          .imageQuality
          .maxHeightDefault;

      int maxQuality = context
          .read<AuthCubit>()
          .configAppAmbulanciaConstants
          .imageQuality
          .maxQualityDefault;

      return AlertDialog(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(I18nHelper.translate(context, 'conductAttachment.title')),
            BlocBuilder<ConductCubit, ConductState>(
              builder: (context, stateConduct) {
                return IconButton(
                  iconSize: 38,
                  onPressed: stateConduct is LoadingSendConductState
                      ? null
                      : () => Navigator.pop(context),
                  icon: Icon(
                    cancel_circle,
                    color: stateConduct is LoadingSendConductState
                        ? AmbulanceColors.grayLight2
                        : AmbulanceColors.green,
                  ),
                );
              },
            ),
          ],
        ),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius)),
        insetPadding: EdgeInsets.only(
          top: ConstantsTheme.doublePadding * 3,
          bottom: ConstantsTheme.doublePadding,
          left: ConstantsTheme.doublePadding,
          right: ConstantsTheme.doublePadding,
        ),
        content: Container(
          width: MediaQuery.of(context).size.width * 0.75,
          child: CameraAttachmentPhoto(
            numAtendimento: numAtendimento,
            isAttachment: true,
            maxHeight: maxHeight,
            maxQuality: maxQuality,
          ),
          height: MediaQuery.of(context).size.width * 0.85,
        ),
      );
    },
  );
}

class DestinyModel {
  late List<DestinyObject> destinies;

  DestinyModel(List listJson) {
    if (listJson.isEmpty) destinies = [];
    destinies = listJson
        .map<DestinyObject>(
            (destinyJson) => DestinyObject.fromJson(destinyJson))
        .toList();
  }
}

class DestinyObject {
  int? codUnimed;
  String? codigoDestinoPaciente;
  String? nomeDestinoPaciente;

  DestinyObject(
      {this.codUnimed, this.codigoDestinoPaciente, this.nomeDestinoPaciente});

  DestinyObject.fromJson(Map<String, dynamic> json) {
    codUnimed = json['codUnimed'];
    codigoDestinoPaciente = json['codigoDestinoPaciente'];
    nomeDestinoPaciente = json['nomeDestinoPaciente'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['codUnimed'] = this.codUnimed;
    data['codigoDestinoPaciente'] = this.codigoDestinoPaciente;
    data['nomeDestinoPaciente'] = this.nomeDestinoPaciente;
    return data;
  }
}

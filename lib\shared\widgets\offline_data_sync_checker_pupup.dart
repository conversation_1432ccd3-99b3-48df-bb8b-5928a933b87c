import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_notification/offline_first_notification_cubit.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/buttons/unimed_buttons.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class OfflineDataSyncCheckerPopUp {
  final BuildContext context;
  final String baseTranslate;

  OfflineDataSyncCheckerPopUp(
      {required this.context, required this.baseTranslate});

  void checkOfflineDataSync() {
    var state = context.read<ConnectivityCubit>().state;
    var offlineFirstNotificationCubit =
        context.read<OfflineFirstNotificationCubit>();
    if (offlineFirstNotificationCubit.amountsOfDataToBeSynchronizedawait > 0 &&
        state is ConnectivityOfflineState) {
      return Alert.open(
        context,
        title: I18nHelper.translate(
          context,
          '$baseTranslate.checkOfflineDataSync.title',
        ),
        text: I18nHelper.translate(
          context,
          '$baseTranslate.checkOfflineDataSync.text',
        ),
        addButtonClose: false,
        actions: [
          UnimedFlatButton(
            borderColor: AmbulanceColors.redClose,
            text: I18nHelper.translate(
              context,
              '$baseTranslate.checkOfflineDataSync.textButton',
            ).toUpperCase(),
            textColor: Colors.white,
            color: AmbulanceColors.redClose,
            onPressed: () => Navigator.pop(context),
          ),
        ],
      );
    }
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:http/http.dart' as http;

const DEFAULT_TIMEOUT = Duration(seconds: 30);

class UnimedHttpClient extends http.BaseClient {
  final UnimedLogger logger = UnimedLogger(className: 'UnimedHttpClient');

  final _httpClient = HttpUtils.bypassInvalidCertificate();
  final Map<String, String>? defaultHeaders;

  Duration _customTimeout = DEFAULT_TIMEOUT;

  Duration get timeout => _customTimeout;
  Duration get defaultTimeout => DEFAULT_TIMEOUT;

  /// It will be assigned only if the timeout is greater than 0
  ///
  /// The value will only be assigned per request, then it will be changed to the
  /// default value
  set timeout(Duration timeout) => {
        if (timeout.inSeconds > 0) {_customTimeout = timeout}
      };

  UnimedHttpClient({this.defaultHeaders});

  void setToDefaultTimeout(int timeout) {
    logger.d('set timeout after login: $timeout');
    this.timeout = Duration(seconds: timeout);
  }

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final Stopwatch stopwatch = Stopwatch()..start();

    request.headers.addAll(defaultHeaders!);

    logger.d('send headers : ${request.headers}');

    try {
      final tmpTimeout = this.timeout;

      final response = await _httpClient.send(request).timeout(tmpTimeout);
      logger.d(
          'SEND url: ${request.url}      -     time: ${stopwatch.elapsed.inMilliseconds}');
      stopwatch.stop();
      return response;
    } on TimeoutException catch (ex) {
      logger.e('httpClient send TimeoutException $ex');
      throw ServiceTimeoutException();
    } on SocketException catch (ex) {
      logger.e('httpClient send SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient send HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient send Exception $ex');
      throw UnimedException(ex.toString());
    }
  }

  @override
  Future<http.Response> get(url, {Map<String, String>? headers}) async {
    final Stopwatch stopwatch = Stopwatch()..start();
    final _headers = _handleHeaders(headers);

    logger.d('GET headers : $_headers');

    final tmpTimeout = this.timeout;

    try {
      final response = await _httpClient
          .get(url, headers: _headers)
          .timeout(tmpTimeout, onTimeout: () {
        throw ServiceTimeoutException();
      });

      final elapsedTime = stopwatch.elapsed.inMilliseconds;

      // Calcular o tamanho do payload recebido
      int responsePayloadSize = response.bodyBytes.length;

      // Calcular a taxa de transferência
      final transferRate =
          responsePayloadSize / (elapsedTime / 1000); // bytes per second

      logger.d(
          'SEND GET url: $url - time: ${elapsedTime}ms - status code: (${response.statusCode}) - payload size: ${responsePayloadSize} bytes - transfer rate: ${transferRate.toStringAsFixed(2)} B/s');

      stopwatch.stop();

      return response;
    } on TimeoutException catch (ex) {
      logger.e('httpClient send TimeoutException $ex');
      throw ServiceTimeoutException();
    } on SocketException catch (ex) {
      logger.e('httpClient get SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient get HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient get Exception $ex');
      throw UnimedException(ex.toString());
    }
  }

  @override
  Future<http.Response> post(
    url, {
    Map<String, String>? headers,
    body,
    Encoding? encoding,
  }) async {
    final Stopwatch stopwatch = Stopwatch()..start();
    final _headers = _handleHeaders(headers);

    logger.d('POST headers : $_headers');
    logger.d('POST body    : $body');

    // https://dart.dev/guides/language/language-tour#assignment-operators
    encoding ??= Encoding.getByName('utf-8');

    final tmpTimeout = this.timeout;

    // _setToDefaultTimeout();

    // Calcular o tamanho do payload enviado
    int requestPayloadSize = 0;
    if (body != null) {
      if (body is String) {
        requestPayloadSize = utf8.encode(body).length;
      } else if (body is List<int>) {
        requestPayloadSize = body.length;
      } else if (body is Map) {
        requestPayloadSize = utf8.encode(json.encode(body)).length;
      }
    }

    try {
      final response = await _httpClient
          .post(
        url,
        body: body,
        headers: _headers,
        encoding: encoding,
      )
          .timeout(
        tmpTimeout,
        onTimeout: () {
          throw ServiceTimeoutException();
        },
      );

      stopwatch.stop();

      final elapsedTime = stopwatch.elapsed.inMilliseconds;

      // Calcular o tamanho do payload recebido
      int responsePayloadSize = response.bodyBytes.length;

      // Calcular a taxa de transferência
      final totalPayloadSize = requestPayloadSize + responsePayloadSize;
      final transferRate =
          totalPayloadSize / (elapsedTime / 1000); // bytes per second

      logger.d(
          'POST url: $url - time: ${elapsedTime}ms - status code: (${response.statusCode}) - request payload size: ${requestPayloadSize} bytes - response payload size: ${responsePayloadSize} bytes - transfer rate: ${transferRate.toStringAsFixed(2)} B/s');
      logger.d('POST response: ${response.body}');

      return response;
    } on TimeoutException catch (ex) {
      logger.e('httpClient post TimeoutException $ex');
      throw ServiceTimeoutException();
    } on SocketException catch (ex) {
      logger.e('httpClient post SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient post HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient post Exception code ${ex.toString()}');
      throw UnimedException(ex.toString());
    }
  }

  @override
  Future<http.Response> put(url,
      {Map<String, String>? headers, body, Encoding? encoding}) async {
    final Stopwatch stopwatch = Stopwatch()..start();

    final _headers = _handleHeaders(headers);

    logger.d('PUT headers : $_headers');
    logger.d('PUT body    : $body');

    // https://dart.dev/guides/language/language-tour#assignment-operators
    encoding ??= Encoding.getByName('utf-8');

    final tmpTimeout = this.timeout;

    // _setToDefaultTimeout();

    // Calcular o tamanho do payload enviado
    int requestPayloadSize = 0;
    if (body != null) {
      if (body is String) {
        requestPayloadSize = utf8.encode(body).length;
      } else if (body is List<int>) {
        requestPayloadSize = body.length;
      } else if (body is Map) {
        requestPayloadSize = utf8.encode(json.encode(body)).length;
      }
    }

    try {
      final response = await _httpClient
          .put(
        url,
        body: body,
        headers: _headers,
        encoding: encoding,
      )
          .timeout(tmpTimeout, onTimeout: () {
        throw ServiceTimeoutException();
      });
      stopwatch.stop();

      final elapsedTime = stopwatch.elapsed.inMilliseconds;

      // Calcular o tamanho do payload recebido
      int responsePayloadSize = response.bodyBytes.length;

      // Calcular a taxa de transferência
      final totalPayloadSize = requestPayloadSize + responsePayloadSize;
      final transferRate =
          totalPayloadSize / (elapsedTime / 1000); // bytes per second

      logger.d(
          'PUT url: $url - time: ${elapsedTime}ms - status code: (${response.statusCode}) - request payload size: ${requestPayloadSize} bytes - response payload size: ${responsePayloadSize} bytes - transfer rate: ${transferRate.toStringAsFixed(2)} B/s');
      logger.d('PUT response: ${response.body}');
      return response;
    } on TimeoutException catch (ex) {
      logger.e('httpClient put TimeoutException $ex');
      throw ServiceTimeoutException();
    } on SocketException catch (ex) {
      logger.e('httpClient put SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient put HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient put Exception code ${ex.toString()}');
      throw UnimedException(ex.toString());
    }
  }

  Map<String, String> _handleHeaders(Map<String, String>? headers) {
    final headersReturn = Map<String, String>();
    if (defaultHeaders != null) headersReturn.addAll(defaultHeaders!);
    if (headers != null) headersReturn.addAll(headers);

    return headersReturn;
  }

  Future<http.Response> patch(url,
      {Map<String, String>? headers, body, Encoding? encoding}) async {
    final _headers = _handleHeaders(headers);

    final Stopwatch stopwatch = Stopwatch()..start();

    logger.d('PATCH url     : $url');
    logger.d('PATCH headers : $_headers');
    logger.d('PATCH body    : $body');

    final tmpTimeout = this.timeout;

    // Calcular o tamanho do payload enviado
    int requestPayloadSize = 0;
    if (body != null) {
      if (body is String) {
        requestPayloadSize = utf8.encode(body).length;
      } else if (body is List<int>) {
        requestPayloadSize = body.length;
      } else if (body is Map) {
        requestPayloadSize = utf8.encode(json.encode(body)).length;
      }
    }

    try {
      final response = await _httpClient
          .patch(url, body: body, headers: _headers, encoding: encoding)
          .timeout(tmpTimeout, onTimeout: () {
        throw ServiceTimeoutException();
      });

      stopwatch.stop();
      final elapsedTime = stopwatch.elapsed.inMilliseconds;

      // Calcular o tamanho do payload recebido
      int responsePayloadSize = response.bodyBytes.length;

      // Calcular a taxa de transferência
      final totalPayloadSize = requestPayloadSize + responsePayloadSize;
      final transferRate =
          totalPayloadSize / (elapsedTime / 1000); // bytes per second

      logger.d(
          'PATCH url: $url - time: ${elapsedTime}ms - status code: (${response.statusCode}) - request payload size: ${requestPayloadSize} bytes - response payload size: ${responsePayloadSize} bytes - transfer rate: ${transferRate.toStringAsFixed(2)} B/s');
      logger.d('PATCH response: ${response.body}');

      return response;
    } on TimeoutException catch (ex) {
      logger.e('httpClient PATCH TimeoutException $ex');
      throw ServiceTimeoutException();
    } on SocketException catch (ex) {
      logger.e('httpClient PATCH SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient PATCH HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient PATCH Exception code ${ex.toString()}');
      throw UnimedException(ex.toString());
    }
  }

  Future<void> sendFile(String url, String file) async {
    final Stopwatch stopwatch = Stopwatch()..start();

    try {
      var request = http.MultipartRequest("POST", Uri.parse(url));
      request.headers['Authorization'] =
          HttpUtils.getAuthorizationBasicAmbulanciaOsb();

      // Calcular o tamanho do payload enviado
      final fileBytes = await File.fromUri(Uri.parse(file)).readAsBytes();
      final requestPayloadSize = fileBytes.length;

      request.files.add(http.MultipartFile.fromBytes('file', fileBytes));

      final streamedResponse =
          await request.send().timeout(timeout, onTimeout: () {
        throw ServiceTimeoutException();
      });

      final response = await http.Response.fromStream(streamedResponse);

      stopwatch.stop();
      final elapsedTime = stopwatch.elapsed.inMilliseconds;

      // Calcular o tamanho do payload recebido
      int responsePayloadSize = response.bodyBytes.length;

      // Calcular a taxa de transferência
      final totalPayloadSize = requestPayloadSize + responsePayloadSize;
      final transferRate =
          totalPayloadSize / (elapsedTime / 1000); // bytes per second

      logger.d(
          'SEND FILE url: $url - time: ${elapsedTime}ms - status code: (${response.statusCode}) - request payload size: ${requestPayloadSize} bytes - response payload size: ${responsePayloadSize} bytes - transfer rate: ${transferRate.toStringAsFixed(2)} B/s');
      logger.d('SEND FILE response: ${response.body}');

      if (response.statusCode == 200) {
        print('Signature Uploaded!');
      } else {
        throw Exception('Failed to upload file: ${response.statusCode}');
      }
    } catch (e) {
      stopwatch.stop();
      logger.e('SEND FILE Exception: $e');
      throw Exception('===== $e =====');
    }
  }
}

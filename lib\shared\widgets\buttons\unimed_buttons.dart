import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';

class UnimedFlatButton extends StatefulWidget {
  final bool isProgress;
  final Color color;
  final Color borderColor;
  final String text;
  final Color textColor;
  final VoidCallback onPressed;
  final double? height;
  final double? minWidth;

  UnimedFlatButton({
    this.height,
    this.minWidth,
    this.color = unimedGreen,
    this.borderColor = unimedGreen,
    this.text = 'OK',
    this.textColor = Colors.white,
    required this.onPressed,
    this.isProgress = false,
  });

  @override
  _UnimedFlatButtonState createState() => _UnimedFlatButtonState();
}

class _UnimedFlatButtonState extends State<UnimedFlatButton> {
  bool _isDisabled = false;

  void _handleOnPressed() {
    if (!_isDisabled) {
      setState(() {
        _isDisabled = true;
      });
      widget.onPressed();
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Container(
      color: Colors.transparent,
      width: widget.minWidth ?? size.width * 0.28,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: ConstantsTheme.doublePadding),
          backgroundColor: widget.color,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
            side: BorderSide(color: widget.borderColor, width: 2),
          ),
        ),
        child: Text(widget.text, style: TextStyle(color: widget.textColor)),
        onPressed: _isDisabled ? null : _handleOnPressed,
      ),
    );
  }
}

import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/bloc/attendance/attendance_cancel/attendance_cancel_cubit.dart'
    as cancel;
import 'package:ambulancia_app/bloc/attendance/closse_attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/delay_reason/delay_reason_cubit.dart';
import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:ambulancia_app/models/delay_reason_model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/unimed-select.dart';

const String Other = 'OUTROS';

class DelayReasonAttendance extends StatefulWidget {
  final int numAtendimento;
  final String? codTypeReclassify;
  final bool reclassify;
  final String pathPhotoOperator;
  final String title;
  final int? codProtocoloDoenca;
  final String? obsProtocoloDoenca;
  final CloseAttendanceV2Model? closeAttendanceData;

  DelayReasonAttendance({
    required this.numAtendimento,
    required this.pathPhotoOperator,
    required this.title,
    this.codTypeReclassify,
    this.reclassify = false,
    this.codProtocoloDoenca,
    this.obsProtocoloDoenca,
    this.closeAttendanceData,
  });

  @override
  _DelayReasonAttendanceState createState() => _DelayReasonAttendanceState();
}

class _DelayReasonAttendanceState extends State<DelayReasonAttendance> {
  TextEditingController motiveController = TextEditingController();
  TextEditingController motiveOtherController = TextEditingController();
  bool motiveEmpty = true;
  DelayReasonModel? motiveSelected;
  final GlobalKey<FormState> _formKeyAttendanceCancel = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    context.read<DelayReasonCubit>().getAttendanceDelayReasson();

    motiveController.addListener(() {
      setState(() {
        motiveEmpty = motiveController.text.isEmpty;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Atenção"),
      actionsAlignment: MainAxisAlignment.spaceAround,
      content: Container(
        width: MediaQuery.of(context).size.width * 0.75,
        height: (motiveSelected != null &&
                    motiveSelected!.delayReasonName == '${Other}' ||
                (widget.title == otherReasonMessage &&
                    motiveSelected != null &&
                    motiveSelected!.delayReasonName == '${Other}'))
            ? MediaQuery.of(context).size.height * 0.33
            : MediaQuery.of(context).size.height * 0.2,
        child: BlocConsumer<DelayReasonCubit, DelayReasonState>(
          listener: (context, state) {
            if (state is LoadedDelayReasonsState) {
              if (widget.title == otherReasonMessage) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  motiveController.text =
                      widget.title == otherReasonMessage ? Other : '';

                  motiveSelected = widget.title == otherReasonMessage
                      ? context
                          .read<DelayReasonCubit>()
                          .getMotives()
                          ?.firstWhere(
                              (element) => element.delayReasonName == Other)
                      : null;
                });
              }
            }
            ;
          },
          builder: (context, state) {
            if (state is LoadedDelayReasonsState) {
              return Form(
                key: _formKeyAttendanceCancel,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    Text(
                      widget.title,
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 22,
                          color: AmbulanceColors.grayDark2),
                    ),
                    _divider(),
                    SizedBox(height: ConstantsTheme.padding * 0.5),
                    UnimedSelect<DelayReasonModel>(
                      title: 'Motivos',
                      controller: motiveController,
                      items: state.delayReasons!
                          .map((DelayReasonModel motive) =>
                              UnimedSelectItemModel(
                                label:
                                    "${motive.delayReasonCode} - ${motive.delayReasonName}",
                                value: motive,
                              ))
                          .toList(),
                      onSelect: (DelayReasonModel? value) {
                        setState(() => motiveSelected = value);
                      },
                    ),
                    _otherField(),
                    _buildLoading(),
                    _divider(),
                  ],
                ),
              );
            } else if (state is LoadingDelayReasonsState) {
              return SpinKitThreeBounce(
                color: AmbulanceColors.green,
              );
            } else if (state is ErrorLoadDelayReasonsState) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(state.message),
                  Padding(
                    padding: const EdgeInsets.only(left: 10, top: 20),
                    child: IconButton(
                      color: AmbulanceColors.green,
                      iconSize: 40,
                      icon: Icon(Icons.refresh),
                      onPressed: () {
                        context
                            .read<DelayReasonCubit>()
                            .getAttendanceDelayReasson();
                      },
                    ),
                  ),
                ],
              );
            } else {
              return Container();
            }
          },
        ),
      ),
    );
  }

  Widget _otherField() {
    return motiveSelected != null &&
            motiveSelected!.delayReasonName == '${Other}'
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _divider(),
              Text(
                'Descreva o motivo *',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: ConstantsTheme.padding * 0.5),
              TextFormField(
                textAlign: TextAlign.left,
                textAlignVertical: TextAlignVertical.top,
                keyboardType: TextInputType.text,
                controller: motiveOtherController,
                textInputAction: TextInputAction.next,
                onFieldSubmitted: (term) {},
                validator: (value) {
                  final String trimmedValue = value!.replaceAll(' ', '');
                  if (trimmedValue.isEmpty)
                    return "campo obrigatório.";
                  else if (trimmedValue.length < 25)
                    return "mínimo de 25 caracteres.";
                  else
                    return null;
                },
                decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.purple),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.purple),
                  ),
                  contentPadding: EdgeInsets.fromLTRB(15.0, 15.0, 20.0, 15.0),
                ),
                style: TextStyle(color: AmbulanceColors.grayDark),
              )
            ],
          )
        : Container();
  }

  Widget _divider() {
    return SizedBox(height: ConstantsTheme.doublePadding);
  }

  Widget _buildLoading() {
    return BlocBuilder<cancel.AttendanceCancelCubit,
        cancel.AttendanceCancelState>(
      buildWhen: (presState, state) {
        if (state is cancel.ErroCancelAttendanceState) {
          _alertError(text: state.message);
        }
        return true;
      },
      builder: (context, state) {
        if (state is cancel.CancelingAttendanceState)
          return SpinKitThreeBounce(color: AmbulanceColors.green);
        else
          return _options(context);
      },
    );
  }

  void _alertError({required String text}) {
    Alert.open(
      context,
      textButtonClose: 'Fechar',
      title: 'Alerta',
      text: text,
    );
  }

  Widget _options(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: AmbulanceColors.redClose,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 50),
                child: Text(
                  "CANCELAR",
                  style: TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                )),
          ),
          Container(
            width: ConstantsTheme.doublePadding,
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.greenLight3,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: motiveSelected == null ? null : _finalizar,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 50),
              child: Text(
                (motiveEmpty ? "Informe o motivo" : "finalizar").toUpperCase(),
                style: TextStyle(fontSize: 20),
              ),
            ),
          )
        ],
      ),
    );
  }

  void _finalizar() {
    if (motiveSelected != null &&
        motiveSelected!.delayReasonName == '${Other}') {
      if (_formKeyAttendanceCancel.currentState!.validate()) {
        if (widget.closeAttendanceData != null) {
          File imagem = File(widget.pathPhotoOperator);
          List<int> bytesImagem = imagem.readAsBytesSync();
          String imagemBase64 = base64Encode(bytesImagem);
          context.read<CloseAttendanceCubit>().closeAttendanceV2(
                  closeAttendanceData: widget.closeAttendanceData!.copyWith(
                delayReasonCode: motiveSelected!.delayReasonCode,
                delayReasonObservation: motiveOtherController.text,
                imagemBase64: imagemBase64,
                recordType: 'Encerramento',
              ));
        } else {
          context.read<CloseAttendanceCubit>().closeAttendance(
                pathPhotoOperator: widget.pathPhotoOperator,
                serviceNumber: widget.numAtendimento,
                recordType: 'Encerramento',
                codTypeReclassify: widget.codTypeReclassify,
                reclassify: widget.reclassify,
                delayReasonCode: motiveSelected!.delayReasonCode,
                delayReasonObservation: motiveOtherController.text,
                codProtocoloDoenca: widget.codProtocoloDoenca,
                obsProtocoloDoenca: widget.obsProtocoloDoenca,
              );
        }
        Navigator.pop(context);
      }
    } else {
      if (widget.closeAttendanceData != null) {
        File imagem = File(widget.pathPhotoOperator);
        List<int> bytesImagem = imagem.readAsBytesSync();
        String imagemBase64 = base64Encode(bytesImagem);
        context.read<CloseAttendanceCubit>().closeAttendanceV2(
                closeAttendanceData: widget.closeAttendanceData!.copyWith(
              delayReasonCode: motiveSelected!.delayReasonCode,
              delayReasonObservation: motiveOtherController.text,
              imagemBase64: imagemBase64,
              recordType: 'Encerramento',
            ));
      } else {
        context.read<CloseAttendanceCubit>().closeAttendance(
              pathPhotoOperator: widget.pathPhotoOperator,
              serviceNumber: widget.numAtendimento,
              recordType: 'Encerramento',
              codTypeReclassify: widget.codTypeReclassify,
              reclassify: widget.reclassify,
              delayReasonCode: motiveSelected!.delayReasonCode,
              codProtocoloDoenca: widget.codProtocoloDoenca,
              obsProtocoloDoenca: widget.obsProtocoloDoenca,
            );
      }
      Navigator.pop(context);
    }
  }
}

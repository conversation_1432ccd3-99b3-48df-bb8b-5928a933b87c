import 'package:ambulancia_app/models/attachment_model.dart';
import 'package:ambulancia_app/shared/api/conduct/conduct_attachment.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'conduct_attachment_state.dart';

class ConductAttachmentCubit extends Cubit<ConductAttachmentState> {
  ConductAttachmentCubit() : super(ConductAttachmentInit());

  Future<void> sendAttachment({
    required int numAtendimento,
  }) async {
    try {
      emit(LoadingAttachmentState());
      final _response = await Locator.instance.get<ConductAttachmentAPI>()
          .getAttachments(serviceNumber: numAtendimento);

      emit(SuccessAttachmentState(_response));
    } catch (e) {
      emit(ErrorLoadAttachmentState('$e'));
    }
  }

  Future<void> deleteAttachment({
    required int attachmentsNumber,
  }) async {
    try {
      emit(LoadingDeleteAttachmentState());
      await Locator.instance.get<ConductAttachmentAPI>()
          .deleteAttachments(attachmentsNumber: attachmentsNumber);

      emit(SuccessDeleteAttachmentState());
    } catch (e) {
      emit(ErrorDeleteAttachmentState('$e'));
    }
  }
}

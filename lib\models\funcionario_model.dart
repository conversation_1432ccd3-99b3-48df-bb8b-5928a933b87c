import 'package:json_annotation/json_annotation.dart';

part 'funcionario_model.g.dart';

@JsonSerializable(anyMap: true, explicitToJson: true)
class FuncionarioModel {
  final int? codFuncionario;
  final String? nomeFuncionario;
  final int? codFuncao;
  final String? descFuncao;

  FuncionarioModel(
      {this.codFuncionario,
      this.nomeFuncionario,
      this.codFuncao,
      this.descFuncao});

  factory FuncionarioModel.fromJson(Map json) =>
      _$FuncionarioModelFromJson(json);
  Map<String, dynamic> toJson() => _$FuncionarioModelToJson(this);
}

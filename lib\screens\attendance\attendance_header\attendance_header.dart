import 'package:ambulancia_app/bloc/attendance/attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_address/attendance_address_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/attendance_cancel.dart';
import 'package:ambulancia_app/screens/attendance/attendance_header/ambulance_stepper.dart';
import 'package:ambulancia_app/screens/attendance/attendance_header/attendance-team-header.dart';
import 'package:ambulancia_app/screens/attendance/attendance_header/destiny.dart';
import 'package:ambulancia_app/screens/attendance/attendance_header/new-service.dart';
import 'package:ambulancia_app/screens/attendance/map-route/attendance_adressRoute.dart';
import 'package:ambulancia_app/shared/api/websocket.api.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/icons/ambulanceApp_icons.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/alert_localization_permission/alert_localization_permission.dart';
import 'package:ambulancia_app/shared/widgets/attendance/attendance_card_header.dart';
import 'package:ambulancia_app/shared/widgets/toggle/toggle.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:permission_handler/permission_handler.dart';

class AttendanceHeader extends StatefulWidget {
  final AttendanceModel attendance;

  AttendanceHeader({required this.attendance});
  @override
  _AttendanceHeaderState createState() => _AttendanceHeaderState();
}

class _AttendanceHeaderState extends State<AttendanceHeader> {
  int _step = 0;
  TextEditingController destinyController = TextEditingController();

  bool? isCanceled;
  bool? isRemoved;
  String attendanceDestiny = "";
  int codStatusCurrent = 0;
  final _baseTranslate = "attendanceScreen.header";

  @override
  void initState() {
    super.initState();
    context.read<AttendanceAddressCubit>().sendAddresses(widget.attendance);
    isCanceled = widget.attendance.codStatus == AttendanceStatus.ENCERRADO
        ? true
        : false;
    isRemoved = widget.attendance.getRemocao;
    if (widget.attendance.codigoDestinoPaciente != null &&
        widget.attendance.enderecoDestino != null) {
      attendanceDestiny =
          '${widget.attendance.codigoDestinoPaciente} - ${widget.attendance.descricaoDestinoPaciente}';
    }
  }

  void setCodStatus(int codStatus) {
    this.setState(() {
      codStatusCurrent = codStatus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(
            ConstantsTheme.borderRadius,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 13,
                  child: AttendanceCardHeader(
                    attendance: widget.attendance,
                    statusCache: true,
                  ),
                ),
                Spacer(),
                Expanded(
                  flex: 8,
                  child: _switchesRow(),
                ),
              ],
            ),
            SizedBox(height: ConstantsTheme.padding * 2),
            AttendanceTeamHeader(attendance: widget.attendance),
            SizedBox(height: ConstantsTheme.padding),
            AmbulanceStepper(
              currentStep: _step,
              attendance: widget.attendance,
              getCodStatus: setCodStatus,
            ),
            NewService(
              statusAttendance: widget.attendance.codStatus,
              attendance: widget.attendance,
            ),
            Padding(
              padding: const EdgeInsets.all(15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    flex: 4,
                    child: _inputAddressAttendance(constraints),
                  ),
                  Flexible(
                    flex: 4,
                    child: _inputAddressDestiny(),
                  ),
                  Flexible(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.only(top: 20),
                      child: _pin(constraints, context),
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      );
    });
  }

  Widget _inputAddressDestiny() {
    return Opacity(
      opacity: isRemoved! ? 1 : 0.4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 5.0),
            child: Text(
              I18nHelper.translate(context, '$_baseTranslate.destiny'),
              style: TextStyle(fontSize: 16),
            ),
          ),
          InkWell(
            onTap: () {
              if (!isRemoved!) return;
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  content: Destiny(attendance: widget.attendance),
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.only(bottom: 1),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                constraints: BoxConstraints(
                  minWidth: double.infinity,
                ),
                child: SingleChildScrollView(
                  child: Text(
                    attendanceDestiny,
                    style: TextStyle(fontSize: 16, color: Colors.black),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: AmbulanceColors.grayLight2),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _inputAddressAttendance(BoxConstraints constraints) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          I18nHelper.translate(context, '$_baseTranslate.address'),
          style: TextStyle(fontSize: 16),
        ),
        SizedBox(height: 5.0),
        Opacity(
          opacity: 0.6,
          child: InkWell(
            onTap: () {},
            child: Padding(
              padding: const EdgeInsets.only(bottom: 1),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                constraints: BoxConstraints(
                  minWidth: double.infinity,
                ),
                child: SingleChildScrollView(
                  child: Text(
                    '${widget.attendance.addressAttendanceFormatted}',
                    style: TextStyle(fontSize: 16, color: Colors.black),
                    maxLines: 1,
                  ),
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  border: Border.all(color: AmbulanceColors.grayLight2),
                ),
              ),
            ),
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outlined,
              color: AmbulanceColors.grayDark,
              size: 24,
            ),
            Flexible(
              child: Text(
                widget.attendance.addressAttendanceFormattedComplement,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                style: TextStyle(fontSize: 14),
              ),
            )
          ],
        )
      ],
    );
  }

  Widget _pin(constraints, BuildContext context) {
    return BlocBuilder<AttendanceAddressCubit, AttendanceAddressState>(
      builder: (context, state) {
        if (state is LoadingAddressState) {
          return Container(
            width: 50,
            height: 50,
            child: SpinKitCircle(
              color: AmbulanceColors.green,
            ),
          );
        } else {
          return InkWell(
            onTap: () async {
              PermissionStatus permission = await Permission.location.status;
              if (permission == PermissionStatus.denied) {
                AlertLocalizationPermission.grantPermission(context: context);
              } else {
                if (widget.attendance.enderecoDestino != null &&
                    widget.attendance.enderecoDestino?.logradouro == null) {
                  Alert.open(
                    context,
                    textButtonClose:
                        I18nHelper.translate(context, 'common.close'),
                    title: I18nHelper.translate(context, 'common.attention'),
                    text: I18nHelper.translate(
                        context, '$_baseTranslate.warningAddressInvalid.text'),
                  );
                } else {
                  _openAdressModal(constraints);
                }
              }
            },
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: AmbulanceColors.greenLight,
              ),
              child: Center(
                child: Icon(
                  AmbulanceIcons.pin,
                  color: AmbulanceColors.green,
                  size: 30,
                ),
              ),
            ),
          );
        }
      },
    );
  }

  Widget _switchesRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: AmbulanceToggle(
            label: I18nHelper.translate(context, '$_baseTranslate.cancel'),
            activeTrackColor: AmbulanceColors.greenLight,
            inactiveTrackColor: AmbulanceColors.grayLight3,
            forceValue: isCanceled,
            // value: isCanceled,
            onChanged: (bool? value) {
              setState(() => isCanceled = true);
              _openCancelModal();
            },
          ),
        ),
        Expanded(
          child: AmbulanceToggle(
            label: I18nHelper.translate(context, '$_baseTranslate.remotion'),
            activeTrackColor: AmbulanceColors.greenChart,
            inactiveTrackColor: AmbulanceColors.grayLight3,
            forceValue: isRemoved,
            onChanged: (bool? value) {
              if (isRemoved!) return;
              setState(() => isRemoved = value);
              context.read<AttendanceCubit>().setPatientRemoval(value!);
            },
          ),
        ),
      ],
    );
  }

  void _openAdressModal(BoxConstraints constraints) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius)),
          insetPadding: EdgeInsets.only(
            top: ConstantsTheme.doublePadding * 3,
            bottom: ConstantsTheme.doublePadding,
            left: ConstantsTheme.doublePadding,
            right: ConstantsTheme.doublePadding,
          ),
          content: Container(
            width: constraints.maxWidth * 0.8,
            child: SingleChildScrollView(
              child: AttendanceAdressRoute(
                  attendance: widget.attendance,
                  codStatusCurrent: codStatusCurrent),
            ),
          ),
        );
      },
    );
  }

  void _openCancelModal() {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(ConstantsTheme.borderRadius)),
            insetPadding: EdgeInsets.only(
              top: ConstantsTheme.doublePadding * 3,
              bottom: ConstantsTheme.doublePadding,
              left: ConstantsTheme.doublePadding,
              right: ConstantsTheme.doublePadding,
            ),
            content: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              child: AttendanceCancel(
                attendance: widget.attendance,
                onCancel: () {
                  setState(() => isCanceled = false);
                },
                onConfirm: () {
                  Locator.instance.get<WebSocketApi>().dispose();

                  final blocListCubit = context.read<AttendanceListCubit>();
                  final currentAttendance =
                      context.read<AttendanceCubit>().currentAttendance();
                  blocListCubit.updateAttendanceLocalList(
                      attendance: currentAttendance);
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
              ),
            ),
          ),
        );
      },
    );
  }
}

import 'package:flutter/material.dart';

class UnimedSelectLoading extends StatelessWidget {
  final String? title;
  final BoxDecoration? boxDecoration;
  final TextStyle? titleStyle;
  final Color loadingColor;
  final double loadingSize;

  const UnimedSelectLoading({
    Key? key,
    this.title,
    this.boxDecoration,
    this.titleStyle,
    this.loadingColor = Colors.grey,
    this.loadingSize = 20.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 2.5),
            child: Text(
              title!,
              style: titleStyle ??
                  TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.black,
                  ),
            ),
          ),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 10.0),
          decoration: boxDecoration ??
              BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(5.0),
              ),
          child: Container(
            height: 25,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Carregando...",
                  style: TextStyle(color: Colors.grey),
                ),
                SizedBox(
                  width: loadingSize,
                  height: loadingSize,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.0,
                    color: loadingColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

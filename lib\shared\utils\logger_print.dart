import 'package:ambulancia_app/shared/locator.dart';
// import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class UnimedLogPrinter extends LogPrinter {
  final String? className;
  late RemoteLog _remoteLog;

  UnimedLogPrinter(this.className) {
    this._remoteLog = Locator.instance.get<RemoteLog>();
  }

  @override
  List<String> log(LogEvent event) {
    var color = PrettyPrinter.defaultLevelColors[event.level];
    // var emoji = PrettyPrinter.levelEmojis[event.level];
    const isRelease = bool.fromEnvironment("dart.vm.product");

    if (event.level == Level.error) {
      _remoteLog.error(event.message);

      return _localLog(color: color, isRelease: isRelease, event: event);
    } else if (event.level == Level.debug) {
      _remoteLog.debug(event.message);

      return _localLog(color: color, isRelease: isRelease, event: event);
    } else if (event.level == Level.warning) {
      _remoteLog.warning(event.message);

      return _localLog(color: color, isRelease: isRelease, event: event);
    } else {
      _remoteLog.info(event.message);

      return _localLog(color: color, isRelease: isRelease, event: event);
    }
  }

  List<String> _localLog({
    required AnsiColor? color,
    required bool isRelease,
    required LogEvent event,
  }) {
    if (!isRelease) {
      return [
        color!(
          'class: $className - mode: ${const String.fromEnvironment('environment')} - msg: ${event.message}',
        ),
      ];
    } else {
      return [];
    }
  }
}

class ConsoleOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      print(line);
    }
  }
}

class UnimedLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    return true;
  }
}

class UnimedLogger extends Logger {
  final className;

  UnimedLogger({this.className})
      : super(
          printer: UnimedLogPrinter(className),
          filter: UnimedLogFilter(),
        );
}

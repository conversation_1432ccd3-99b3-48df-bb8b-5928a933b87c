import 'package:ambulancia_app/models/config_app_ambulancia_constants_model.dart';
import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(InitialAuthState());

  UserCredentials? _userCredentials;
  UserCredentials? get credentials => _userCredentials;

  UserModel? _userModel;
  UserModel? get userModel => _userModel;

  List<CidRecordSQLite> _cidList = [];
  List<CidRecordSQLite> get cidList => _cidList;

  late ConfigAppAmbulanciaConstants _configAppAmbulanciaConstants;
  ConfigAppAmbulanciaConstants get configAppAmbulanciaConstants =>
      _configAppAmbulanciaConstants;

  Future<void> autenticate(LoginDataModel loginDataModel) async {
    try {
      emit(LoadingAuthState());
      _userCredentials = loginDataModel.userCredentials;
      _userModel = await Locator.instance<AuthApi>().login(loginDataModel);

      _configAppAmbulanciaConstants = await Locator.instance
          .get<GraphQlApi>()
          .getConfigAppAmbulanciaConstants(
              userId: _userCredentials?.user ?? '');

      emit(LoadedAuthState(_userModel));
    } catch (e) {
      emit(ErrorAuthState('${e.toString()}'));
    }
  }

  void signout() async {
    emit(LoadingLogoutUserState());
    await Locator.instance<AuthApi>().logout(userCredentials: _userCredentials);
    _userCredentials = null;
    _userModel = null;
    FirebaseCrashlytics.instance.setUserIdentifier("");
    emit(DoneLogoutUserState());
  }

  void updatePassword(String newPassword) {
    _userCredentials!.password = newPassword;
  }
}

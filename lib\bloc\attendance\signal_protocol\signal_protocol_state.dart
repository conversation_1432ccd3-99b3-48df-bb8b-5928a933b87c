part of 'signal_protocol_cubit.dart';

abstract class SinalProtocolState extends Equatable {
  const SinalProtocolState();

  @override
  List<Object> get props => [];
}

class SinalProtocolInitial extends SinalProtocolState {}

class LoadingSinalProtocolsState extends SinalProtocolState {}

class ErrorLoadSinalProtocolsState extends SinalProtocolState {
  final String message;
  @override
  List<Object> get props => [message];

  ErrorLoadSinalProtocolsState(this.message);
}

class LoadedSinalProtocolsState extends SinalProtocolState {
  final List<SignalProtocolModel>? listSignalProtocols;

  @override
  List<Object> get props => [listSignalProtocols ?? []];
  LoadedSinalProtocolsState({this.listSignalProtocols});
}

class SetProtocolConfirmationtAttendanceState extends SinalProtocolState {
  final bool value;

  @override
  List<Object> get props => [value];
  SetProtocolConfirmationtAttendanceState({required this.value});
}

class SetSignalProtocolSelected extends SinalProtocolState {
  final SignalProtocolModel value;
  @override
  List<Object> get props => [value];
  SetSignalProtocolSelected({required this.value});
}

import 'package:ambulancia_app/models/image_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'conduct_model.g.dart';

@JsonSerializable(anyMap: true, explicitToJson: true)
class ConductModel {
  final int numAtendimento;
  final String observacaoConduta;
  final String codDiagnostico;
  final String descricaoDiagnostico;
  final String descricaoTerapeutica;
  final List<ImageModel> attachments;
  final ImageModel operatorImage;

  ConductModel({
    required this.numAtendimento,
    required this.observacaoConduta,
    required this.codDiagnostico,
    required this.descricaoDiagnostico,
    required this.descricaoTerapeutica,
    required this.attachments,
    required this.operatorImage,
  });

  factory ConductModel.fromJson(Map json) => _$ConductModelFromJson(json);
  Map<String, dynamic> toJson() => _$ConductModelToJson(this);
}

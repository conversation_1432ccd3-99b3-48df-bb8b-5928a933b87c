import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ambulancia_app/main.dart' as app;
import 'package:integration_test/integration_test.dart';
import '../../../shared/text/button/text_button.dart';
import '../../../shared/text/dropdown/text_dropdown.dart';
import '../../../shared/text/textfield/text_textfield.dart';
import '../../../shared/utils/login_sucess.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets(
      'Cancelar primeiro atendimento da lista de atendimentos, por outros motivos',
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginSucess(tester, '60866109323', '123456');

    final Finder selectItemListAttendaceCencelButton =
        find.byIcon(Icons.block_outlined).last;
    await tester.tap(selectItemListAttendaceCencelButton);
    await tester.pumpAndSettle();

    final Finder textFieldAttendanceCancelOptions =
        find.byKey(Key('attendance_cancel_options'));
    await tester.tap(textFieldAttendanceCancelOptions);
    await tester.pumpAndSettle();

    final Finder dropdownOptions = find.text(dropdownOptionsReason);
    await tester.tap(dropdownOptions);
    await tester.pumpAndSettle();

    final reasoForCancellation =
        find.byKey(Key('attendance_reason_ancellation'));
    await tester.enterText(reasoForCancellation, textReasoForCancellation);
    await tester.pumpAndSettle();

    final Finder buttonFinalizarCancelAtendance = find.text(textButtonFinalize);
    await tester.tap(buttonFinalizarCancelAtendance);
    await tester.pumpAndSettle();
  });
}

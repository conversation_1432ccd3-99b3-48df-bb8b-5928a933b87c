import 'package:flutter/material.dart';

const MaterialColor cooperadoPurple = MaterialColor(
  0xFF411564,
  <int, Color>{
    50: Color(0xFFece8f0),
    100: Color(0xFFd9d0e0),
    200: Color(0xFFc6b9d1),
    300: Color(0xFFb3a1c1),
    400: Color(0xFFa08ab2),
    500: Color(0xFF8d73a2),
    600: Color(0xFF7a5b93),
    700: Color(0xFF674483),
    800: Color(0xFF542c74),
    900: Color(0xFF411564),
  },
);
const MaterialColor unimedGreen = MaterialColor(
  0xFF00995D,
  <int, Color>{
    50: Color(0xFFe0f3ec),
    100: Color(0xFFb3e0ce),
    200: Color(0xFF80ccae),
    300: Color(0xFF4db88e),
    400: Color(0xFF26a875),
    500: Color(0xFF00995d),
    600: Color(0xFF009155),
    700: Color(0xFF00864b),
    800: Color(0xFF007c41),
    900: Color(0xFF006b30),
  },
);

const MaterialColor unimedOrange = MaterialColor(
  0xFFF47920,
  <int, Color>{
    50: Color(0xFFfeefe4),
    100: Color(0xFFfcd7bc),
    200: Color(0xFFfabc90),
    300: Color(0xFFf7a163),
    400: Color(0xFFf68d41),
    500: Color(0xFFf47920),
    600: Color(0xFFf3711c),
    700: Color(0xFFf16618),
    800: Color(0xFFef5c13),
    900: Color(0xFFec490b),
  },
);

class UnimedColors {
  static const Color gray = Color.fromRGBO(244, 244, 244, 1);
  static const Color grayLight = Color.fromRGBO(239, 239, 239, 1);
  static const Color grayLight2 = Color.fromRGBO(193, 193, 193, 1);
  static const Color grayDark = Color.fromRGBO(112, 112, 112, 1);
  static const Color grayDark2 = Color.fromRGBO(77, 77, 77, 1);
  static const Color green = Color.fromRGBO(0, 153, 93, 1);
  static const Color greenLight = Color.fromRGBO(190, 215, 0, 1);
  static const Color greenLight2 = Color.fromRGBO(163, 212, 58, 0.3);
  static const Color greenDark = Color.fromRGBO(10, 95, 85, 1);
  static const Color orange = Color.fromRGBO(244, 121, 32, 1);
  static const Color orange2 = Color.fromRGBO(255, 150, 75, 1);

  static const Color purple = Color.fromRGBO(65, 21, 100, 1);
  static const Color yellowLight = Color.fromRGBO(218, 228, 142, 1);
  static const Color greenAsccentChart = Color.fromRGBO(190, 215, 0, 1);
  static const Color pinkChart = Color.fromRGBO(237, 22, 81, 1);
  static const Color greenChart = Color.fromRGBO(0, 133, 81, 1);

  static const Color unimedFone = Color.fromRGBO(104, 45, 0, 1);
  static const Color unimedAeroMedico = Color.fromRGBO(8, 95, 95, 1);
  static const Color opcionalGray2 = Color.fromRGBO(138, 138, 138, 1);
  static const Color opcionalGray = Color.fromRGBO(233, 233, 233, 1);

  static const Color buttonGreenDisabled = Color.fromRGBO(126, 203, 173, 1);
  static const Color buttonGreen = Color.fromRGBO(0, 153, 93, 1);

  static const Color purpleStatus = Color.fromRGBO(163, 35, 142, 1);
  static const Color blueStatus = Color.fromRGBO(65, 21, 100, 1);
  static const Color redStatus = Color.fromRGBO(204, 68, 75, 1);
}

class AmbulanceColors {
  static const Color lightBlue = Color.fromRGBO(180, 180, 230, 1);
  static const Color beige = Color.fromRGBO(249, 228, 183, 1);
  static const Color gray = Color.fromRGBO(244, 244, 244, 1);
  static const Color grayLight = Color.fromRGBO(238, 238, 238, 1);
  static const Color grayLight2 = Color.fromRGBO(91, 92, 101, 1);
  static const Color grayLight3 = Color.fromRGBO(196, 203, 207, 1);
  static const Color grayDark = Color.fromRGBO(112, 112, 112, 1);
  static const Color grayDark2 = Color.fromRGBO(77, 77, 77, 1);
  static const Color green = Color.fromRGBO(10, 95, 85, 1);
  static const Color greenLight = Color.fromRGBO(177, 211, 75, 1);
  static const Color greenLight2 = Color.fromRGBO(163, 212, 58, 0.3);
  static const Color greenLight3 = Color.fromRGBO(0, 153, 93, 1);
  static const Color greenLight4 = Color.fromRGBO(184, 207, 88, 1);
  static const Color greenDark = Color.fromRGBO(10, 95, 85, 1);
  static const Color greenDark2 = Color.fromRGBO(13, 66, 26, 1);
  static const Color orange = Color.fromRGBO(244, 121, 32, 1);
  static const Color orange2 = Color.fromRGBO(255, 150, 75, 1);
  static const Color border = Color.fromRGBO(166, 166, 166, 1);
  static const Color error = Color.fromRGBO(237, 22, 81, 1);

  static const Color purple = Color.fromRGBO(65, 21, 100, 1);
  static const Color purpleSecondary = Color.fromRGBO(163, 35, 142, 1);
  static const Color yellowLight = Color.fromRGBO(218, 228, 142, 1);
  static const Color yellow = Color.fromRGBO(246, 210, 39, 1);
  static const Color greenAsccentChart = Color.fromRGBO(190, 215, 0, 1);
  static const Color pinkChart = Color.fromRGBO(237, 22, 81, 1);
  static const Color greenChart = Color.fromRGBO(0, 133, 81, 1);

  static const Color redClose = Color(0xFFCA203C);
  static const Color acceptColor = Color.fromRGBO(10, 95, 85, 1);

  static const chartColors = [
    Color.fromRGBO(34, 168, 168, 1.0),
    Color.fromRGBO(213, 213, 213, 1.0),
    Color.fromRGBO(115, 175, 198, 1.0),
    Color.fromRGBO(56, 66, 81, 1.0),
    Color.fromRGBO(251, 163, 167, 1.0),
    Color.fromRGBO(123, 145, 162, 1.0),
    Color.fromRGBO(0, 64, 26, 1.0),
    Color.fromRGBO(0, 153, 93, 1.0),
    Color.fromRGBO(237, 22, 81, 1.0),
    Color.fromRGBO(49, 235, 159, 1.0),
    Color.fromRGBO(65, 21, 100, 1.0),
  ];
}

class ThemeAmbulancia {
  static ThemeData green() {
    return ThemeData(
      useMaterial3: false,
      buttonTheme: ButtonThemeData(
        minWidth: 10,
        buttonColor: unimedGreen,
        textTheme: ButtonTextTheme.primary,
      ),
      fontFamily: 'UnimedSans',
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: AmbulanceColors.grayDark),
        bodyMedium: TextStyle(color: AmbulanceColors.grayDark),
        displayLarge: TextStyle(color: AmbulanceColors.grayDark),
        titleLarge: TextStyle(color: AmbulanceColors.grayDark),
      ),
      colorScheme: ColorScheme.fromSwatch(primarySwatch: unimedGreen)
          .copyWith(surface: AmbulanceColors.grayLight),
    );
  }
}

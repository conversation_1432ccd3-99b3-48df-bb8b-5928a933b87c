// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geolocation-config.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeolocationConfigModel _$GeolocationConfigModelFromJson(
        Map<String, dynamic> json) =>
    GeolocationConfigModel(
      distanceFilter: json['distanceFilter'] as int?,
      timeIntervalSec: json['timeIntervalSec'] as int?,
    );

Map<String, dynamic> _$GeolocationConfigModelToJson(
        GeolocationConfigModel instance) =>
    <String, dynamic>{
      'distanceFilter': instance.distanceFilter,
      'timeIntervalSec': instance.timeIntervalSec,
    };

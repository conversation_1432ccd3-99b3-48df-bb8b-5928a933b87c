import 'package:ambulancia_app/models/version-remote.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  VersionRemoteModel? testVersionRemoteModel;
  Map? jsonVersionRemoteModel;
  setUpAll(
    () {
      testVersionRemoteModel = VersionRemoteModel(
        forceUpdate: true,
        buildNumber: 1222,
        versionNumber: "2",
      );
      jsonVersionRemoteModel = {
        "force_update": false,
        "build_number": 310023,
        "version_number": "3.1.0"
      };
    },
  );

  group(
    "isInstanceOf VersionRemoteModel model tests",
    () {
      test("Should be return instance of VersionRemoteModel", () {
        expect(testVersionRemoteModel, isInstanceOf<VersionRemoteModel>());
      });

      test("Should be return instance of String", () {
        expect(testVersionRemoteModel!.versionNumber, isInstanceOf<String>());
      });
      test("Should be return instance of Int", () {
        expect(testVersionRemoteModel!.buildNumber, isInstanceOf<int>());
      });
      test("Should be return instance of Bool", () {
        expect(testVersionRemoteModel!.forceUpdate, isInstanceOf<bool>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of VersionRemoteModel to json", () {
      expect(testVersionRemoteModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of VersionRemoteModel from json", () {
      expect(VersionRemoteModel.fromJson(jsonVersionRemoteModel!),
          isInstanceOf<VersionRemoteModel>());
    });
  });

  group(
    "isInstanceOf VersionRemoteModel json to model type test",
    () {
      test("Should be return instance of bool", () {
        expect(jsonVersionRemoteModel!["force_update"], isInstanceOf<bool>());
      });

      test("Should be return instance of int", () {
        expect(jsonVersionRemoteModel!["build_number"], isInstanceOf<int>());
      });
      test("Should be return instance of String", () {
        expect(
            jsonVersionRemoteModel!["version_number"], isInstanceOf<String>());
      });
      test("Can´t return if is null", () {
        expect(jsonVersionRemoteModel!["force_update"] == null, false);
        expect(jsonVersionRemoteModel!["build_number"] == null, false);
        expect(jsonVersionRemoteModel!["version_number"] == null, false);
      });
    },
  );

  group("Json test errors", () {
    Map<dynamic, dynamic> jsonError = {
      "force_update": "true",
      "build_number": 310023,
      "version_number": "3.2.0"
    };

    test(
        "Should be return an error 'type 'String' is not a subtype of type 'bool?' in type cast",
        () {
      try {
        VersionRemoteModel.fromJson(jsonError);
      } catch (e) {
        expect(e.toString(),
            "type 'String' is not a subtype of type 'bool?' in type cast");
      }
    });
    test("Should be return a version number 320000", () {
      expect(
          int.parse(
                  jsonError["version_number"].toString().replaceAll(".", "")) *
              1000,
          320000);
    });
    test("Should be return a build number 310023", () {
      expect(jsonError["build_number"], 310023);
    });
    test("Should be return if needs to update", () {
      expect(
          jsonError["build_number"] >=
              int.parse(jsonError["version_number"]
                      .toString()
                      .replaceAll(".", "")) *
                  1000,
          false);
      jsonError["force_update"] = true;
      expect(jsonError["force_update"], true);
    });
  });
}

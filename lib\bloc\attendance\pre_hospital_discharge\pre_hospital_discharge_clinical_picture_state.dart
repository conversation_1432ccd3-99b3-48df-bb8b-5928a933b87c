part of 'pre_hospital_discharge_clinical_picture_cubit.dart';

abstract class PreHospitalDischargeClinicalPictureState extends Equatable {
  const PreHospitalDischargeClinicalPictureState();

  @override
  List<Object> get props => [];
}

class PreHospitalDischargeClinicalPictureInitial
    extends PreHospitalDischargeClinicalPictureState {}

class LoadingPreHospitalDischargeClinicalPicturesState
    extends PreHospitalDischargeClinicalPictureState {}

class ErrorLoadPreHospitalDischargeClinicalPicturesState
    extends PreHospitalDischargeClinicalPictureState {
  final String message;
  ErrorLoadPreHospitalDischargeClinicalPicturesState(this.message);
}

class LoadedPreHospitalDischargeClinicalPictureState
    extends PreHospitalDischargeClinicalPictureState {
  final List<PreHospitalDischargeClinicalPictureModel>?
      listPreHospitalDischargeClinicalPictureModel;
  LoadedPreHospitalDischargeClinicalPictureState(
      {this.listPreHospitalDischargeClinicalPictureModel});
}

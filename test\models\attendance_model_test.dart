import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  AttendanceModel? testAttendanceModel;
  Map<String, dynamic>? jsonAttendanceModel;

  setUpAll(() {
    jsonAttendanceModel = {
      "numAtendimento": 183780,
      "codUnimed": 63,
      "codVeiculo": "14",
      "codTipoCliente": "1",
      "nomeTipoCliente": "UNIMED URGENTE",
      "codTipoAtendimento": "C4",
      "nomeTipoAtendimento": "MINIMA URGENCIA",
      "codStatus": 6,
      "nomeStatus": "SAÍDA ORIGEM",
      "unimedCarteira": 63,
      "codigoCarteira": 2006392563,
      "dvCarteira": "0",
      "idade": 24,
      "sintomaAcontecendo": "TESTE",
      "sintomaComecou": "TESTE",
      "sintomaHistorico": "TESTE",
      "sintomaQuando": "TESTE",
      "observacaoMedicoRegulador": "TESTE",
      "dataAtendimento": "2023-01-18 14:28:39",
      "nomeMae": "MARIA FAKER",
      "equipe": [
        {
          "codFuncionario": 3281,
          "nomeFuncionario": "ORISMAR VELOSO LOIOLA",
          "codFuncao": 24,
          "descFuncao": "MOTORISTA SOCORRISTA"
        },
        {
          "codFuncionario": 9663,
          "nomeFuncionario": "JOSE ERNANDO MESQUITA MOTA",
          "codFuncao": 1,
          "descFuncao": "AUXILIAR DE ENFERMAGEM"
        }
      ],
      "nome": "DAVI BEZERRA N GOMES",
      "rg": "20079542411",
      "cpf": "60866109323",
      "dataNascimento": "1998-01-25 00:00:00",
      "enderecoAtendimento": {
        "numero": "77",
        "tipoLogradouro": "AV",
        "logradouro": "DOM MANUEL",
        "bairro": "CENTRO",
        "cidade": "FORTALEZA",
        "CEP": "60060090",
        "complemento": "Casa",
        "uf": "CE"
      },
      "enderecoDestino": {
        "numero": "950",
        "tipoLogradouro": "R",
        "logradouro": "ANDRADE FURTADO",
        "bairro": "COCO",
        "cidade": "FORTALEZA",
        "CEP": "60192070",
        "complemento": "APTO 501"
      },
      "remocao": "S",
      "codigoDestinoPaciente": "1",
      "descricaoDestinoPaciente": "HOSPITAL SAO RAIMUNDO",
      "materiais": [
        {"codigoMaterial": 1, "descricao": "AAS", "quantidade": 50}
      ],
      "uniurgProtocoloDoencas": {
        "codProtocoloDoenca": 1,
        "nomeProtocoloDoenca": "Protocolo Teste"
      },
      "obsProtocoloDoenca": "Observação do protocolo"
    };
  });

  group("AttendanceModel Tests", () {
    test("Should create an instance of AttendanceModel from JSON", () {
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      expect(testAttendanceModel, isInstanceOf<AttendanceModel>());
    });

    test("Should convert AttendanceModel to JSON", () {
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      final json = testAttendanceModel!.toJson();
      expect(json, isInstanceOf<Map<String, dynamic>>());
      expect(json['nome'], equals("DAVI BEZERRA N GOMES"));
    });

    test("Should handle null values in JSON gracefully", () {
      jsonAttendanceModel!['cpf'] = null;
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      expect(testAttendanceModel!.cpf, isNull);
    });

    test("Should validate getRemocao logic", () {
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      expect(testAttendanceModel!.getRemocao, isTrue);

      testAttendanceModel!.remocao = 'N';
      expect(testAttendanceModel!.getRemocao, isFalse);
    });

    test("Should format address correctly", () {
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      expect(
        testAttendanceModel!.addressAttendanceFormatted,
        equals('AV DOM MANUEL, '),
      );

      testAttendanceModel!.enderecoAtendimento!.tipoLogradouro = null;
      expect(
        testAttendanceModel!.addressAttendanceFormatted,
        equals('Selecione o Destino'),
      );
    });

    test("Should validate UniurgProtocoloDoencas serialization", () {
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      final protocolo = testAttendanceModel!.uniurgProtocoloDoencas;
      expect(protocolo, isNotNull);
      expect(protocolo!.codProtocoloDoenca, equals(1));
      expect(protocolo.nomeProtocoloDoenca, equals("Protocolo Teste"));
    });

    test("Should validate copyWith functionality", () {
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      final updatedModel = testAttendanceModel!.copyWith(nome: "Updated Name");
      expect(updatedModel.nome, equals("Updated Name"));
      expect(updatedModel.rg, equals(testAttendanceModel!.rg));
    });

    test("Should validate carteiraNumero generation", () {
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);
      final expectedCarteira = '0630020063925630';
      expect(testAttendanceModel!.carteiraNumero, equals(expectedCarteira));
    });

    test("Should handle invalid data gracefully", () {
      jsonAttendanceModel!['numAtendimento'] = null;
      expect(() => AttendanceModel.fromJson(jsonAttendanceModel!),
          throwsA(isA<TypeError>()));
    });
  });

  group("DropdownAttendanceModel Tests", () {
    test("Should create an instance with valid data", () {
      final dropdownModel = DropdownAttendanceModel(id: 1, name: "Test");
      expect(dropdownModel.id, equals(1));
      expect(dropdownModel.name, equals("Test"));
    });

    test("Should handle null values", () {
      final dropdownModel = DropdownAttendanceModel();
      expect(dropdownModel.id, isNull);
      expect(dropdownModel.name, isNull);
    });
  });
}

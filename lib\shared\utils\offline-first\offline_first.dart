import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/services/connectivity.service.dart';
import 'package:ambulancia_app/shared/services/sync-offline.service.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/response.table.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/sync.table.dart';

import '../../locator.dart';

class OfflineFirst {
  //TODO:categoty é o indicador que é usado para limpar a cache do de responses do usuário
  static Future persistResponse(
      {required String customID,
      required String category,
      required String body}) async {
    final sqliteTableResponse = ResponseTableSQLite();
    final LoginDataModel? loginDataModel =
        await Locator.instance.get<AuthApi>().getCredentials();

    ResponseRecordSQLite response = ResponseRecordSQLite(
        customId:
            '${customID}_${loginDataModel!.userCredentials!.userClean}_${loginDataModel.vehicleModel!.nomeVeiculo.toString().replaceAll(' ', '_')}',
        value: body,
        category: category);
    await sqliteTableResponse.addOrUpdate(response);
  }

  static Future<String?> getCacheResponse({required String customID}) async {
    final sqliteTableResponse = ResponseTableSQLite();
    final LoginDataModel? loginDataModel =
        await Locator.instance.get<AuthApi>().getCredentials();
    ResponseRecordSQLite? valuesCurrentJson =
        await sqliteTableResponse.getByCustomId(
      customId:
          '${customID}_${loginDataModel!.userCredentials!.userClean}_${loginDataModel.vehicleModel!.nomeVeiculo.toString().replaceAll(' ', '_')}',
    );
    return valuesCurrentJson?.value;
  }

  static Future<List<SyncRecordSQLite>?> getCacheRequest(
      {required customID}) async {
    final sqliteTableSync = SyncTableSQLite();
    final LoginDataModel? loginDataModel =
        await Locator.instance.get<AuthApi>().getCredentials();
    final listRequest = await sqliteTableSync.getByCustomId(
      customId:
          '${customID}_${loginDataModel!.userCredentials!.userClean}_${loginDataModel.vehicleModel!.nomeVeiculo.toString().replaceAll(' ', '_')}',
    );
    return listRequest;
  }

  static Future persistRequest({
    required String customId,
    required String body,
    required String url,
    required Map<String, String> headers,
    required SYNC_TYPE_REQUEST typeRequest,
    bool isPersistRequestConduct = false,
  }) async {
    final sqliteTable = SyncTableSQLite();
    final LoginDataModel? loginDataModel =
        await Locator.instance.get<AuthApi>().getCredentials();
    SyncRecordSQLite request = SyncRecordSQLite(
        customId:
            '${customId}_${loginDataModel!.userCredentials!.userClean}_${loginDataModel.vehicleModel!.nomeVeiculo.toString().replaceAll(' ', '_')}',
        jsonRequest: body,
        url: url,
        header: headers,
        typeRequest: typeRequest);
    isPersistRequestConduct
        ? await sqliteTable.persistDataConduct(dataObject: request)
        : await sqliteTable.persistData(dataObject: request);
  }

  static Future persistRequestWithFile({
    required String customId,
    required String url,
    required String pathFile,
    required String fileName,
    required String authorization,
    required String jsonFields,
  }) async {
    final sqliteTable = SyncTableSQLite();
    final LoginDataModel? loginDataModel =
        await Locator.instance.get<AuthApi>().getCredentials();
    final request = SyncRecordSQLite(
        customId:
            '${customId}_${loginDataModel!.userCredentials!.userClean}_${loginDataModel.vehicleModel!.nomeVeiculo.toString().replaceAll(' ', '_')}',
        jsonRequest: '',
        url: url,
        header: {},
        pathFile: pathFile,
        fileName: fileName,
        authorization: authorization,
        jsonFields: jsonFields,
        typeRequest: SYNC_TYPE_REQUEST.POST_WITH_FILE);
    await sqliteTable.persistData(dataObject: request);
  }

  static Future<void> syncDatabase() async {
    bool _connectivityResult =
        await Locator.instance.get<ConnectivityService>().checkConnection();
    if (_connectivityResult == true) {
      await Locator.instance<SyncOfflineService>().syncTable();
    }
  }
}

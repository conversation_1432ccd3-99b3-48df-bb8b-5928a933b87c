import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CloseAttendanceV2Model', () {
    test('toJson deve retornar um mapa correto com todos os campos preenchidos',
        () {
      final model = CloseAttendanceV2Model(
        reclassify: true,
        codTypeReclassify: 'tipo1',
        delayReasonCode: 123,
        delayReasonObservation: 'Trânsito',
        codProtocoloDoenca: 456,
        obsProtocoloDoenca: 'Observação doença',
        preHospitalDischargeObs: 'Obs alta',
        preHospDischargeDateTime: '2024-06-23T10:00:00',
        preHospDischargeReceivingDoctor: 'Dr. House',
        preHospitalDischargeDiagnosis: 'Diagnóstico',
        preHospDischargeReasonCode: 1,
        otherPreHospDischargeReason: 'Outro motivo',
        preHospDischargeClinicalPictureCode: 2,
        newPreHospDischargeClinicalPicture: 'Nova clínica',
        recordType: 'Avaliação Clínica',
        numAtendimento: '12345',
        imagemBase64: 'base64string',
      );

      final json = model.toJson();

      expect(json['reclassify'], true);
      expect(json['codTypeReclassify'], 'tipo1');
      expect(json['delayReasonCode'], 123);
      expect(json['delayReasonObservation'], 'Trânsito');
      expect(json['codProtocolSignal'], 456);
      expect(json['obsSignsOfIllness'], 'Observação doença');
      expect(json['preHospitalDischargeObs'], 'Obs alta');
      expect(json['preHospDischargeDateTime'], '2024-06-23T10:00:00');
      expect(json['preHospDischargeReceivingDoctor'], 'Dr. House');
      expect(json['preHospitalDischargeDiagnosis'], 'Diagnóstico');
      expect(json['preHospDischargeReasonCode'], 1);
      expect(json['otherPreHospDischargeReason'], 'Outro motivo');
      expect(json['preHospDischargeClinicalPictureCode'], 2);
      expect(json['newPreHospDischargeClinicalPicture'], 'Nova clínica');
      expect(json['image']['fileName'], 'ac_12345.png');
      expect(json['image']['fileBase64'], 'base64string');
    });

    test('toJson deve omitir campos opcionais quando não preenchidos', () {
      final model = CloseAttendanceV2Model(
        reclassify: false,
        preHospitalDischargeObs: 'Obs',
        preHospDischargeDateTime: '2024-06-23T10:00:00',
        preHospitalDischargeDiagnosis: 'Diagnóstico',
        preHospDischargeReasonCode: 1,
        preHospDischargeClinicalPictureCode: 2,
        numAtendimento: '54321',
        recordType: 'Conduta',
      );

      final json = model.toJson();

      expect(json['reclassify'], false);
      expect(json.containsKey('codTypeReclassify'), false);
      expect(json['delayReasonCode'], null);
      expect(json['delayReasonObservation'], null);
      expect(json.containsKey('codProtocolSignal'), false);
      expect(json.containsKey('obsSignsOfIllness'), false);
      expect(json['preHospitalDischargeObs'], 'Obs');
      expect(json['preHospDischargeDateTime'], '2024-06-23T10:00:00');
      expect(json['preHospitalDischargeDiagnosis'], 'Diagnóstico');
      expect(json['preHospDischargeReasonCode'], 1);
      expect(json['preHospDischargeClinicalPictureCode'], 2);
      expect(json['image']['fileName'], 'c_54321.png');
    });

    test('copyWith deve criar uma cópia com valores atualizados', () {
      final model = CloseAttendanceV2Model(
        reclassify: false,
        preHospitalDischargeObs: 'Obs',
        preHospDischargeDateTime: '2024-06-23T10:00:00',
        preHospitalDischargeDiagnosis: 'Diagnóstico',
        preHospDischargeReasonCode: 1,
        preHospDischargeClinicalPictureCode: 2,
        numAtendimento: '54321',
        recordType: 'Encerramento',
      );

      final copy = model.copyWith(
        reclassify: true,
        codTypeReclassify: 'novoTipo',
        imagemBase64: 'novaImagem',
      );

      expect(copy.reclassify, true);
      expect(copy.codTypeReclassify, 'novoTipo');
      expect(copy.imagemBase64, 'novaImagem');
      expect(copy.numAtendimento, '54321');
      expect(copy.recordType, 'Encerramento');
    });
  });
}

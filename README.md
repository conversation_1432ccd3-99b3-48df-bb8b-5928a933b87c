<img src="https://img.shields.io/badge/Flutter-02569B?style=for-the-badge&logo=flutter&logoColor=white" />

# Sistema de Ambulâncias

## Comandos

- Quando algum model com modelo JsonSerializable for criado ou ajustado rodar o seguinte comando:
- $ flutter pub run build_runner build --delete-conflicting-outputs
- Para gerar versão app distribution
- $ flutter pub run ambulancia_app:firebase_upload_android
- Para desativar a verificação de telefone/tablet, adicione o seguinte comando no build ou no - run da aplicação:
- $ --dart-define=VERIFY='off'

## :computer: Tecnologias

- Flutter 3.24.3
- Java: openjdk 17.0.14 2025-01-21
- Bloc (Cubit) 8.0.0

## Documentações

| README  | Link                                    |
|---------|-----------------------------------------|
| Testes Unitários | [Link](README_TEST_UNIT.md) |
| Testes de integração | [Link](README_INTEGRATION_TEST.md) |
| Atualizações de cids passa a passo | [Link](README_UPDATE_CIDS.md) |

Update Nestjs

```bash
npm i -g npm-check-updates
ncu -u -f /^@nestjs/
```

Verificar pacotes não usados

```bash
npm install -g depcheck
depcheck
```

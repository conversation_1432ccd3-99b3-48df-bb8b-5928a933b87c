class VersionValidateResponse {
  String? message;
  String? lastVersion;
  String? version;
  bool? force;
  bool? isOutOfDate;

  VersionValidateResponse(
      {this.message,
      this.lastVersion,
      this.version,
      this.force,
      this.isOutOfDate});

  VersionValidateResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    lastVersion = json['lastVersion'];
    version = json['version'];
    force = json['force'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['message'] = this.message;
    data['lastVersion'] = this.lastVersion;
    data['version'] = this.version;
    data['force'] = this.force;
    data['isOutOfDate'] = this.isOutOfDate;
    return data;
  }
}

abstract class SignaturePadState {
  const SignaturePadState();
}

class SubimitSignature extends SignaturePadState {}

class SignaturePadInitial extends SignaturePadState {}

class LoadingSendSignature extends SignaturePadState {}

class SuccessSendSignature extends SignaturePadState {
  final String? message;
  SuccessSendSignature(this.message);
}

class ErrorSendSignaturePadState extends SignaturePadState {
  final String message;
  ErrorSendSignaturePadState(this.message);
}

class LoadingVerifySubscription extends SignaturePadState {}

class SuccessSendVerifySubscription extends SignaturePadState {
  final bool? signed;
  SuccessSendVerifySubscription(this.signed);
}

class ErrorVerifySubscription extends SignaturePadState {
  final String message;
  ErrorVerifySubscription(this.message);
}

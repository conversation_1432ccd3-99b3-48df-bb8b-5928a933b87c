import 'package:ambulancia_app/shared/constants/keys/constants_keys.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

class Alert {
  static void open(
    BuildContext context, {
    Color? buttonCloseColor,
    String title = 'Alerta',
    String text = '',
    String textButtonClose = 'FECHAR',
    List<String>? lines,
    List<Widget>? actions,
    Function? callbackClose,
    bool barrierDismissible = false,
    bool addButtonClose = true,
  }) async {
    bool _disableButton = false;
    List<Widget> _texts = [
      Text(
        '$text',
        style: TextStyle(
            color: AmbulanceColors.grayDark2, fontWeight: FontWeight.bold),
      )
    ];

    List<Widget> _actions = [];
    if (text.isNotEmpty && lines != null && lines.length > 0) {
      _texts.addAll(lines.map((l) => Text(
            '$l',
            style: TextStyle(
              color: unimedGreen.shade900,
            ),
          )));
    }

    if (actions != null && actions.length > 0) {
      _actions = actions;
    }

    if (addButtonClose) {
      _actions.add(
        StatefulBuilder(
          builder: (context, setState) {
            return ElevatedButton(
              style: ElevatedButton.styleFrom(
                  backgroundColor: buttonCloseColor ??=
                      AmbulanceColors.redClose,
                  textStyle: TextStyle(
                    color: Colors.white,
                  )),
              child: Text(textButtonClose),
              onPressed: _disableButton == true
                  ? null
                  : () {
                      setState(
                        () {
                          _disableButton = !_disableButton;
                        },
                      );

                      Navigator.of(context).pop();

                      if (callbackClose != null) {
                        callbackClose();
                      }
                    },
            );
          },
        ),
      );
    }

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return PopScope(
          canPop: barrierDismissible,
          child: Transform(
            transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                backgroundColor: Colors.white,
                title: Text(
                  '$title',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AmbulanceColors.greenDark,
                  ),
                ),
                content: SingleChildScrollView(
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: _texts,
                  ),
                ),
                actions: _actions,
              ),
            ),
          ),
        );
      },
    );
  }
}

class AmbulanciaAlertDialog extends StatelessWidget {
  AmbulanciaAlertDialog(
      {required this.onPressed,
      required this.textWidget,
      this.iconData = Icons.info_outline,
      this.textButton = "Ok",
      this.colorIcon = AmbulanceColors.greenDark});

  final IconData iconData;
  final VoidCallback onPressed;
  final Widget textWidget;
  final textButton;
  final colorIcon;

  @override
  Widget build(context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: Center(child: Icon(iconData, size: 70.0, color: colorIcon)),
      content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            textWidget,
            Divider(
              height: 5.0,
              color: Colors.transparent,
            ),
            SizedBox(height: 10.0),
            ElevatedButton(
                key: ConstantsKey.login_button_alert,
                style: ElevatedButton.styleFrom(
                  textStyle: TextStyle(color: Colors.white),
                  backgroundColor: AmbulanceColors.green,
                ),
                child: Text(textButton),
                onPressed: onPressed),
          ]),
      backgroundColor: Colors.white,
    );
  }
}

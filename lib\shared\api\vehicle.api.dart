import 'dart:convert';

import 'package:ambulancia_app/models/vehicle_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:http/http.dart' as http;

class VehicleApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'TeamApi');
  VehicleApi(this.httpClient);

  Future<List<VehicleModel>?> listVehicle(String codUnimed) async {
    final endpoint = 'ambulance/vehicle';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);
        logger.d('listVehicle retorno : $bodyRetorno');

        final vehicles = bodyRetorno
            .map<VehicleModel>((json) => VehicleModel.fromJson(json))
            .toList();
        return vehicles;
      } else {
        logger.e('listVehicle ${response.statusCode} found');
        throw VehicleException(MessageException.general);
      }
    } on NotFoundException catch (ex) {
      logger.i('listVehicle NotFoundException ${ex.message}');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.i('listVehicle NoInternetException ${ex.message}');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('listVehicle ServiceTimeoutException $ex');
      throw ServiceTimeoutException();
    } catch (ex) {
      logger.e('listVehicle catch Exception $ex');
      throw VehicleException(MessageException.general);
    }
  }
}

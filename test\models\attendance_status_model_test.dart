import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('test to verify that all service statuses are correct', () {
    test('test status init attendance', () {
      expect(AttendanceStatus.INICIO == 1, isTrue);
    });

    test('test status regulation attendance', () {
      expect(AttendanceStatus.REGULACAO == 2, isTrue);
    });

    test('test status dispath attendance', () {
      expect(AttendanceStatus.DESPACHO == 3, isTrue);
    });

    test('test status base exit attendance', () {
      expect(AttendanceStatus.SAIDA_BASE == 4, isTrue);
    });

    test('test status arrival origin attendance', () {
      expect(AttendanceStatus.CHEGADA_ORIGEM == 5, isTrue);
    });

    test('test status origin output attendance', () {
      expect(AttendanceStatus.SAIDA_ORIGEM == 6, isTrue);
    });

    test('test status destination arrival attendance', () {
      expect(AttendanceStatus.CHEGADA_DESTINO == 7, isTrue);
    });

    test('test status output destination attendance', () {
      expect(AttendanceStatus.SAIDA_DESTINO == 8, isTrue);
    });
    test('test status arrival base attendance', () {
      expect(AttendanceStatus.CHEGADA_BASE == 9, isTrue);
    });

    test('test status closed attendance', () {
      expect(AttendanceStatus.ENCERRADO == 10, isTrue);
    });

    test('test status new attendance', () {
      expect(AttendanceStatus.NOVO_ATENDIMENTO == 11, isTrue);
    });

    test('test status canceled attendance', () {
      expect(AttendanceStatus.CANCELADO == 12, isTrue);
    });
  });

  group('test function statusApi2StatusApp return', () {
    test('test return status attendance steps and codStatus 4', () {
      expect(AttendanceStatus.statusApi2StatusApp(4),
          AttendanceTimelineStepsEnum.start.index);
    });

    test('test return status attendance steps and codStatus 5', () {
      expect(AttendanceStatus.statusApi2StatusApp(5),
          AttendanceTimelineStepsEnum.originArrival.index);
    });

    test('test return status attendance steps and codStatus 6', () {
      expect(AttendanceStatus.statusApi2StatusApp(6),
          AttendanceTimelineStepsEnum.originDeparture.index);
    });

    test('test return status attendance steps and codStatus 7', () {
      expect(AttendanceStatus.statusApi2StatusApp(7),
          AttendanceTimelineStepsEnum.destinationArrival.index);
    });

    test('test return status attendance steps and codStatus 8', () {
      expect(AttendanceStatus.statusApi2StatusApp(8),
          AttendanceTimelineStepsEnum.destinationDeparture.index);
    });

    test('test return status attendance steps and codStatus 9', () {
      expect(AttendanceStatus.statusApi2StatusApp(9),
          AttendanceTimelineStepsEnum.baseArrival.index);
    });

    test('test return status attendance steps and codStatus 10', () {
      expect(AttendanceStatus.statusApi2StatusApp(10),
          AttendanceTimelineStepsEnum.closure.index);
    });

    test('test return status attendance steps and codStatus 11', () {
      expect(AttendanceStatus.statusApi2StatusApp(11),
          AttendanceTimelineStepsEnum.newAttendance.index);
    });

    test('test return status attendance steps and codStatus 12', () {
      expect(AttendanceStatus.statusApi2StatusApp(12),
          AttendanceTimelineStepsEnum.canceled.index);
    });

    test(
        'test return status attendance steps and codStatus different from 4 to 12',
        () {
      expect(AttendanceStatus.statusApi2StatusApp(13), -1);
    });
  });

  group('test function statusApp2StatusApi return', () {
    test('test return status attendance steps and codStatusApp 1', () {
      expect(
          AttendanceStatus.statusApp2StatusApi(0), AttendanceStatus.SAIDA_BASE);
    });

    test('test return status attendance steps and codStatusApp 2', () {
      expect(AttendanceStatus.statusApp2StatusApi(1),
          AttendanceStatus.CHEGADA_ORIGEM);
    });

    test('test return status attendance steps and codStatusApp 3', () {
      expect(AttendanceStatus.statusApp2StatusApi(2),
          AttendanceStatus.SAIDA_ORIGEM);
    });

    test('test return status attendance steps and codStatusApp 4', () {
      expect(AttendanceStatus.statusApp2StatusApi(3),
          AttendanceStatus.CHEGADA_DESTINO);
    });

    test('test return status attendance steps and codStatusApp 5', () {
      expect(AttendanceStatus.statusApp2StatusApi(4),
          AttendanceStatus.SAIDA_DESTINO);
    });

    test('test return status attendance steps and codStatusApp 6', () {
      expect(AttendanceStatus.statusApp2StatusApi(5),
          AttendanceStatus.CHEGADA_BASE);
    });

    test('test return status attendance steps and codStatusApp 7', () {
      expect(
          AttendanceStatus.statusApp2StatusApi(6), AttendanceStatus.ENCERRADO);
    });

    test('test return status attendance steps and codStatusApp 8', () {
      expect(AttendanceStatus.statusApp2StatusApi(7),
          AttendanceStatus.NOVO_ATENDIMENTO);
    });

    test('test return status attendance steps and codStatusApp 9', () {
      expect(
          AttendanceStatus.statusApp2StatusApi(8), AttendanceStatus.CANCELADO);
    });

    test(
        'test return status attendance steps and codStatusApp different from 1 to 8',
        () {
      expect(AttendanceStatus.statusApp2StatusApi(9), 11);
    });
  });

  group('test verify init attendance (codStatus/statusAttendance)', () {
    test('test verify init attendance return true', () {
      expect(AttendanceStatus.verifyInitAttendance(3, 1), isTrue);
    });

    test('test verify init attendance return false', () {
      expect(AttendanceStatus.verifyInitAttendance(1, 3), isFalse);
    });

    test('test verify init attendance return false', () {
      expect(AttendanceStatus.verifyInitAttendance(0, 3), isTrue);
    });
  });
}

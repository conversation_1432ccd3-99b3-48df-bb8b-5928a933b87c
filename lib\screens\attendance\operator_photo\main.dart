import 'dart:async';
import 'dart:io';

import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/bloc/register_photo/photo_cubit.dart';
import 'package:ambulancia_app/bloc/register_photo/photo_state.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/photo.constants.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../bloc/conduct/conduct_cubit.dart';
import '../../../shared/i18n/i18n_helper.dart';

class CameraScreen extends StatefulWidget {
  final Function? onTakePhoto;
  final AttendanceModel attendanceModel;
  final String? recordType;
  final String reasonAnnexPhoto;
  final int maxQuality;
  final int maxHeight;

  CameraScreen({
    this.onTakePhoto,
    required this.attendanceModel,
    this.recordType,
    required this.reasonAnnexPhoto,
    this.maxQuality = 60,
    this.maxHeight = 720,
  });

  @override
  _CameraScreenState createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  final logger = UnimedLogger(className: "CameraScreen");
  CameraController? _cameraController;
  CameraDescription? _cameraFront;
  CameraDescription? _cameraBack;
  List<CameraDescription>? cameras;

  late bool isCameraFront;
  bool imagePickerIsOpen = false;
  String? photoPath;
  bool canPressButton = true;
  bool showAlertCadastrar = false;
  bool _initingCamera = false;
  bool inited = false;
  bool _closing = false;
  bool _loadingSendPhoto = false;
  final _baseTranslateConductForm = 'conductForm';

  double cameraButtonSize = 75;
  Color cameraButtonColor = AmbulanceColors.green;

  ResolutionPreset get _resolutionBasedOnBioData {
    if (widget.maxHeight <= 352) {
      return ResolutionPreset.low;
    } else if (widget.maxHeight <= 720) {
      return ResolutionPreset.medium;
    } else if (widget.maxQuality <= 1280) {
      return ResolutionPreset.high;
    } else if (widget.maxQuality <= 1920) {
      return ResolutionPreset.veryHigh;
    } else {
      return ResolutionPreset.ultraHigh;
    }
  }

  void _initCamera(BuildContext context, {bool front = true}) async {
    try {
      if (_initingCamera) return;
      setState(() => _initingCamera = true);

      cameras ??= await availableCameras();

      if (cameras!.isEmpty)
        throw UnimedException('Dispositivo não possui câmera.');

      cameras!.forEach((camera) {
        _getCameraBackFront(camera);
      });

      isCameraFront = front;
      CameraDescription camera =
          isCameraFront ? _cameraFront ?? cameras!.first : _cameraBack!;

      _cameraController = CameraController(
        camera,
        _resolutionBasedOnBioData,
        imageFormatGroup: ImageFormatGroup.jpeg,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      if (!mounted) {
        return;
      }
      await _cameraController!
          .lockCaptureOrientation(DeviceOrientation.portraitUp);
    } on UnimedException catch (e) {
      logger.e("Error on _initCamera UnimedException $e");

      Alert.open(context,
          title: "Alerta",
          text: '${e.message}',
          callbackClose: () => _close(context));
    } catch (e) {
      logger.e("Error on _initCamera Exception $e");

      Alert.open(context,
          title: "Alerta",
          text: 'Não foi possível acessar a câmera.',
          callbackClose: () => _close(context));
    } finally {
      Future.delayed(Duration(milliseconds: 250), () {
        setState(() => _initingCamera = false);
      });
    }
  }

  void _getCameraBackFront(CameraDescription camera) {
    if (camera.lensDirection == CameraLensDirection.back) {
      _cameraBack ??= camera;
    } else if (camera.lensDirection == CameraLensDirection.front) {
      _cameraFront ??= camera;
    }
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!inited) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _initCamera(context);
        inited = true;
      });
    }
    final waitOpenCamera =
        (_cameraController == null || !_cameraController!.value.isInitialized);

    if (waitOpenCamera) {
      return Scaffold(
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SpinKitThreeBounce(
              color: AmbulanceColors.green,
              size: 20,
            )
          ],
        )),
      );
    } else {
      return BlocListener<ConductCubit, ConductState>(
        listener: ((context, state) {
          if (state is SuccessSendConductState) {
            context.read<ConductCubit>().cleanListAttachment();
            _alertSucess(state.message);
          } else if (state is ErrorSendConductState) {
            _alertError(state.message);
          }
        }),
        child: Scaffold(
            backgroundColor: Colors.white,
            body: photoPath == null
                ? _cameraPreview(context)
                : _photoPreview(context)),
      );
    }
  }

  _alertSucess(text) {
    Alert.open(context,
        title: I18nHelper.translate(context, '$_baseTranslateConductForm.sent'),
        text: text, callbackClose: () {
      if (mounted) {
        Navigator.pop(context);
      }
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }

  _alertError(text) {
    Alert.open(
      context,
      textButtonClose:
          I18nHelper.translate(context, '$_baseTranslateConductForm.close'),
      title: I18nHelper.translate(context, '$_baseTranslateConductForm.alert'),
      text: text,
    );
  }

  void onTakePhoto(String filePath, BuildContext context) {
    BlocProvider.of<RegisterPhotoCubit>(context).savePhotoInAppDocuments(
        serviceNumber: widget.attendanceModel.numAtendimento,
        xfile: filePath,
        recordType: widget.recordType,
        reasonAnnexPhoto: widget.reasonAnnexPhoto);
  }

  void onTakePhotoOperator(String filePath, BuildContext context) {
    context.read<ConductCubit>().savePhotoOperator(
          serviceNumber: widget.attendanceModel.numAtendimento,
          pathFile: filePath,
        );
  }

  Widget _cameraPreview(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.topCenter,
      children: [
        CameraPreview(_cameraController!),
        SafeArea(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: const EdgeInsets.only(bottom: 0.0),
              height: 75,
              color: AmbulanceColors.green,
              padding: const EdgeInsets.all(12.0),
              child: _cameraOptions(context),
            ),
          ),
        )
      ],
    );
  }

  Widget _photoPreview(BuildContext context) {
    return BlocListener<ConductCubit, ConductState>(
      listener: (context, state) {
        if (state is LoadingSendConductState) {
          setState(() {
            _loadingSendPhoto = true;
          });
        } else {
          setState(() {
            _loadingSendPhoto = false;
          });
        }
      },
      child: BlocListener<RegisterPhotoCubit, RegisterPhotoState>(
        listenWhen: (previousState, state) {
          if (state is ErrorSendPhotoState) {
            Alert.open(context,
                title: 'Atenção', text: MessageException.general);
          } else if (state is SuccessSendPhoto) {
            if (widget.recordType == 'Avaliação Clínica') {
              context.read<ClinicEvaluationCubit>().sendClinicEvaluation(
                    numAtendimento: widget.attendanceModel.numAtendimento,
                  );
              Navigator.pop(context);
            }
          }
          return true;
        },
        listener: (context, state) {
          if (state is LoadingSendPhoto) {
            setState(() {
              _loadingSendPhoto = true;
            });
          } else {
            setState(() {
              _loadingSendPhoto = false;
            });
          }
        },
        child: Column(
          children: [
            Image.file(
              File(photoPath!),
              height: 500,
              width: 500,
            ),
            Container(
              padding: const EdgeInsets.all(24.0),
              child: _photoOptions(context),
            ),
            if (_loadingSendPhoto) ...[
              Text(
                "Enviando informações, aguarde...",
                style: TextStyle(fontSize: 24),
              ),
              SpinKitThreeBounce(
                size: 30,
                color: AmbulanceColors.green,
              )
            ],
          ],
        ),
      ),
    );
  }

  Widget _cameraOptions(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          flex: 1,
          child: _buttonClose(context),
        ),
        Expanded(
          flex: 1,
          child: _buttonTakePicture(context),
        ),
        Expanded(flex: 1, child: _buttonChangeCameraOrientation(context)),
      ],
    );
  }

  Widget _photoOptions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: _loadingSendPhoto
              ? null
              : () {
                  Future.delayed(Duration(milliseconds: 250), () {
                    setState(() {
                      photoPath = null;
                    });
                  });
                },
          child: Icon(
            Icons.cached,
            color: _loadingSendPhoto
                ? AmbulanceColors.grayDark
                : AmbulanceColors.greenLight3,
            size: 32,
          ),
        ),
        SizedBox(
          width: 20,
        ),
        FloatingActionButton(
          backgroundColor: _loadingSendPhoto
              ? AmbulanceColors.grayDark
              : AmbulanceColors.greenLight3,
          onPressed: _closing || _loadingSendPhoto
              ? null
              : () {
                  if (widget.reasonAnnexPhoto ==
                      photoReasonAnnexEnum.conduta.value) {
                    onTakePhotoOperator(File(photoPath!).path, context);
                  } else {
                    onTakePhoto(File(photoPath!).path, context);
                  }
                },
          child: Icon(
            Icons.check,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buttonTakePicture(BuildContext context) {
    return InkWell(
      key: Key('button_take_picture'),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 100),
        width: cameraButtonSize,
        height: cameraButtonSize,
        padding: EdgeInsets.all(3),
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 3)),
        child: Container(
          decoration: BoxDecoration(
            color: cameraButtonColor,
            shape: BoxShape.circle,
          ),
        ),
      ),
      onTap: !canPressButton
          ? null
          : () async {
              setState(() {
                cameraButtonSize = 85;
                cameraButtonColor = Colors.red;
              });

              await _takePhoto(context);

              setState(() {
                cameraButtonSize = 75;
                cameraButtonColor = AmbulanceColors.green;
              });
            },
    );
  }

  Future<void> _takePhoto(BuildContext context) async {
    try {
      if (canPressButton) {
        setState(() {
          canPressButton = false;
        });

        if (!_cameraController!.value.isInitialized) {
          await _cameraController!.initialize();
        }

        final XFile file = await _cameraController!.takePicture();

        File? compressedFile = await _compressAndGetFile(File(file.path));
        //wait to save file
        await Future.delayed(Duration(milliseconds: 250));
        setState(() {
          photoPath = compressedFile!.path;
        });
      }
    } catch (e) {
      _initCamera(context);
      logger.e("Error on _takePhoto $e");
      Alert.open(context,
          title: 'Anexar Foto',
          text: 'Erro ao anexar foto. Tente novamente',
          callbackClose: () {});
    } finally {
      setState(() {
        canPressButton = true;
      });
    }
  }

  Widget _buttonChangeCameraOrientation(BuildContext context) {
    return TextButton(
      onPressed: _initingCamera || !canPressButton
          ? null
          : () {
              _initCamera(context, front: !isCameraFront);
            },
      child: Icon(
        Icons.flip_camera_ios_outlined,
        color: Colors.white,
        size: 32,
      ),
    );
  }

  Widget _buttonClose(BuildContext context) {
    return Platform.isAndroid
        ? Container()
        : TextButton(
            child: FittedBox(
              child: Text(
                "Cancelar",
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            onPressed: () => _close(context));
  }

  void _close(BuildContext context) {
    if (!_closing && Navigator.of(context).canPop()) {
      _closing = true;
      Navigator.of(context).pop();
    }
  }

  Future<File?> _compressAndGetFile(File file) async {
    try {
      final filePath = file.absolute.path;
      final lastIndex = filePath.lastIndexOf(RegExp(r'.jp'));
      final splitted = filePath.substring(0, (lastIndex));
      final targetPath = "${splitted}_out${filePath.substring(lastIndex)}";
      final result = await FlutterImageCompress.compressAndGetFile(
        filePath,
        targetPath,
        quality: widget.maxQuality,
      );

      return File(result!.path);
    } catch (e) {
      return file;
    }
  }
}

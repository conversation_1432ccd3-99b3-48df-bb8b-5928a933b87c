import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:flutter/material.dart';

class UserDocument extends StatelessWidget {
  final AttendanceModel attendanceModel;
  const UserDocument({Key? key, required this.attendanceModel})
      : super(key: key);
  final _baseTranslate = 'attendanceScreen';
  @override
  Widget build(BuildContext context) {
    if (attendanceModel.codigoCarteira != null)
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(I18nHelper.translate(context, '$_baseTranslate.wallet'),
              style: TextStyle(fontSize: 14)),
          Text(attendanceModel.carteiraNumero, style: TextStyle(fontSize: 16)),
        ],
      );
    else if (attendanceModel.cpf != null && attendanceModel.cpf!.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('CPF', style: TextStyle(fontSize: 14)),
          Text(attendanceModel.cpf!, style: TextStyle(fontSize: 16)),
        ],
      );
    } else
      return Container();
  }
}

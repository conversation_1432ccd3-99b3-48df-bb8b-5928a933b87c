import 'dart:convert';

import 'package:ambulancia_app/models/version-remote.model.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';

class RemoteConfigService {
  final logger = UnimedLogger(className: 'RemoteConfigService');

  late FirebaseRemoteConfig _remoteConfig;
  Map<String, dynamic>? _value;
  VersionRemoteModel? _versionRemoteModel;

  Duration timeExpiration = Duration(seconds: 30);

  Map<String, dynamic>? get value => _value;
  VersionRemoteModel? get version => _versionRemoteModel;

  RemoteConfigService() {
    if (!(const String.fromEnvironment('environment') == "TEST")) {
      _remoteConfig = FirebaseRemoteConfig.instance;
      _remoteConfig
          .setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: timeExpiration,
          minimumFetchInterval: const Duration(minutes: 5),
        ),
      )
          .then((onValue) {
        _remoteConfig.fetchAndActivate().then((onValue) {
          logger.i('\n fetchAndActivate - ($onValue)\n');
          final env = const String.fromEnvironment('environment');

          final valueString = _remoteConfig.getString('VERSION_$env');
          _value = jsonDecode(valueString);
        }).onError((error, stackTrace) {
          logger.e('\n RemoteConfigService Internal Erro - ($error)');
        });
      });
    }
  }

  Future<VersionRemoteModel?> forceFetchValue() async {
    try {
      await _remoteConfig.fetchAndActivate();

      final env = const String.fromEnvironment('environment');
      final valueString = _remoteConfig.getString('VERSION_$env');
      logger.i('VERSION_$env: $valueString');
      _value = jsonDecode(valueString);
      _versionRemoteModel =
          VersionRemoteModel.fromJson(jsonDecode(valueString));
      print("MODEL: ${_versionRemoteModel!.buildNumber}");
      return _versionRemoteModel;
    } catch (ex) {
      print(ex);
      return null;
    }
  }
}

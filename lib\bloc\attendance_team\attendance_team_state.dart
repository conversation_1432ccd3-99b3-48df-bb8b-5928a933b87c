part of 'attendance_team_cubit.dart';

abstract class AttendanceTeamState extends Equatable {
  const AttendanceTeamState();

  @override
  List<Object> get props => [];
}

class AttendanceTeamInitial extends AttendanceTeamState {}

class LoadingAttendanceTeamState extends AttendanceTeamState {}

class UpdatingAttendanceTeamState extends AttendanceTeamState {}

class LoadedAttendanceTeamState extends AttendanceTeamState {
  final FuncoesModel? availableDoctors, availableNurses;
  LoadedAttendanceTeamState({this.availableDoctors, this.availableNurses});
}

class UpdatedAttendanceTeamState extends AttendanceTeamState {
  final FuncoesModel? availableDoctors, availableNurses;
  final lastcodDoctor, lastCodNurse;

  UpdatedAttendanceTeamState(
      {this.lastcodDoctor,
      this.lastCodNurse,
      this.availableDoctors,
      this.availableNurses});
}

class ErrorAttendanceTeamState extends AttendanceTeamState {
  final messsage;
  ErrorAttendanceTeamState(this.messsage);
}

class ErrorUpdatingAttendanceTeamState extends AttendanceTeamState {
  final messsage;
  ErrorUpdatingAttendanceTeamState(this.messsage);
}

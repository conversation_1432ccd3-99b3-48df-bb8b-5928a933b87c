import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-text.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/shared/formatters.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/toggle/toggle.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CentralNervousSystem extends StatefulWidget {
  final backgroundColor;
  final textColor;
  final ResponseClinicEvaluation clinicEvaluation;
  final emmitChangedForParent;
  CentralNervousSystem(
      {Key? key,
      required this.clinicEvaluation,
      this.backgroundColor = AmbulanceColors.beige,
      this.textColor = AmbulanceColors.greenDark,
      this.emmitChangedForParent})
      : super(key: key);

  @override
  _CentralNervousSystemState createState() => _CentralNervousSystemState();
}

class _CentralNervousSystemState extends State<CentralNervousSystem> {
  bool showLaterizationSigns = false;
  bool showpupils = false;
  Activity? glasgowObject;
  late List<bool> checkValues;
  TextEditingController textGlasgow = TextEditingController();
  final _baseTranslate = 'evaluationClinicalForm.centralNervousSystem';
  @override
  void initState() {
    super.initState();
    checkValues =
        List<bool>.filled(widget.clinicEvaluation.pupilas.length, false);

    final requestClinicEvaluation =
        BlocProvider.of<ClinicEvaluationCubit>(context)
            .requestClinicEvaluation!;

    glasgowObject = widget.clinicEvaluation.glasgow.firstWhereOrNull(
        (element) =>
            element.codigo == requestClinicEvaluation.glasgow.toString());

    textGlasgow.text = requestClinicEvaluation.glasgow?.toString() ?? '';
  }

  bool checkCurrentValueIput(int index) {
    return checkValues[index];
  }

  @override
  Widget build(BuildContext context) {
    return ClincalCard(
      title: ' ${I18nHelper.translate(context, '$_baseTranslate.title')}',
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  I18nHelper.translate(context, '$_baseTranslate.subTitle'),
                  style: TextStyle(
                    color: widget.textColor,
                  ),
                ),
                GridView.builder(
                  primary: false,
                  shrinkWrap: true,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.8,
                  ),
                  itemCount: widget.clinicEvaluation.pupilas.length,
                  itemBuilder: (context, index) {
                    return ClinicalInputCheck(
                      itemIndex: index,
                      checkParentValue: checkCurrentValueIput,
                      label: widget.clinicEvaluation.pupilas[index].descricao,
                      labelColor: widget.textColor,
                      checkColor: widget.textColor,
                      forceValue:
                          widget.clinicEvaluation.pupilas[index].codigo ==
                              BlocProvider.of<ClinicEvaluationCubit>(context)
                                  .requestClinicEvaluation!
                                  .pupilas,
                      onPresss: (isChecked) {
                        setState(() {
                          for (int i = 0;
                              i < widget.clinicEvaluation.pupilas.length;
                              ++i) checkValues[i] = false;

                          checkValues[index] = isChecked!;

                          BlocProvider.of<ClinicEvaluationCubit>(context)
                                  .requestClinicEvaluation!
                                  .pupilas =
                              isChecked
                                  ? widget
                                      .clinicEvaluation.pupilas[index].codigo
                                  : null;
                        });
                        widget.emmitChangedForParent();
                      },
                    );
                  },
                ),
              ],
            ),
          ),
          SizedBox(width: 10),
          Flexible(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClinicalInputText(
                    title: I18nHelper.translate(
                        context, '$_baseTranslate.glasgow'),
                    textColor: widget.textColor,
                    controller: textGlasgow,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      ...FormatterField.simpleNumberFormatter,
                      LengthLimitingTextInputFormatter(2),
                    ],
                    onChange: (value) {
                      final code = int.tryParse(value);
                      BlocProvider.of<ClinicEvaluationCubit>(context)
                          .requestClinicEvaluation!
                          .glasgow = code;

                      setState(() => glasgowObject =
                              widget.clinicEvaluation.glasgow.firstWhereOrNull(
                            (element) => element.codigo == code.toString(),
                          ));
                      widget.emmitChangedForParent();
                    }),
                Padding(
                  padding: const EdgeInsets.only(left: 2, top: 2),
                  child: Text(
                    glasgowObject?.descricao ??
                        I18nHelper.translate(
                            context, '$_baseTranslate.requiredCaption'),
                    style: TextStyle(
                      color:
                          glasgowObject == null ? Colors.red : widget.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
              ],
            ),
          ),
          Flexible(
            flex: 1,
            child: AmbulanceToggle(
              label: I18nHelper.translate(
                  context, '$_baseTranslate.laterizationSigns'),
              buttonColor: widget.textColor,
              inactiveTrackColor: Colors.white,
              activeTrackColor: Colors.white,
              forceValue: BlocProvider.of<ClinicEvaluationCubit>(context)
                      .requestClinicEvaluation!
                      .sinaisLaterizacao ==
                  'S',
              onChanged: (value) {
                setState(() {
                  BlocProvider.of<ClinicEvaluationCubit>(context)
                      .requestClinicEvaluation!
                      .sinaisLaterizacao = value! ? 'S' : 'N';
                });
                widget.emmitChangedForParent();
              },
            ),
          ),
        ],
      ),
    );
  }
}

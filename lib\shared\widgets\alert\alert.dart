import 'package:flutter/material.dart';

import '../../../theme/colors.dart';

class Alert {
  static void open(
    BuildContext context, {
    String title = '',
    Widget? widgetTitle,
    String text = '',
    String textButtonClose = 'Fechar',
    Color colorButtonClose = AmbulanceColors.redClose,
    List<String>? lines,
    List<Widget>? actions,
    Function? callbackClose,
    bool barrierDismissible = false,
  }) async {
    List<Widget> _texts = [
      Text(
        text,
        style: const TextStyle(
            color: AmbulanceColors.grayDark2, fontWeight: FontWeight.bold),
      )
    ];
    List<Widget> _actions = [];
    if (text.isNotEmpty && lines != null && lines.isNotEmpty) {
      _texts.addAll(lines.map((l) => Text(
            l,
            style: TextStyle(
              color: unimedGreen.shade900,
            ),
          )));
    }

    if (actions != null && actions.isNotEmpty) {
      _actions = actions;
    }

    bool canClickButtonClose = true;
    _actions.add(
      ElevatedButton(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: colorButtonClose,
        ),
        child: Text(textButtonClose),
        onPressed: () {
          if (canClickButtonClose) {
            canClickButtonClose = false;
            Navigator.of(context).pop();

            if (callbackClose != null) {
              callbackClose();
            }
          }
        },
      ),
    );

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return PopScope(
          canPop: barrierDismissible,
          child: Transform(
            transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                backgroundColor: unimedGreen.shade50,
                title: widgetTitle ??
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: unimedGreen.shade900,
                      ),
                    ),
                content: SingleChildScrollView(
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: _texts,
                  ),
                ),
                actions: _actions,
              ),
            ),
          ),
        );
      },
    );
  }

  static void openAlternativeLayout(
    BuildContext context, {
    Widget? title,
    String text = '',
    String textButtonClose = 'Fechar',
    List<String>? lines,
    Widget? action,
    Function? callbackClose,
    bool barrierDismissible = false,
  }) async {
    List<Widget> _texts = [
      Text(
        text,
        textAlign: TextAlign.center,
        style: const TextStyle(color: AmbulanceColors.grayDark2),
      )
    ];
    List<Widget> _actions = [];
    if (text.isNotEmpty && lines != null && lines.isNotEmpty) {
      _texts.addAll(lines.map((l) => Text(
            l,
            style: TextStyle(
              color: unimedGreen.shade900,
            ),
          )));
    }

    if (action != null) {
      _actions.add(action);
    }
    _actions.add(const SizedBox(
      width: 8,
    ));
    _actions.add(
      Expanded(
          child: ElevatedButton(
        style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: Colors.red /* UnimedColors.redCancel, */
            ),
        child: Text(textButtonClose),
        onPressed: () {
          Navigator.of(context).pop();

          if (callbackClose != null) {
            callbackClose();
          }
        },
      )),
    );

    List<Widget> rowActions = [];
    rowActions.add(Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(children: _actions)));

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return PopScope(
          canPop: barrierDismissible,
          child: Transform(
            transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
            child: Opacity(
              opacity: a1.value,
              child: AlertDialog(
                //backgroundColor: unimedGreen.shade50,
                title: title,
                content: SingleChildScrollView(
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    children: _texts,
                  ),
                ),
                actions: rowActions,
              ),
            ),
          ),
        );
      },
    );
  }
}

class UnimedAlertDialog extends StatelessWidget {
  const UnimedAlertDialog(
      {Key? key,
      required this.onPressed,
      required this.textWidget,
      this.title,
      this.iconData = Icons.info_outline,
      this.textButton = "Ok",
      this.colorIcon = AmbulanceColors.greenDark})
      : super(key: key);

  final IconData iconData;
  final VoidCallback onPressed;
  final Text textWidget;
  final String textButton;
  final Color colorIcon;
  final Widget? title;

  @override
  Widget build(context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: Center(
          child: Column(
        children: [
          Icon(
            iconData,
            size: 70.0,
            color: colorIcon,
          ),
          title ?? Container(),
        ],
      )),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            textWidget,
          ],
        ),
      ),
      actionsAlignment: MainAxisAlignment.center,
      actions: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: AmbulanceColors.orange,
          ),
          child: Text(textButton),
          onPressed: onPressed,
        ),
      ],
      backgroundColor: Colors.white,
    );
  }
}

alertButton(
    {BuildContext? context,
    required String text,
    Color? color,
    Function? onClick}) {
  return SizedBox(
    height: 45,
    child: Material(
      child: InkWell(
        onTap: onClick as void Function()?,
        child: Ink(
          decoration: BoxDecoration(
            color: color,
            borderRadius: const BorderRadius.all(
              Radius.circular(8.0),
            ),
          ),
          child: Center(
            child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Text(
                  text,
                  style: const TextStyle(color: Colors.white),
                  textAlign: TextAlign.center,
                )),
          ),
        ),
      ),
    ),
  );
}

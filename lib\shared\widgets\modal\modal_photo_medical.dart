import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/conduct/conduct_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:ambulancia_app/screens/attendance/medical_photo/main.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

void openModalPhotoMedical({
  required BuildContext context,
  required AttendanceModel attendanceModel,
  required String recordType,
  String? codTypeReclassify,
  bool reclassify = false,
  int? codProtocoloDoenca,
  String? obsProtocoloDoenca,
  CloseAttendanceV2Model? closeAttendanceV2,
}) {
  const String _fontFamily = 'Icomoon';

  const IconData cancel_circle = IconData(0xea0d, fontFamily: _fontFamily);
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      int maxHeight = context
          .read<AuthCubit>()
          .configAppAmbulanciaConstants
          .imageQuality
          .maxHeightDefault;

      int maxQuality = context
          .read<AuthCubit>()
          .configAppAmbulanciaConstants
          .imageQuality
          .maxQualityDefault;
      return BlocBuilder<AttendanceMovementsCubit, AttendanceMovementsState>(
        builder: (context, stateAtendace) {
          return BlocBuilder<ConductCubit, ConductState>(
            builder: (context, stateConduct) {
              return PopScope(
                canPop: stateAtendace is UpdatingMovementsState ||
                        stateConduct is LoadingSendConductClosureState
                    ? false
                    : true,
                child: AbsorbPointer(
                  absorbing: stateAtendace is UpdatingMovementsState ||
                          stateConduct is LoadingSendConductClosureState
                      ? true
                      : false,
                  child: AlertDialog(
                    title: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text('Identificação do médico necessária:  '),
                        IconButton(
                            iconSize: 38,
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              cancel_circle,
                              color: AmbulanceColors.green,
                            ))
                      ],
                    ),
                    shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(ConstantsTheme.borderRadius)),
                    insetPadding: EdgeInsets.only(
                      top: ConstantsTheme.doublePadding * 3,
                      bottom: ConstantsTheme.doublePadding,
                      left: ConstantsTheme.doublePadding,
                      right: ConstantsTheme.doublePadding,
                    ),
                    content: Container(
                      width: MediaQuery.of(context).size.width * 0.75,
                      child: MedicalPhoto(
                        attendanceModel: attendanceModel,
                        recordType: recordType,
                        codTypeReclassify: codTypeReclassify,
                        reclassify: reclassify,
                        maxHeight: maxHeight,
                        maxQuality: maxQuality,
                        codProtocoloDoenca: codProtocoloDoenca,
                        obsProtocoloDoenca: obsProtocoloDoenca,
                        closeAttendanceData: closeAttendanceV2,
                      ),
                      height: MediaQuery.of(context).size.width * 0.85,
                    ),
                  ),
                ),
              );
            },
          );
        },
      );
    },
  );
}

import 'package:ambulancia_app/models/destiny_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  DestinyModel? testDestinyModel;
  List? jsonListTestDestinyModel;
  setUpAll(
    () {
      jsonListTestDestinyModel = [
        {
          "codUnimed": 1,
          "codigoDestinoPaciente": "destino",
          "nomeDestinoPaciente": "hospital unimed"
        },
        {
          "codUnimed": 1,
          "codigoDestinoPaciente": "destino",
          "nomeDestinoPaciente": "hospital unimed"
        }
      ];
      testDestinyModel = DestinyModel(jsonListTestDestinyModel!);
    },
  );

  group(
    "isInstanceOf DestinyModel model tests",
    () {
      test("Should be return instance of DestinyModel", () {
        expect(testDestinyModel, isInstanceOf<DestinyModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of DestinyObject to json", () {
      expect(testDestinyModel!.destinies[0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of DestinyObject from json", () {
      expect(DestinyObject.fromJson(jsonListTestDestinyModel![0]),
          isInstanceOf<DestinyObject>());
    });
  });

  group(
    "isInstanceOf DestinyModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonListTestDestinyModel, isInstanceOf<List>());
        expect(jsonListTestDestinyModel![0]["codUnimed"], isInstanceOf<int>());
        expect(jsonListTestDestinyModel![0]["codigoDestinoPaciente"],
            isInstanceOf<String>());
        expect(jsonListTestDestinyModel![0]["nomeDestinoPaciente"],
            isInstanceOf<String>());
      });
      test("Can´t return if is null", () {
        expect(jsonListTestDestinyModel![0]["codUnimed"] == null, false);
        expect(jsonListTestDestinyModel![0]["codigoDestinoPaciente"] == null,
            false);
        expect(
            jsonListTestDestinyModel![0]["nomeDestinoPaciente"] == null, false);
      });

      test("builder", () {
        final model = DestinyObject(
            codUnimed: jsonListTestDestinyModel![0]["codUnimed"],
            codigoDestinoPaciente: jsonListTestDestinyModel![0]
                ["codigoDestinoPaciente"],
            nomeDestinoPaciente: jsonListTestDestinyModel![0]
                ["nomeDestinoPaciente"]);
        expect(model.codUnimed, isInstanceOf<int>());
      });
    },
  );
}

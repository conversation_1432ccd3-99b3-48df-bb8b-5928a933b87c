import 'dart:async';

import 'package:ambulancia_app/models/version-remote.model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/api/vo/version-validate.vo.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/services/remote-config.service.dart';
import 'package:ambulancia_app/shared/services/version.service.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'update_version_state.dart';

class UpdateVersionCubit extends Cubit<UpdateVersionState> {
  UpdateVersionCubit() : super(UpdateVersionInitial());

  checkVersionEvent() async {
    emit(CheckingVersionState());
    final versionInfo =
        await (Locator.instance.get<VersionService>().getInfo());
    final localVersion = const String.fromEnvironment('environment') == "PROD"
        ? '${versionInfo!.version}'
        : '${versionInfo!.version}+${versionInfo.buildNumber}';

    final VersionRemoteModel configs = await (Locator.instance
        .get<RemoteConfigService>()
        .forceFetchValue()) as VersionRemoteModel;

    // final remoteVersion = FlavorConfig.isProduction()
    //     ? '${configs.versionNumber}'
    //     : '${configs.versionNumber}+${configs.buildNumber}';

    final isOutOfDate =
        await (_isOutOfDate(versionInfo: versionInfo, configs: configs));

    emit(isOutOfDate!.isOutOfDate!
        ? OutOfDateState(
            localVersion: localVersion,
            remoteVersion: isOutOfDate.lastVersion,
            forceUpdate: isOutOfDate.force)
        : UpdatedState());
  }

  Future<VersionValidateResponse?> _isOutOfDate(
      {required VersionInfo versionInfo,
      required VersionRemoteModel configs}) async {
    //final bNumberServer = configs.buildNumber;
    final bNumberLocal = int.parse(versionInfo.buildNumber);
    debugPrint("===== SERVER: ${configs.buildNumber}");
    debugPrint("======= LOCAL: ${versionInfo.buildNumber}");
    final versionApi = await Locator.instance
        .get<AuthApi>()
        .getCheckVersion(versionInfo.version, bNumberLocal);
    return versionApi;
  }
}

import 'package:flutter_test/flutter_test.dart';



Future<Null> permissionHandle(WidgetTester tester) async {
  await tester.pumpAndSettle(Duration(microseconds: 200));
  await Future.delayed(Duration(milliseconds: 1200));
  await tester.pumpAndSettle(Duration(microseconds: 200));

  final Finder permissionCamera = find.text('PERMITIR O USO DA CÂMERA');
  await tester.tap(permissionCamera);
  await tester.pumpAndSettle();

  final Finder permissionLocation = find.text('PERMITIR O USO DA LOCALIZAÇÃO GPS');
  await tester.tap(permissionLocation);
  await tester.pumpAndSettle();

  final Finder permissionStorage = find.text('PERMITIR O USO DO ARMAZENAMENTO INTERNO');
  await tester.tap(permissionStorage);
  await tester.pumpAndSettle();
}

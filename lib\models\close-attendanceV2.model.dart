class CloseAttendanceV2Model {
  final bool reclassify;
  final String? codTypeReclassify;
  final int? delayReasonCode;
  final String? delayReasonObservation;
  final int? codProtocoloDoenca;
  final String? obsProtocoloDoenca;

  final String preHospitalDischargeObs;
  final String preHospDischargeDateTime;
  final String? preHospDischargeReceivingDoctor;
  final String preHospitalDischargeDiagnosis;
  final int preHospDischargeReasonCode;
  final String? otherPreHospDischargeReason;
  final int preHospDischargeClinicalPictureCode;
  final String? newPreHospDischargeClinicalPicture;
  final String? recordType;
  final String numAtendimento;
  final String? imagemBase64;

  CloseAttendanceV2Model({
    required this.reclassify,
    this.codTypeReclassify,
    this.delayReasonCode,
    this.delayReasonObservation,
    this.codProtocoloDoenca,
    this.obsProtocoloDoenca,
    required this.preHospitalDischargeObs,
    required this.preHospDischargeDateTime,
    this.preHospDischargeReceivingDoctor,
    required this.preHospitalDischargeDiagnosis,
    required this.preHospDischargeReasonCode,
    this.otherPreHospDischargeReason,
    required this.preHospDischargeClinicalPictureCode,
    this.newPreHospDischargeClinicalPicture,
    this.recordType,
    required this.numAtendimento,
    this.imagemBase64,
  });

  Map<String, dynamic> toJson() {
    return {
      "reclassify": reclassify,
      if (reclassify) "codTypeReclassify": codTypeReclassify,
      "delayReasonCode": delayReasonCode,
      "delayReasonObservation":
          delayReasonCode != null ? delayReasonObservation : null,
      if (codProtocoloDoenca != null) "codProtocolSignal": codProtocoloDoenca,
      if (obsProtocoloDoenca != null && obsProtocoloDoenca!.isNotEmpty)
        "obsSignsOfIllness": obsProtocoloDoenca,
      "preHospitalDischargeObs": "$preHospitalDischargeObs",
      "preHospDischargeDateTime": "$preHospDischargeDateTime",
      "preHospDischargeReceivingDoctor": "$preHospDischargeReceivingDoctor",
      "preHospitalDischargeDiagnosis": "$preHospitalDischargeDiagnosis",
      "preHospDischargeReasonCode": preHospDischargeReasonCode,
      "otherPreHospDischargeReason": otherPreHospDischargeReason,
      "preHospDischargeClinicalPictureCode":
          preHospDischargeClinicalPictureCode,
      "newPreHospDischargeClinicalPicture": newPreHospDischargeClinicalPicture,
      "image": {
        "fileName": _generateFileName(
            recordType: recordType ?? "", serviceNumber: numAtendimento),
        "fileBase64": imagemBase64,
      }
    };
  }

  CloseAttendanceV2Model copyWith({
    bool? reclassify,
    String? codTypeReclassify,
    int? delayReasonCode,
    String? delayReasonObservation,
    int? codProtocoloDoenca,
    String? obsProtocoloDoenca,
    String? preHospitalDischargeObs,
    String? preHospDischargeDateTime,
    String? preHospDischargeReceivingDoctor,
    String? preHospitalDischargeDiagnosis,
    int? preHospDischargeReasonCode,
    String? otherPreHospDischargeReason,
    int? preHospDischargeClinicalPictureCode,
    String? newPreHospDischargeClinicalPicture,
    String? recordType,
    String? numAtendimento,
    String? imagemBase64,
  }) {
    return CloseAttendanceV2Model(
      reclassify: reclassify ?? this.reclassify,
      codTypeReclassify: codTypeReclassify ?? this.codTypeReclassify,
      delayReasonCode: delayReasonCode ?? this.delayReasonCode,
      delayReasonObservation:
          delayReasonObservation ?? this.delayReasonObservation,
      codProtocoloDoenca: codProtocoloDoenca ?? this.codProtocoloDoenca,
      obsProtocoloDoenca: obsProtocoloDoenca ?? this.obsProtocoloDoenca,
      preHospitalDischargeObs:
          preHospitalDischargeObs ?? this.preHospitalDischargeObs,
      preHospDischargeDateTime:
          preHospDischargeDateTime ?? this.preHospDischargeDateTime,
      preHospDischargeReceivingDoctor: preHospDischargeReceivingDoctor ??
          this.preHospDischargeReceivingDoctor,
      preHospitalDischargeDiagnosis:
          preHospitalDischargeDiagnosis ?? this.preHospitalDischargeDiagnosis,
      preHospDischargeReasonCode:
          preHospDischargeReasonCode ?? this.preHospDischargeReasonCode,
      otherPreHospDischargeReason:
          otherPreHospDischargeReason ?? this.otherPreHospDischargeReason,
      preHospDischargeClinicalPictureCode:
          preHospDischargeClinicalPictureCode ??
              this.preHospDischargeClinicalPictureCode,
      newPreHospDischargeClinicalPicture: newPreHospDischargeClinicalPicture ??
          this.newPreHospDischargeClinicalPicture,
      recordType: recordType ?? this.recordType,
      numAtendimento: numAtendimento ?? this.numAtendimento,
      imagemBase64: imagemBase64 ?? this.imagemBase64,
    );
  }

  String _generateFileName({
    required String recordType,
    required String serviceNumber,
  }) {
    switch (recordType) {
      case 'Avaliação Clínica':
        return 'ac_$serviceNumber.png';
      case 'Conduta':
        return 'c_$serviceNumber.png';
      case 'Encerramento':
        return 'en_$serviceNumber.png';
      default:
        return '';
    }
  }
}

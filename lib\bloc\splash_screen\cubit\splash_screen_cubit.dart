import 'package:ambulancia_app/shared/api/conduct.api.dart';
import 'package:ambulancia_app/shared/enviroments.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'splash_screen_state.dart';

class SplashScreenCubit extends Cubit<SplashScreenState> {
  SplashScreenCubit() : super(SplashScreenStateInitial());

  Future<void> verifyContinuedAplication(
      {required BuildContext context}) async {
    try {
      await Locator.instance.get<ConductApi>().loadCIDFromCSV();

      if (Enviroments.VERIFY.toUpperCase() != 'OFF') {
        if (_verifyTablet(context: context)) {
          emit(ContinuedStartupSplashScreen());
        } else {
          emit(InterruptedStartupSplashScreen(
              message:
                  'Infelizmente seu dispositivo não é compatível com a aplicação, tente executar em um Tablet'));
        }
      } else {
        emit(ContinuedStartupSplashScreen());
      }
    } catch (e) {
      emit(InterruptedStartupSplashScreen(message: '$e'));
    }
  }

  bool _verifyTablet({required BuildContext context}) {
    double shortestSide = MediaQuery.of(context).size.shortestSide;
    bool isMobile = shortestSide < 600;
    return !isMobile;
  }
}

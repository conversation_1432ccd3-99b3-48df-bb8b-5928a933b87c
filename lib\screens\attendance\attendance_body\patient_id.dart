import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/user-document.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';

class PatientId extends StatelessWidget {
  final AttendanceModel attendance;
  final _baseTranslate = 'attendanceScreen';
  PatientId({required this.attendance});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ConstantsTheme.padding),
      decoration: BoxDecoration(
        color: AmbulanceColors.grayLight3,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(ConstantsTheme.borderRadius),
          topRight: Radius.circular(ConstantsTheme.borderRadius),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _itemText(
                label: I18nHelper.translate(context, '$_baseTranslate.name'),
                value: attendance.nome),
          ),
          SizedBox(width: ConstantsTheme.doublePadding),
          UserDocument(attendanceModel: attendance),
          SizedBox(width: ConstantsTheme.doublePadding),
          _ageWidget(context)
        ],
      ),
    );
  }

  Widget _ageWidget(context) {
    return Container(
      padding: EdgeInsets.symmetric(
          vertical: ConstantsTheme.padding,
          horizontal: ConstantsTheme.doublePadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(ConstantsTheme.borderRadius),
          topLeft: Radius.circular(ConstantsTheme.borderRadius),
          topRight: Radius.circular(ConstantsTheme.borderRadius),
        ),
        border: Border.all(
          color: AmbulanceColors.pinkChart,
          width: 1.5,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outlined,
            color: AmbulanceColors.pinkChart,
            size: 35,
          ),
          SizedBox(width: ConstantsTheme.padding),
          Column(
            children: [
              Text(
                I18nHelper.translate(context, '$_baseTranslate.age'),
                style: TextStyle(
                  fontSize: 11,
                  color: AmbulanceColors.pinkChart,
                ),
              ),
              Text(
                attendance.idade.toString(),
                style: TextStyle(
                  fontSize: 22,
                  color: AmbulanceColors.pinkChart,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  _itemText({required String label, required String value}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 16),
        ),
        Text(
          value,
          style: TextStyle(fontSize: 16),
        ),
      ],
    );
  }
}

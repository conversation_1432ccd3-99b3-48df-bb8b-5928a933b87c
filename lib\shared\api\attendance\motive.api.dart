import 'dart:convert';

import 'package:ambulancia_app/models/motive.model.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:http/http.dart' as http;

class MotiveApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'MotiveApi');

  MotiveApi(this.httpClient);

  Future<List<MotiveModel>> getMotivesCancel() async {
    //TODO código unimed inserido diretamente pois nao possuímos ele como parametro na entrada
    final endpoint = '/unimedurgente/motivo-nao-atendimento/unimed/63';
    final url = '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb()
    };
    try {
      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);
        logger.d('getMotivesCancel retorno : $bodyRetorno');
        return (bodyRetorno as List)
            .map<MotiveModel>((sa) => MotiveModel.fromJson(sa))
            .toList();
      } else {
        logger.e('getMotivesCancel found');
        throw MotiveException(MessageException.general);
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e(
          'getMotivesCancel - Error AttendanceApi ServiceTimeoutException $ex');
      throw ServiceTimeoutException();
    } on MotiveException catch (ex) {
      logger.i('getMotivesCancel AutorizacoesException ${ex.message}');
      throw MotiveException(MessageException.general);
    } on NoInternetException catch (ex) {
      logger.i('getMotivesCancel NoInternetException ${ex.message}');
      throw NoInternetException();
    } on NotFoundException catch (ex) {
      logger.i('getMotivesCancel NotFoundException ${ex.message}');
      throw NotFoundException();
    } catch (ex) {
      logger.i('getMotivesCancel exception $ex');
      throw MotiveException(MessageException.general);
    }
  }
}

import 'package:ambulancia_app/models/vehicle_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'user_model.g.dart';

@JsonSerializable(explicitToJson: true)
class UserCredentials {
  String? user;
  String? password;
  UserCredentials({this.user, this.password});

  String get userClean => user!.replaceAll(RegExp(r'^|\D'), '');

  factory UserCredentials.fromJson(Map json) =>
      _$UserCredentialsFromJson(json as Map<String, dynamic>);
  Map<String, dynamic> toJson() => _$UserCredentialsToJson(this);
}

@JsonSerializable(explicitToJson: true)
class LoginDataModel {
  UserCredentials? userCredentials;
  VehicleModel? vehicleModel;
  LoginDataModel({this.userCredentials, this.vehicleModel});
  factory LoginDataModel.fromJson(Map json) =>
      _$LoginDataModelFromJson(json as Map<String, dynamic>);
  Map<String, dynamic> toJson() => _$LoginDataModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class UserModel {
  String? cpf;
  String? nome;
  String? telefone;
  String? email;
  String? cnh;
  String? coren;

  UserModel(
      {this.cpf, this.nome, this.telefone, this.email, this.cnh, this.coren});

  factory UserModel.fromJson(Map json) =>
      _$UserModelFromJson(json as Map<String, dynamic>);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}

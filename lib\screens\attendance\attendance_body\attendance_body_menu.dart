import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/bloc/register_photo/photo_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/attendance_regulation.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/conduct/attendance_conduct.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/main.dart';
import 'package:ambulancia_app/screens/attendance/supplies/main.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AttendanceMenu extends StatefulWidget {
  final int? activeStep;
  final AttendanceModel attendance;
  final Function(Widget?)? onChanged;
  AttendanceMenu({
    this.activeStep,
    required this.attendance,
    this.onChanged,
  });

  @override
  _AttendanceMenuState createState() => _AttendanceMenuState();
}

class _AttendanceMenuState extends State<AttendanceMenu> {
  static late List<AttendanceMenuModel> _itensMenu = [];
  int? _activeStepMenu;
  final _baseTranslate = 'attendanceScreen.menu';

  @override
  void initState() {
    super.initState();
    _activeStepMenu = 0;
    Future.delayed(Duration.zero, () => _populateMenu());
    Future.delayed(Duration.zero, () => _resetMenu());
  }

  void _resetMenu() {
    widget.onChanged!(_itensMenu[0].widget);
  }

  void _populateMenu() {
    _itensMenu = [
      AttendanceMenuModel(
        flex: 31,
        text: I18nHelper.translate(context, '$_baseTranslate.regulation.title'),
        widget: AttendanceRegulation(attendance: widget.attendance),
        showModal: false,
      ),
      AttendanceMenuModel(
        flex: 31,
        text: I18nHelper.translate(
            context, '$_baseTranslate.evaluationClinical.title'),
        widget: BlocProvider(
          create: (context) => RegisterPhotoCubit(),
          child: ClinicalEvaluation(attendanceModel: widget.attendance),
        ),
        showModal: true,
      ),
      AttendanceMenuModel(
        flex: 19,
        text: I18nHelper.translate(context, '$_baseTranslate.conduct.title'),
        widget: AttendanceConduct(attendanceModel: widget.attendance),
        showModal: true,
      ),
      AttendanceMenuModel(
        flex: 30,
        text: I18nHelper.translate(
            context, '$_baseTranslate.materialExpenses.title'),
        widget: AttendanceMaterialExpenses(attendance: widget.attendance),
        showModal: true,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
      return Row(
        children: _itensMenu
            .asMap()
            .entries
            .map((entry) => _menuTile(
                index: entry.key, item: entry.value, constraints: constraints))
            .toList(),
      );
    });
  }

  Widget _menuTile(
      {required AttendanceMenuModel item,
      int? index,
      BoxConstraints? constraints}) {
    final _padding = ConstantsTheme.padding * 0.5;
    return Expanded(
      flex: item.flex!,
      child: GestureDetector(
        key: Key('menu_item_$index'),
        onTap: () => _menuAction(item, index, constraints),
        child: Container(
          padding: EdgeInsets.all(
            ConstantsTheme.doublePadding,
          ),
          margin: EdgeInsets.only(
            left: index == 0 ? 0 : _padding,
            right: index == _itensMenu.length - 1 ? 0 : _padding,
          ),
          decoration: BoxDecoration(
              color: index == _activeStepMenu
                  ? AmbulanceColors.greenLight3
                  : AmbulanceColors.green,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(ConstantsTheme.borderRadius),
                topLeft: Radius.circular(ConstantsTheme.borderRadius),
                topRight: Radius.circular(ConstantsTheme.borderRadius),
              )),
          child: Text(
            item.text!,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _menuAction(
      AttendanceMenuModel item, int? index, BoxConstraints? constraints) {
    // if (item.widget is ClinicalEvaluation) {
    //   Alert.open(context,
    //       text: 'Funcionalidade em desenvolvimento.', title: 'Alerta');
    //   return;
    // }

    // condition access
    var index = BlocProvider.of<AttendanceMovementsCubit>(context)
        .getCurrentIndexStatusApp()!;
    if (index < 1) return;

    if (item.showModal!) {
      widget.onChanged!(Container());
      _openModal(item.widget, constraints);
    } else
      widget.onChanged!(item.widget);
    setState(() {
      _activeStepMenu = index;
    });
  }

  void _openModal(Widget? child, BoxConstraints? constraints) {
    final _dialog = showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius)),
          insetPadding: EdgeInsets.only(
            top: ConstantsTheme.doublePadding * 3,
            bottom: ConstantsTheme.doublePadding,
            left: ConstantsTheme.doublePadding,
            right: ConstantsTheme.doublePadding,
          ),
          content: Container(width: constraints!.maxWidth * 0.95, child: child),
        );
      },
    );

    _dialog.whenComplete(() {
      if (mounted) {
        setState(() {
          _activeStepMenu = 0;
          _resetMenu();
        });
      }
    });
  }
}

class AttendanceMenuModel {
  final String? text;
  final int? flex;
  final Widget? widget;
  final bool? showModal;

  AttendanceMenuModel({
    this.flex,
    this.text,
    this.widget,
    this.showModal,
  });
}

part of 'attendance_list_cubit.dart';

abstract class AttendanceListState {
  const AttendanceListState();
}

class AttendanceListInitial extends AttendanceListState {}

class LoadingAttendancesListState extends AttendanceListState {}

class ErrorAttendancesListState extends AttendanceListState {
  final message;
  ErrorAttendancesListState(this.message);
}

class LoadedAttendancesListState extends AttendanceListState {
  final List<AttendanceModel>? attendances;
  LoadedAttendancesListState({this.attendances});
}

class NoDataListState extends AttendanceListState {}

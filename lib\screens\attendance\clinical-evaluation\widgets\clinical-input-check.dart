import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';

class ClinicalInputCheck extends StatefulWidget {
  final Function(bool?)? onPresss;
  final checkColor;
  final controller;
  final hintText;
  final label;
  final labelColor;
  final title;
  final forceValue;
  final checkParentValue;
  final itemIndex;
  ClinicalInputCheck({
    Key? key,
    this.controller,
    this.hintText = '-',
    this.label = '',
    this.labelColor = AmbulanceColors.greenDark,
    this.onPresss,
    this.checkColor = AmbulanceColors.greenDark,
    this.title = '',
    this.forceValue,
    this.checkParentValue,
    this.itemIndex,
  }) : super(key: key);

  @override
  _ClinicalInputCheckState createState() => _ClinicalInputCheckState();
}

class _ClinicalInputCheckState extends State<ClinicalInputCheck> {
  bool? isCheck;
  @override
  void initState() {
    isCheck = widget.forceValue ?? false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() => isCheck = !isCheck!);
        if (widget.checkParentValue != null)
          widget.checkParentValue(widget.itemIndex) == false
              ? isCheck = true
              : isCheck = false;
        if (widget.onPresss != null) widget.onPresss!(isCheck);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Row(
          children: [
            Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.fromBorderSide(
                    BorderSide(
                      color: widget.labelColor,
                      width: 1.6,
                    ),
                  ),
                  borderRadius: BorderRadius.circular(ConstantsTheme.padding),
                ),
                child: _iconCheck()),
            SizedBox(width: 15),
            Expanded(
              child: Text(
                widget.label,
                style: TextStyle(
                  color: widget.labelColor,
                  fontSize: 20,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _iconCheck() => widget.forceValue ?? isCheck!
      ? Icon(
          Icons.check,
          color: widget.checkColor,
          semanticLabel: widget.label,
          size: 30,
        )
      : Container();
}

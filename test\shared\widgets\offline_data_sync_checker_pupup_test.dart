import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_notification/offline_first_notification_cubit.dart';
import 'package:ambulancia_app/shared/widgets/offline_data_sync_checker_pupup.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_i18n/flutter_i18n.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockConnectivityCubit extends Mock implements ConnectivityCubit {}

class MockOfflineFirstNotificationCubit extends Mock
    implements OfflineFirstNotificationCubit {}

String TITLE = 'Atenção!';
String MESSAGE =
    'Existem atendimentos com dados pendentes de sincronização. Conecte-se à internet para que a sincronização dos dados seja realizada automaticamente.';

void main() {
  late MockConnectivityCubit mockConnectivityCubit;
  late MockOfflineFirstNotificationCubit mockOfflineFirstNotificationCubit;

  setUpAll(() {
    registerFallbackValue(ConnectivityInitial());
    registerFallbackValue(ConnectivityOfflineState());
    registerFallbackValue(OfflineFirstNotificationInitial());
    registerFallbackValue(OfflineFirstNotificationStartingSync());
    registerFallbackValue(
        OfflineFirstNotificationFinishing(amountsOfOfflineData: 0));
    registerFallbackValue(OfflineFirstNotificationError());
  });

  setUp(() {
    mockConnectivityCubit = MockConnectivityCubit();
    mockOfflineFirstNotificationCubit = MockOfflineFirstNotificationCubit();
  });

  group('OfflineDataSyncCheckerPopUp', () {
    Future<void> pumpWidget(WidgetTester tester) async {
      await tester.pumpWidget(
        MultiBlocProvider(
          providers: [
            BlocProvider<ConnectivityCubit>.value(value: mockConnectivityCubit),
            BlocProvider<OfflineFirstNotificationCubit>.value(
              value: mockOfflineFirstNotificationCubit,
            ),
          ],
          child: MaterialApp(
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              DefaultCupertinoLocalizations.delegate,
              FlutterI18nDelegate(
                translationLoader: E2EFileTranslationLoader(
                  useCountryCode: true,
                  basePath: 'assets/i18n',
                  fallbackFile: 'pt_BR',
                  forcedLocale: const Locale('pt', 'BR'),
                ),
              ),
            ],
            supportedLocales: [
              const Locale('pt', 'BR'),
            ],
            home: Scaffold(
              body: Builder(
                builder: (context) {
                  return ElevatedButton(
                    key: Key("check_sync_button"),
                    onPressed: () {
                      final checker = OfflineDataSyncCheckerPopUp(
                        context: context,
                        baseTranslate: 'home.main.attendaceList',
                      );
                      checker.checkOfflineDataSync();
                    },
                    child: Text('Check Sync'),
                  );
                },
              ),
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();
    }

    setUp(() {});

    testWidgets('should show alert dialog when offline and data to sync',
        (WidgetTester tester) async {
      when(() => mockConnectivityCubit.state)
          .thenReturn(ConnectivityOfflineState());
      when(() => mockConnectivityCubit.stream)
          .thenAnswer((_) => Stream.value(ConnectivityOfflineState()));
      when(() => mockOfflineFirstNotificationCubit
          .amountsOfDataToBeSynchronizedawait).thenReturn(3);
      when(() => mockOfflineFirstNotificationCubit.stream)
          .thenAnswer((_) => Stream.value(OfflineFirstNotificationInitial()));

      await pumpWidget(tester);

      await tester.tap(find.byKey(Key("check_sync_button")));
      await tester.pumpAndSettle();

      expect(find.text(TITLE), findsOneWidget);
      expect(find.text(MESSAGE), findsOneWidget);

      await tester.tap(find.text("FECHAR"));
      await tester.pumpAndSettle();
    });

    testWidgets('should not show alert dialog when online',
        (WidgetTester tester) async {
      when(() => mockConnectivityCubit.state)
          .thenReturn(ConnectivityOnlineState());
      when(() => mockConnectivityCubit.stream)
          .thenAnswer((_) => Stream.value(ConnectivityOnlineState()));
      when(() => mockOfflineFirstNotificationCubit
          .amountsOfDataToBeSynchronizedawait).thenReturn(3);
      when(() => mockOfflineFirstNotificationCubit.stream)
          .thenAnswer((_) => Stream.value(OfflineFirstNotificationInitial()));

      await pumpWidget(tester);

      await tester.tap(find.byKey(Key("check_sync_button")));
      await tester.pumpAndSettle();

      expect(find.text(TITLE), findsNothing);
      expect(find.text(MESSAGE), findsNothing);
    });

    testWidgets('should not show alert dialog when no data to sync',
        (WidgetTester tester) async {
      when(() => mockConnectivityCubit.state)
          .thenReturn(ConnectivityOfflineState());
      when(() => mockConnectivityCubit.stream)
          .thenAnswer((_) => Stream.value(ConnectivityOfflineState()));
      when(() => mockOfflineFirstNotificationCubit
          .amountsOfDataToBeSynchronizedawait).thenReturn(0);
      when(() => mockOfflineFirstNotificationCubit.stream)
          .thenAnswer((_) => Stream.value(OfflineFirstNotificationInitial()));

      await pumpWidget(tester);

      await tester.tap(find.byKey(Key("check_sync_button")));
      await tester.pumpAndSettle();

      expect(find.text(TITLE), findsNothing);
      expect(find.text(MESSAGE), findsNothing);
    });

    testWidgets('should not show alert dialog when no data to sync',
        (WidgetTester tester) async {
      when(() => mockConnectivityCubit.state)
          .thenReturn(ConnectivityOfflineState());
      when(() => mockConnectivityCubit.stream)
          .thenAnswer((_) => Stream.value(ConnectivityOfflineState()));
      when(() => mockOfflineFirstNotificationCubit
          .amountsOfDataToBeSynchronizedawait).thenReturn(0);
      when(() => mockOfflineFirstNotificationCubit.stream)
          .thenAnswer((_) => Stream.value(OfflineFirstNotificationInitial()));

      await pumpWidget(tester);

      await tester.tap(find.byKey(Key("check_sync_button")));
      await tester.pumpAndSettle();

      expect(find.text(TITLE), findsNothing);
      expect(find.text(MESSAGE), findsNothing);
    });
  });
}

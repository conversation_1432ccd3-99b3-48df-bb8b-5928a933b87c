import 'package:flutter/services.dart';

class FormatterField {
  static final List<TextInputFormatter> inputEmailFormatter =
      <TextInputFormatter>[
    FilteringTextInputFormatter.allow((RegExp("[a-zA-Z0-9.@_-]"))),
    LengthLimitingTextInputFormatter(64),
  ];
  static final List<TextInputFormatter> inputNumberSuppliesFormatter =
      <TextInputFormatter>[
    FilteringTextInputFormatter.allow((RegExp("[0-9]"))),
    LengthLimitingTextInputFormatter(3),
  ];

  static final List<TextInputFormatter> simpleTextFormatter =
      <TextInputFormatter>[
    FilteringTextInputFormatter.allow(RegExp(r"[A-Za-zÃÁãÉéÊêÍíÓóÚú.,ç ]")),
  ];
  static final List<TextInputFormatter> simpleNumberFormatter =
      <TextInputFormatter>[
    FilteringTextInputFormatter.allow(RegExp(r"[0-9]")),
  ];
}

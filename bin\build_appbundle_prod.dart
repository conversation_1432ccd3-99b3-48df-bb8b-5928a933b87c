import 'dart:convert';
import 'dart:io';

void main(List<String> args) {
  // print('Hi args $args');
  print('Build Android PROD...');

  Process.start('flutter', [
    'build',
    'appbundle',
    '-t',
    'lib/main-prod.dart',
    '--release',
    '--verbose',
    '--dart-define-from-file',
    'lib/prod.env.json',
    ...args
  ]).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');
    });
    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

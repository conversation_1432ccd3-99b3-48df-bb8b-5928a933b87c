import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/destiny_model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'destiny_state.dart';

class DestinyCubit extends Cubit<DestinyState> {
  DestinyCubit() : super(DestinyInitial());

  DestinyModel? _destinyModel;
  DestinyModel? get destinyModel => _destinyModel;

  Future listDestinies(codUnimed, {bool forceUpdate = false}) async {
    try {
      emit(LoadingDestiniesState());

      if (_destinyModel != null && !forceUpdate)
        emit(LoadedDestiniesState(_destinyModel));
      else {
        _destinyModel =
            await Locator.instance<AttendanceApi>().listDestinies(codUnimed);
        emit(LoadedDestiniesState(_destinyModel));
      }
    } catch (ex) {
      emit(ErrorDestinyState(ex.toString()));
    }
  }

  Future updateDestiny(
      {required AttendanceModel attendance, required DestinyObject selectedDestinyView}) async {
    try {
      emit(UpdatingDestiniesState(selectedDestinyView));

      await Locator.instance<AttendanceApi>().updateDestiny(
        numAttendance: attendance.numAtendimento,
        codigoDestinoPaciente: selectedDestinyView.codigoDestinoPaciente,
      );

      await _updateAddressPerfilApps(attendance);

      emit(UpdatedDestiniesState());
    } catch (ex) {
      emit(ErrorDestinyState(ex.toString()));
    }
  }

  _updateAddressPerfilApps(AttendanceModel attendance) async {
    // final AttendanceModel currentAttendance =
        await Locator.instance<AttendanceApi>()
            .getAttendanceDetail('${attendance.numAtendimento}');
    // final token = await Locator.instance.get<AuthApi>().getTokenPerfilApps();

    // await Locator.instance.get<AttendanceApi>().updateDestinyGeolocalization(
    //     attendanceModel: currentAttendance, token: token);
  }
}

import 'package:ambulancia_app/models/motive.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  MotiveModel? testMotiveModel;
  Map<String, dynamic>? jsonMotiveModel;
  setUpAll(
    () {
      testMotiveModel = MotiveModel(
        codigo: "1",
        descricao: "1",
      );

      jsonMotiveModel = {
        "codigo": "1",
        "descricao": "1",
      };
    },
  );

  group(
    "isInstanceOf MotiveModel model tests",
    () {
      test("Should be return instance of MotiveModel", () {
        expect(testMotiveModel, isInstanceOf<MotiveModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of MotiveModel to json", () {
      expect(testMotiveModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of MotiveModel from json", () {
      expect(
          MotiveModel.fromJson(jsonMotiveModel!), isInstanceOf<MotiveModel>());
    });
  });

  group(
    "isInstanceOf MotiveModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonMotiveModel!["codigo"], isInstanceOf<String>());
        expect(jsonMotiveModel!["descricao"], isInstanceOf<String>());
      });
    },
  );
}

import 'package:ambulancia_app/bloc/register_photo/photo_state.dart';
import 'package:ambulancia_app/models/cid.model.dart';
import 'package:ambulancia_app/shared/api/photo.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RegisterPhotoCubit extends Cubit<RegisterPhotoState> {
  RequestCidModel requestCidModel = RequestCidModel();
  final service = Locator.instance.get<PhotoOperatorApi>();
  RegisterPhotoCubit() : super(RegisterPhotoInitial());

  Future<void> savePhotoInAppDocuments(
      {required xfile,
      serviceNumber,
      recordType,
      required String reasonAnnexPhoto}) async {
    try {
      emit(LoadingSendPhoto());
      final response = await service.sendPhoto(
          numAtendimento: serviceNumber.toString(),
          pathPhotoOperator: xfile,
          recordType: recordType.toString(),
          reasonAnnexPhoto: reasonAnnexPhoto);
      emit(SuccessSendPhoto(response));
    } catch (e) {
      emit(ErrorSendPhotoState(e.toString()));
    }
  }
}

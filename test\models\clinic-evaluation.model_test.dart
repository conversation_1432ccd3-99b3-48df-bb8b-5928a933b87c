import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  ResponseClinicEvaluation? modelTestResponseClinicEvaluation;
  Map<String, dynamic>? json;
  RequestClinicEvaluation? modelTestRequestClinicEvaluation;
  Map<String, dynamic>? json2;
  setUpAll(
    () {
      modelTestResponseClinicEvaluation = ResponseClinicEvaluation(
        acessoVenoso: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        circulacao: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        funacaoRenal: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        pele: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        pupilas: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        ritmos: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        sinaisLaterizacao: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        sistemaGastrointestinal: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        tipoMarcaPasso: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        viasAereasVentilacao: [
          Activity(
            codigo: "dsa-ds",
            descricao: "ambulancia",
          ),
        ],
        informacaoCirurgica: [
          Activity(
            codigo: "1",
            descricao: "ambulancia",
          ),
        ],
        glasgow: [
          Activity(
            codigo: "1",
            descricao: "ambulancia",
          ),
        ],
        sistemaVenosoCentral: [
          Activity(
            codigo: "1",
            descricao: "ambulancia",
          ),
        ],
      );
      json = {
        "acessoVenoso": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "circulacao": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "funacaoRenal": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "pele": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "pupilas": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "ritmos": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "sinaisLaterizacao": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "sistemaGastrointestinal": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "tipoMarcaPasso": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "viasAereasVentilacao": [
          {
            "codigo": "dsa-ds",
            "descricao": "ambulancia",
          }
        ],
        "informacaoCirurgica": [
          {
            "codigo": 1,
            "descricao": "ambulancia",
          }
        ],
        "glasgow": [
          {
            "codigo": 1,
            "descricao": "ambulancia",
          }
        ],
        "sistemaVenosoCentral": [
          {
            "codigo": 1,
            "descricao": "ambulancia",
          }
        ],
      };
      modelTestRequestClinicEvaluation = RequestClinicEvaluation(
        numAtendimento: 1,
        frequenciaCardiaca: 2,
        frequenciaRespiratoria: 3,
        pressaoArterialAlta: 2,
        pressaoArterialBaixa: 2,
        saturacao: 2,
        ritmos: "2",
        circulacao: "2",
        tipoMarcaPasso: "1",
        acessoVenoso: "1",
        diluicaoVelocidadeInfusao: 1,
        aminasVasopressorasQuais: "1",
        pupilas: "1",
        glasgow: 1,
        sinaisLaterizacao: "1",
        pele: "1",
        posOperatorio: "1",
        feridaOperatoria: "1",
        infeccaoFeridaOperatoria: "1",
        drenos: "1",
        estomas: "1",
        examesImagem: "1",
        sistemaGastrointestinal: "1",
        duracaoVentilacaoQtdeDias: 1,
        duracaoVentilacaoQtdeHorasDia: 1,
        observacaoAvaliacaoClinica: "1",
        funcaoRenalDiurese: "1",
        funcaoRenalNormal: "1",
        diurese: "1",
        intubacaoTraqueal: "1",
        mascaraVenturi: "1",
        traqueostomia: "1",
        cateterNasal: "1",
        oxigenioPorMascara: "1",
        ventilacaoMecanica: "1",
        arAmbiente: "1",
        escalaDor: "SD",
        intensidadeDor: 2,
        dorCabeca: "S",
        dorBarriga: "S",
        dorMembrosSup: "S",
        dorMembrosInf: "S",
        dorLombar: "S",
        dorToraxPeito: "S",
        dorDorso: "S",
        dorPescocoCervical: "S",
        dorOutros: "S",
        obsDorOutros: "TESTE",
      );
      json2 = {
        "numAtendimento": 1,
        "frequenciaCardiaca": 2,
        "frequenciaRespiratoria": 3,
        "pressaoArterialAlta": 2,
        "pressaoArterialBaixa": 2,
        "saturacao": 2,
        "ritmos": "2",
        "circulacao": "2",
        "tipoMarcaPasso": "1",
        "acessoVenoso": "1",
        "diluicaoVelocidadeInfusao": 1,
        "aminasVasopressorasQuais": "1",
        "pupilas": "1",
        "glasgow": 1,
        "sinaisLaterizacao": "1",
        "pele": "1",
        "posOperatorio": "1",
        "feridaOperatoria": "1",
        "infeccaoFeridaOperatoria": "1",
        "drenos": "1",
        "estomas": "1",
        "examesImagem": "1",
        "examesLaboratoriais": "1",
        "sistemaGastrointestinal": "1",
        "duracaoVentilacaoQtdeDias": 1,
        "duracaoVentilacaoQtdeHorasDia": 1,
        "observacaoAvaliacaoClinica": "1",
        "funcaoRenalDiurese": "1",
        "funcaoRenalNormal": "1",
        "diurese": "1",
        "intubacaoTraqueal": "1",
        "mascaraVenturi": "1",
        "traqueostomia": "1",
        "cateterNasal": "1",
        "oxigenioPorMascara": "1",
        "ventilacaoMecanica": "1",
        "arAmbiente": "1",
        "funcaoRenalHemodialise": "1",
        "escalaDor": "SD",
        "intensidadeDor": 2,
        "dorCabeca": "S",
        "dorBarriga": "S",
        "dorMembrosSup": "S",
        "dorMembrosInf": "S",
        "dorLombar": "S",
        "dorToraxPeito": "S",
        "dorDorso": "S",
        "dorPescocoCervical": "S",
        "dorOutros": "S",
        "obsDorOutros": "TESTE",
      };
    },
  );

  group(
    "isInstanceOf ResponseClinicEvaluation model tests",
    () {
      test("Should be return instance of ResponseClinicEvaluation", () {
        expect(modelTestResponseClinicEvaluation,
            isInstanceOf<ResponseClinicEvaluation>());
      });
      test("Should be return instance of AcessoVenoso", () {
        expect(modelTestResponseClinicEvaluation!.acessoVenoso.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of Circulacao", () {
        expect(modelTestResponseClinicEvaluation!.circulacao.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of FunacaoRenal", () {
        expect(modelTestResponseClinicEvaluation!.funacaoRenal.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of Pele", () {
        expect(modelTestResponseClinicEvaluation!.pele.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of Pupilas", () {
        expect(modelTestResponseClinicEvaluation!.pupilas.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of Ritmos", () {
        expect(modelTestResponseClinicEvaluation!.ritmos.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of SinaisLaterizacao", () {
        expect(modelTestResponseClinicEvaluation!.sinaisLaterizacao.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of SistemaGastrointestinal", () {
        expect(modelTestResponseClinicEvaluation!.sistemaGastrointestinal.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of TipoMarcaPasso", () {
        expect(modelTestResponseClinicEvaluation!.tipoMarcaPasso.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of ViasAereasVentilacao", () {
        expect(modelTestResponseClinicEvaluation!.viasAereasVentilacao.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of InformacaoCirurgica", () {
        expect(modelTestResponseClinicEvaluation!.informacaoCirurgica.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of Glasgow", () {
        expect(modelTestResponseClinicEvaluation!.glasgow.first,
            isInstanceOf<Activity>());
      });
      test("Should be return instance of SistemaVenosoCentral", () {
        expect(modelTestResponseClinicEvaluation!.sistemaVenosoCentral.first,
            isInstanceOf<Activity>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of ResponseClinicEvaluation to json", () {
      expect(modelTestResponseClinicEvaluation!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of ResponseClinicEvaluation from json", () {
      expect(ResponseClinicEvaluation.fromJson(json!),
          isInstanceOf<ResponseClinicEvaluation>());
    });
    test("Should be return instance of AcessoVenoso from json", () {
      expect(Activity.fromJson(json!["acessoVenoso"][0]),
          isInstanceOf<Activity>());
    });
    test("Should be return instance of Circulacao from json", () {
      expect(
          Activity.fromJson(json!["circulacao"][0]), isInstanceOf<Activity>());
    });
    test("Should be return instance of FunacaoRenal from json", () {
      expect(Activity.fromJson(json!["funacaoRenal"][0]),
          isInstanceOf<Activity>());
    });
    test("Should be return instance of Pele from json", () {
      expect(Activity.fromJson(json!["pele"][0]), isInstanceOf<Activity>());
    });
    test("Should be return instance of Pupilas from json", () {
      expect(Activity.fromJson(json!["pupilas"][0]), isInstanceOf<Activity>());
    });
    test("Should be return instance of Ritmos from json", () {
      expect(Activity.fromJson(json!["ritmos"][0]), isInstanceOf<Activity>());
    });
    test("Should be return instance of SinaisLaterizacao from json", () {
      expect(Activity.fromJson(json!["sinaisLaterizacao"][0]),
          isInstanceOf<Activity>());
    });
    test("Should be return instance of SistemaGastrointestinal from json", () {
      expect(Activity.fromJson(json!["sistemaGastrointestinal"][0]),
          isInstanceOf<Activity>());
    });
    test("Should be return instance of TipoMarcaPasso from json", () {
      expect(Activity.fromJson(json!["tipoMarcaPasso"][0]),
          isInstanceOf<Activity>());
    });
    test("Should be return instance of ViasAereasVentilacao from json", () {
      expect(Activity.fromJson(json!["viasAereasVentilacao"][0]),
          isInstanceOf<Activity>());
    });
    test("Should be return instance of InformacaoCirurgica from json", () {
      expect(Activity.fromJson(json!["informacaoCirurgica"][0]),
          isInstanceOf<Activity>());
    });
    test("Should be return instance of Glasgow from json", () {
      expect(Activity.fromJson(json!["glasgow"][0]), isInstanceOf<Activity>());
    });
    test("Should be return instance of SistemaVenosoCentral from json", () {
      expect(Activity.fromJson(json!["sistemaVenosoCentral"][0]),
          isInstanceOf<Activity>());
    });
  });

  group(
    "isInstanceOf ResponseClinicEvaluation json to model type test",
    () {
      test("Should be return type of the List", () {
        expect(
            modelTestResponseClinicEvaluation!.toJson()["acessoVenoso"] is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["circulacao"] is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["funacaoRenal"] is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["pele"] is List, true);
        expect(modelTestResponseClinicEvaluation!.toJson()["pupilas"] is List,
            true);
        expect(modelTestResponseClinicEvaluation!.toJson()["ritmos"] is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["sinaisLaterizacao"]
                is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!
                .toJson()["sistemaGastrointestinal"] is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["tipoMarcaPasso"]
                is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["viasAereasVentilacao"]
                is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["informacaoCirurgica"]
                is List,
            true);
        expect(modelTestResponseClinicEvaluation!.toJson()["glasgow"] is List,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["sistemaVenosoCentral"]
                is List,
            true);
      });

      test("Can´t return if is null and verify acessoVenoso type", () {
        expect(
            modelTestResponseClinicEvaluation!.toJson()["acessoVenoso"] != null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["acessoVenoso"][0]
                ["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["acessoVenoso"][0]
                ["descricao"] is String,
            true);
      });

      test("Can´t return if is null and verify circulacao type", () {
        expect(
            modelTestResponseClinicEvaluation!.toJson()["circulacao"] != null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["circulacao"][0]
                ["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["circulacao"][0]
                ["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify funacaoRenal type", () {
        expect(
            modelTestResponseClinicEvaluation!.toJson()["funacaoRenal"] != null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["funacaoRenal"][0]
                ["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["funacaoRenal"][0]
                ["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify pele type", () {
        expect(
            modelTestResponseClinicEvaluation!.toJson()["pele"] != null, true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["pele"][0]["codigo"]
                is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["pele"][0]["descricao"]
                is String,
            true);
      });
      test("Can´t return if is null and verify pupilas type", () {
        expect(modelTestResponseClinicEvaluation!.toJson()["pupilas"] != null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["pupilas"][0]["codigo"]
                is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["pupilas"][0]
                ["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify ritmos type", () {
        expect(modelTestResponseClinicEvaluation!.toJson()["ritmos"] != null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["ritmos"][0]["codigo"]
                is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["ritmos"][0]
                ["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify sinaisLaterizacao type", () {
        expect(
            modelTestResponseClinicEvaluation!.toJson()["sinaisLaterizacao"] !=
                null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["sinaisLaterizacao"][0]
                ["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["sinaisLaterizacao"][0]
                ["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify sistemaGastrointestinal type",
          () {
        expect(
            modelTestResponseClinicEvaluation!
                    .toJson()["sistemaGastrointestinal"] !=
                null,
            true);
        expect(
            modelTestResponseClinicEvaluation!
                .toJson()["sistemaGastrointestinal"][0]["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!
                .toJson()["sistemaGastrointestinal"][0]["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify tipoMarcaPasso type", () {
        expect(
            modelTestResponseClinicEvaluation!.toJson()["tipoMarcaPasso"] !=
                null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["tipoMarcaPasso"][0]
                ["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["tipoMarcaPasso"][0]
                ["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify viasAereasVentilacao type", () {
        expect(
            modelTestResponseClinicEvaluation!
                    .toJson()["viasAereasVentilacao"] !=
                null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["viasAereasVentilacao"]
                [0]["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["viasAereasVentilacao"]
                [0]["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify informacaoCirurgica type", () {
        expect(
            modelTestResponseClinicEvaluation!
                    .toJson()["informacaoCirurgica"] !=
                null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["informacaoCirurgica"]
                [0]["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["informacaoCirurgica"]
                [0]["descricao"] is String,
            true);
      });

      test("Can´t return if is null and verify glasgow type", () {
        expect(modelTestResponseClinicEvaluation!.toJson()["glasgow"] != null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["glasgow"][0]["codigo"]
                is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["glasgow"][0]
                ["descricao"] is String,
            true);
      });
      test("Can´t return if is null and verify sistemaVenosoCentral type", () {
        expect(
            modelTestResponseClinicEvaluation!
                    .toJson()["sistemaVenosoCentral"] !=
                null,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["sistemaVenosoCentral"]
                [0]["codigo"] is String,
            true);
        expect(
            modelTestResponseClinicEvaluation!.toJson()["sistemaVenosoCentral"]
                [0]["descricao"] is String,
            true);
      });
    },
  );
  //request test

  group(
    "isInstanceOf RequestClinicEvaluation model tests",
    () {
      test("Should be return instance of RequestClinicEvaluation", () {
        expect(modelTestRequestClinicEvaluation,
            isInstanceOf<RequestClinicEvaluation>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of RequestClinicEvaluation to json", () {
      expect(modelTestRequestClinicEvaluation!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of RequestClinicEvaluation from json2", () {
      expect(RequestClinicEvaluation.fromJson(json2!),
          isInstanceOf<RequestClinicEvaluation>());
    });
  });

  group(
    "isInstanceOf RequestClinicEvaluation json to model type test",
    () {
      test("Should be return type of the json2", () {
        expect(json2!["numAtendimento"] is int, true);
        expect(json2!["frequenciaCardiaca"] is int, true);
        expect(json2!["frequenciaRespiratoria"] is int, true);
        expect(json2!["pressaoArterialAlta"] is int, true);
        expect(json2!["pressaoArterialBaixa"] is int, true);
        expect(json2!["saturacao"] is int, true);

        expect(json2!["ritmos"] is String, true);
        expect(json2!["circulacao"] is String, true);
        expect(json2!["tipoMarcaPasso"] is String, true);
        expect(json2!["acessoVenoso"] is String, true);
        expect(json2!["diluicaoVelocidadeInfusao"] is int, true);
        expect(json2!["aminasVasopressorasQuais"] is String, true);
        expect(json2!["pupilas"] is String, true);
        expect(json2!["glasgow"] is int, true);
        expect(json2!["sinaisLaterizacao"] is String, true);
        expect(json2!["pele"] is String, true);
        expect(json2!["posOperatorio"] is String, true);
        expect(json2!["feridaOperatoria"] is String, true);

        expect(json2!["infeccaoFeridaOperatoria"] is String, true);
        expect(json2!["drenos"] is String, true);
        expect(json2!["estomas"] is String, true);
        expect(json2!["examesLaboratoriais"] is String, true);
        expect(json2!["examesImagem"] is String, true);
        expect(json2!["sistemaGastrointestinal"] is String, true);
        expect(json2!["duracaoVentilacaoQtdeDias"] is int, true);
        expect(json2!["duracaoVentilacaoQtdeHorasDia"] is int, true);
        expect(json2!["observacaoAvaliacaoClinica"] is String, true);
        expect(json2!["funcaoRenalHemodialise"] is String, true);
        expect(json2!["funcaoRenalDiurese"] is String, true);
        expect(json2!["funcaoRenalNormal"] is String, true);
        expect(json2!["diurese"] is String, true);
        expect(json2!["intubacaoTraqueal"] is String, true);
        expect(json2!["mascaraVenturi"] is String, true);
        expect(json2!["traqueostomia"] is String, true);
        expect(json2!["cateterNasal"] is String, true);
        expect(json2!["oxigenioPorMascara"] is String, true);
        expect(json2!["ventilacaoMecanica"] is String, true);
        expect(json2!["arAmbiente"] is String, true);
        expect(json2!["escalaDor"] is String, true);
        expect(json2!["intensidadeDor"] is int, true);
        expect(json2!["dorCabeca"] is String, true);
        expect(json2!["dorBarriga"] is String, true);
        expect(json2!["dorMembrosSup"] is String, true);
        expect(json2!["dorMembrosInf"] is String, true);
        expect(json2!["dorLombar"] is String, true);
        expect(json2!["dorToraxPeito"] is String, true);
        expect(json2!["dorDorso"] is String, true);
        expect(json2!["dorPescocoCervical"] is String, true);
        expect(json2!["dorOutros"] is String, true);
        expect(json2!["obsDorOutros"] is String, true);
      });
      test("Can´t return if is null", () {
        expect(json2!["numAtendimento"] != null, true);
        expect(json2!["frequenciaCardiaca"] != null, true);
        expect(json2!["frequenciaRespiratoria"] != null, true);
        expect(json2!["pressaoArterialAlta"] != null, true);
        expect(json2!["pressaoArterialBaixa"] != null, true);
        expect(json2!["saturacao"] != null, true);
        expect(json2!["ritmos"] != null, true);
        expect(json2!["circulacao"] != null, true);
        expect(json2!["tipoMarcaPasso"] != null, true);
        expect(json2!["acessoVenoso"] != null, true);
        expect(json2!["diluicaoVelocidadeInfusao"] != null, true);
        expect(json2!["aminasVasopressorasQuais"] != null, true);
        expect(json2!["pupilas"] != null, true);
        expect(json2!["glasgow"] != null, true);
        expect(json2!["sinaisLaterizacao"] != null, true);
        expect(json2!["pele"] != null, true);
        expect(json2!["posOperatorio"] != null, true);
        expect(json2!["feridaOperatoria"] != null, true);
        expect(json2!["infeccaoFeridaOperatoria"] != null, true);
        expect(json2!["drenos"] != null, true);
        expect(json2!["estomas"] != null, true);
        expect(json2!["examesLaboratoriais"] != null, true);
        expect(json2!["examesImagem"] != null, true);
        expect(json2!["sistemaGastrointestinal"] != null, true);
        expect(json2!["duracaoVentilacaoQtdeDias"] != null, true);
        expect(json2!["duracaoVentilacaoQtdeHorasDia"] != null, true);
        expect(json2!["observacaoAvaliacaoClinica"] != null, true);
        expect(json2!["funcaoRenalHemodialise"] != null, true);
        expect(json2!["funcaoRenalDiurese"] != null, true);
        expect(json2!["funcaoRenalNormal"] != null, true);
        expect(json2!["diurese"] != null, true);
        expect(json2!["intubacaoTraqueal"] != null, true);
        expect(json2!["mascaraVenturi"] != null, true);
        expect(json2!["traqueostomia"] != null, true);
        expect(json2!["cateterNasal"] != null, true);
        expect(json2!["oxigenioPorMascara"] != null, true);
        expect(json2!["ventilacaoMecanica"] != null, true);
        expect(json2!["arAmbiente"] != null, true);
        expect(json2!["escalaDor"] != null, true);
        expect(json2!["intensidadeDor"] != null, true);
        expect(json2!["dorCabeca"] != null, true);
        expect(json2!["dorBarriga"] != null, true);
        expect(json2!["dorMembrosSup"] != null, true);
        expect(json2!["dorMembrosInf"] != null, true);
        expect(json2!["dorLombar"] != null, true);
        expect(json2!["dorToraxPeito"] != null, true);
        expect(json2!["dorDorso"] != null, true);
        expect(json2!["dorPescocoCervical"] != null, true);
        expect(json2!["dorOutros"] != null, true);
        expect(json2!["obsDorOutros"] != null, true);
      });
    },
  );
}

import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/attendance_body_menu.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/patient_id.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class AttendanceBody extends StatefulWidget {
  final AttendanceModel attendance;
  AttendanceBody({required this.attendance});
  @override
  _AttendanceBodyState createState() => _AttendanceBodyState();
}

class _AttendanceBodyState extends State<AttendanceBody> {
  int _step = 0;
  Widget? _activeWidget;

  @override
  void initState() {
    super.initState();
    _activeWidget = Container();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ConstantsTheme.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ConstantsTheme.padding),
      ),
      child: Column(
        children: [
          PatientId(
            attendance: widget.attendance,
          ),
          SizedBox(height: ConstantsTheme.padding),
          AttendanceMenu(
            activeStep: _step,
            attendance: widget.attendance,
            onChanged: (Widget? widget) {
              SchedulerBinding.instance.addPostFrameCallback((_) {
                setState(() {
                  _activeWidget = widget ?? Container();
                });
              });
            },
          ),
          SizedBox(height: ConstantsTheme.doublePadding),
          Padding(
            padding: EdgeInsets.all(ConstantsTheme.padding),
            child: _activeWidget,
          )
        ],
      ),
    );
  }
}

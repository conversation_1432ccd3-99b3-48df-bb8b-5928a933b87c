import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

Future<Null> takeAPictureTest(WidgetTester tester) async {
  final Finder buttonTakePicture =
      find.byKey(const Key('button_take_picture')).last;
  await tester.tap(buttonTakePicture);
  await tester.pumpAndSettle(Duration(seconds: 8));

  final Finder buttonCheck = find.byIcon(Icons.check).last;
  await tester.tap(buttonCheck);
  await tester.pumpAndSettle();
}

import 'package:ambulancia_app/models/funcionario_model.dart';

class TeamVehicle {
  int? codUnimed;
  String? codPlantao;
  String? codVeiculo;
  String? nomeVeiculo;
  List<FuncionarioModel>? listaFuncionarioFuncao;

  TeamVehicle(
      {this.codUnimed,
      this.codPlantao,
      this.codVeiculo,
      this.nomeVeiculo,
      this.listaFuncionarioFuncao})
      : super();

  TeamVehicle.fromJson(Map<String, dynamic> json) {
    codUnimed = json['codUnimed'];
    codPlantao = json['codPlantao'];
    codVeiculo = json['codVeiculo'];
    nomeVeiculo = json['nomeVeiculo'];
    if (json['listaFuncionarioFuncao'] != null) {
      listaFuncionarioFuncao = [];
      json['listaFuncionarioFuncao'].forEach((v) {
        listaFuncionarioFuncao!.add(new FuncionarioModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['codUnimed'] = this.codUnimed;
    data['codPlantao'] = this.codPlantao;
    data['codVeiculo'] = this.codVeiculo;
    data['nomeVeiculo'] = this.nomeVeiculo;
    if (this.listaFuncionarioFuncao != null) {
      data['listaFuncionarioFuncao'] =
          this.listaFuncionarioFuncao!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

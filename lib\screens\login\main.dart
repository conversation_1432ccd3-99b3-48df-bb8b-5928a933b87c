import 'package:ambulancia_app/screens/login/login_form.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final logger = UnimedLogger(className: 'LoginPage');

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      key: Key('login_page'),
      canPop: false,
      child: Scaffold(
        body: Stack(
          children: <Widget>[
            SvgPicture.asset(
              'assets/images/unimed_bg_branco.svg',
              alignment: Alignment.center,
              fit: BoxFit.fill,
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _cardForm(),
                Container(
                  height: 10,
                ),
                Image.asset(
                  'assets/images/logos-form.png',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _cardForm() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.6,
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Column(
            children: [
              Image.asset(
                'assets/images/logo-unimed.png',
                width: 100,
              ),
              LoginForm()
            ],
          ),
        ),
      ),
    );
  }
}

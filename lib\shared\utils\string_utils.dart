import 'package:intl/intl.dart';

class StringUtils {
  static String removeDiactrics(String input) {
    final matchRE = RegExp(r"[ÁÀÃÂáàãâÉÈÊéèêÍÌÎíìîÓÒÔÕóòôõÚÙÛúùûçÇ]");
    final replacementMap = Map.fromIterables(
        "ÁÀÃÂáàãâÉÈÊéèêÍÌÎíìîÓÒÔÕóòôõÚÙÛúùûçÇ".split(""),
        "AAAAaaaaEEEeeeEEEiiiOOOOooooUUUuuucC".split(""));

    return input.replaceAllMapped(matchRE, (m) => replacementMap[m[0]!]!);
  }

  static String enumName(String enumToString) {
    List<String> paths = enumToString.split(".");
    return paths[paths.length - 1];
  }

  static String onlyNumber(String str) {
    final numberRegex = RegExp(r'[0-9]', multiLine: true);
    String strOnlyNumber = '';
    Iterable matches = numberRegex.allMatches(str);
    matches.forEach((match) {
      strOnlyNumber += str.substring(match.start, match.end);
    });
    return strOnlyNumber;
  }

  static String formatThousands(int value) {
    NumberFormat formatter = NumberFormat("###,###,###", "eu");
    return formatter.format(value);
  }

  static bool validateEmail(String value) {
    Pattern pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = new RegExp(pattern as String);
    if (!regex.hasMatch(value)) return false;
    return true;
  }

  static bool validateCpf(String strCPF) {
    if (strCPF.length < 11) return false;
    int soma = 0;
    int resto = 0;
    soma = 0;
    if (strCPF == "00000000000" ||
        strCPF == "11111111111" ||
        strCPF == "22222222222" ||
        strCPF == "3333333333" ||
        strCPF == "44444444444" ||
        strCPF == "55555555555" ||
        strCPF == "66666666666" ||
        strCPF == "77777777777" ||
        strCPF == "88888888888" ||
        strCPF == "99999999999") {
      return false;
    }

    for (int i = 1; i <= 9; i++)
      soma = soma + int.parse(strCPF.substring(i - 1, i)) * (11 - i);
    resto = (soma * 10) % 11;

    if ((resto == 10) || (resto == 11)) resto = 0;
    if (resto != int.parse(strCPF.substring(9, 10))) return false;

    soma = 0;
    for (int i = 1; i <= 10; i++)
      soma = soma + int.parse(strCPF.substring(i - 1, i)) * (12 - i);
    resto = (soma * 10) % 11;

    if ((resto == 10) || (resto == 11)) resto = 0;
    if (resto != int.parse(strCPF.substring(10, 11))) {
      return false;
    } else {
      return true;
    }
  }

  static String formatMonth(int mes) {
    return mes < 13 && mes > 0
        ? [
            'Janeiro',
            'Fevereiro',
            'Março',
            'Abril',
            'Maio',
            'Junho',
            'Julho',
            'Agosto',
            'Setembro',
            'Outubro',
            'Novembro',
            'Dezembro'
          ][mes - 1]
        : '';
  }

  static String pontuationSeparator(int value) {
    return value.toString().replaceAllMapped(
        new RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]}.');
  }

  static String ptBrValue(double value) {
    final f = NumberFormat("###0.0#", "pt_BR");
    return '${f.format(value)}%';
  }

  static String glasgowFormat(String value) {
    return value
        .toString()
        .replaceAllMapped(RegExp(r'^[0-9]'), (Match m) => '');
  }
}

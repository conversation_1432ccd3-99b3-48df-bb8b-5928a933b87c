import 'package:ambulancia_app/bloc/attendance/attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/closse_attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_team/attendance_team_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/conduct/conduct_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/models/conduct_model.dart';
import 'package:ambulancia_app/models/config_app_ambulancia_constants_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/conduct/assistance-protocols/assistance_protocols_attendance.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/conduct/close_attendance/modal_reclassification.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/conduct/conduc-attachment/item_attachment.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/pre_hospital_discharge/pre_hospital_discharge_screen.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';
import 'package:ambulancia_app/shared/utils/photo.constants.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/buttons/primary_button.dart';
import 'package:ambulancia_app/shared/widgets/modal/modal_photo.dart';
import 'package:ambulancia_app/shared/widgets/modal/modal_photo_medical.dart';
import 'package:ambulancia_app/shared/widgets/title/title_form.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class AttendanceConduct extends StatefulWidget {
  final AttendanceModel attendanceModel;
  AttendanceConduct({required this.attendanceModel});
  @override
  _AttendanceConductState createState() => _AttendanceConductState();
}

class _AttendanceConductState extends State<AttendanceConduct> {
  final AutocompleteOptionToString displayStringForOption =
      RawAutocomplete.defaultStringForOption;
  TextEditingController descriptionController = TextEditingController();
  TextEditingController cidController = TextEditingController();
  TextEditingController observationController = TextEditingController();
  final _formKeyConduct = GlobalKey<FormState>();

  CidRecordSQLite? cidSelected;
  late List<CidRecordSQLite> cidList;

  int? step;
  bool formChanged = false;
  bool pendingAttachment = false;
  final _baseTranslate = 'conductForm';
  int? _indexMovement;

  @override
  void initState() {
    context.read<ConductCubit>().cleanListAttachment();

    super.initState();
    _indexMovement =
        context.read<AttendanceMovementsCubit>().getCurrentIndexStatusApp();

    SchedulerBinding.instance.addPostFrameCallback((_) {
      observationController.text = widget.attendanceModel.observacaoConduta;

      descriptionController.text = widget.attendanceModel.descricaoTerapeutica;

      verifyContent();
    });
  }

  void verifyContent() {
    if (widget.attendanceModel.codDiagnostico != "") {
      cidSelected = CidRecordSQLite(
          codCid: widget.attendanceModel.codDiagnostico,
          dvCid: "",
          descricao: widget.attendanceModel.descricaoDiagnostico);
      cidController.text = _labelDescription(cidSelected);
    }

    context.read<ConductCubit>().listCidDb("");
  }

  String _labelDescription(CidRecordSQLite? cidSelected) {
    if (cidSelected == null) return '';
    if (cidSelected.codCid == null || cidSelected.descricao == null) return '';
    return '${cidSelected.codCid} - ${cidSelected.descricao}';
  }

  @override
  Widget build(BuildContext context) {
    ConfigAppAmbulanciaConstants _configAppAmbulanciaConstants =
        context.read<AuthCubit>().configAppAmbulanciaConstants;
    return Form(
      key: _formKeyConduct,
      child: Column(
        children: [
          Expanded(
            child: ListView(
              shrinkWrap: true,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TitleForm(
                      title: I18nHelper.translate(
                          context, '$_baseTranslate.title'),
                    ),
                    _configAppAmbulanciaConstants
                            .hospitalDischarge!.hospitalDischargeButtonVisible
                        ? Visibility(
                            visible: (_indexMovement == 5 ||
                                    _indexMovement ==
                                        AttendanceTimelineStepsEnum
                                            .newAttendance.index) &&
                                (widget.attendanceModel.observacaoConduta
                                    .isNotEmpty) &&
                                ((_validateServiceTeamProfile() == true &&
                                        widget.attendanceModel
                                            .descricaoDiagnostico.isNotEmpty) ||
                                    (_validateServiceTeamProfile() == false)),
                            child: PrimaryButton(
                              text: I18nHelper.translate(context,
                                      '$_baseTranslate.preHospitalDischarge')
                                  .toUpperCase(),
                              onPressed: () {
                                final bool hasUnsavedChanges =
                                    descriptionController.text !=
                                            widget.attendanceModel
                                                .descricaoTerapeutica ||
                                        cidController.text !=
                                            _validateUnsavedBehaviorChanges() ||
                                        observationController.text !=
                                            widget.attendanceModel
                                                .observacaoConduta;

                                if (hasUnsavedChanges) {
                                  _alertError(
                                    I18nHelper.translate(context,
                                        '$_baseTranslate.alertTextNoSave'),
                                  );
                                } else {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) =>
                                        PreHospitalDischargeScreen(
                                      attendanceModel: widget.attendanceModel,
                                    ),
                                  );
                                }
                              },
                            ),
                          )
                        : _btnCloseService(context),
                  ],
                ),
                SizedBox(height: 10),
                Text(
                  I18nHelper.translate(context, '$_baseTranslate.diagnosis') +
                      (_validateServiceTeamProfile() ? '*' : ''),
                  style: TextStyle(fontSize: 18),
                ),
                SizedBox(height: 5),
                _selectCid(),
                _divider(),
                ..._itemTile(
                  key: Key('description'),
                  onChangedAction: () {},
                  label: I18nHelper.translate(
                      context, '$_baseTranslate.description'),
                  isReading: false,
                  value: widget.attendanceModel.descricaoTerapeutica,
                  controller: descriptionController,
                ),
                _divider(),
                ..._itemTile(
                    key: Key('observation'),
                    onChangedAction: () {},
                    label:
                        I18nHelper.translate(context, '$_baseTranslate.note'),
                    isReading: false,
                    controller: observationController,
                    validator: (value) {
                      if (value!.isEmpty)
                        return I18nHelper.translate(
                            context, '$_baseTranslate.requiredField');
                      else
                        return null;
                    }),
                _divider(),
                ItemAttachment(attendanceModel: widget.attendanceModel),
                _divider(),
                _progressIndicator(),
                _divider(),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 0, top: 15),
            child: _buttonGroup(context),
          )
        ],
      ),
    );
  }

  Widget _progressIndicator() {
    return BlocBuilder<ConductCubit, ConductState>(
      buildWhen: (preState, state) {
        if (state is SuccessSendConductClosureState) {
          context.read<AttendanceMovementsCubit>().updateStatusMovement(
              attendance: '${widget.attendanceModel.numAtendimento}',
              indexStatusApp: AttendanceTimelineStepsEnum.closure.index);
        }
        if (state is ErrorSendConductClosureState) {
          _alertError(state.message);
        }
        return true;
      },
      builder: (context, state) {
        if (state is LoadingSendConductState)
          return SpinKitThreeBounce(color: AmbulanceColors.green);
        else
          return Container();
      },
    );
  }

  Widget _selectCid() {
    return BlocListener<ConductCubit, ConductState>(
      listener: (context, state) {
        if (state is LoadedListCidDBState) {
          this.setState(() {
            cidList = List.from(state.list);
          });
        }

        if (state is LoadedListCidDBState || state is ErrorLoadListCidDBState) {
          context.read<ConductCubit>().sendAttachment(
                numAtendimento: widget.attendanceModel.numAtendimento,
              );
        }
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 16.0),
        child: Container(
          child: Autocomplete<CidRecordSQLite>(
            fieldViewBuilder: (BuildContext context,
                TextEditingController fieldTextEditingController,
                FocusNode fieldFocusNode,
                VoidCallback onFieldSubmitted) {
              return TextFormField(
                key: Key('text_field_cid'),
                decoration: InputDecoration(
                  suffixIcon: IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () {
                      cidController.clear();
                      setState(() {
                        cidSelected = null;
                      });
                      formChanged = true;
                    },
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.green),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.green),
                  ),
                  contentPadding: EdgeInsets.fromLTRB(15.0, 15.0, 20.0, 15.0),
                ),
                style: TextStyle(color: AmbulanceColors.grayDark, fontSize: 18),
                controller: cidController,
                focusNode: fieldFocusNode,
                onChanged: (value) {
                  fieldTextEditingController.text = value;
                  formChanged = true;
                  if (value.isEmpty) {
                    cidSelected = null;
                  }
                },
                validator: _validateServiceTeamProfile()
                    ? (value) {
                        if (value!.isEmpty)
                          return I18nHelper.translate(
                              context, '$_baseTranslate.requiredField');
                        else
                          return null;
                      }
                    : null,
              );
            },
            optionsBuilder: (TextEditingValue textEditingValue) {
              if (textEditingValue.text == '') {
                return const Iterable<CidRecordSQLite>.empty();
              }
              final searchText = textEditingValue.text.toLowerCase();

              final filteredList = cidList.where((cid) {
                final descricao = cid.descricao!.toLowerCase();
                final codCid = cid.codCid!.toLowerCase();

                return descricao.contains(searchText) ||
                    codCid.contains(searchText);
              }).toList();

              final limitedList = filteredList.take(15).toList();

              return limitedList;
            },
            displayStringForOption: (CidRecordSQLite cid) {
              return _labelDescription(cid);
            },
            optionsViewBuilder: (context, onSelected, options) {
              return Align(
                alignment: Alignment.topLeft,
                child: Material(
                  elevation: 4.0,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxHeight: 200),
                    child: ListView.builder(
                      key: const Key('list_cids'),
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: options.length,
                      itemBuilder: (BuildContext context, int index) {
                        final option = options.elementAt(index);
                        return InkWell(
                          key: ValueKey("cids_$index"),
                          onTap: () {
                            onSelected(option);
                          },
                          child: Builder(builder: (BuildContext context) {
                            final bool highlight =
                                AutocompleteHighlightedOption.of(context) ==
                                    index;
                            if (highlight) {
                              SchedulerBinding.instance
                                  .addPostFrameCallback((Duration timeStamp) {
                                Scrollable.ensureVisible(context,
                                    alignment: 0.5);
                              });
                            }
                            return Container(
                              color: highlight
                                  ? Theme.of(context).focusColor
                                  : null,
                              padding: const EdgeInsets.all(16.0),
                              child: Text(displayStringForOption(
                                  _labelDescription(option))),
                            );
                          }),
                        );
                      },
                    ),
                  ),
                ),
              );
            },
            onSelected: (CidRecordSQLite selection) {
              this.setState(
                () {
                  cidSelected = selection;
                  cidController.text = _labelDescription(selection);
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _divider() {
    return SizedBox(height: ConstantsTheme.doublePadding * 1.5);
  }

  List<Widget> _itemTile({
    required String label,
    String? value,
    TextEditingController? controller,
    required bool isReading,
    Function? onChangedAction,
    String? Function(String?)? validator,
    Key? key,
  }) {
    return [
      Text(
        label,
        style: TextStyle(fontSize: 20),
      ),
      SizedBox(height: ConstantsTheme.padding * 0.5),
      TextFormField(
        key: key,
        scrollPhysics: ClampingScrollPhysics(),
        textAlign: TextAlign.left,
        textAlignVertical: TextAlignVertical.top,
        keyboardType: TextInputType.multiline,
        maxLines: 15,
        controller: controller,
        readOnly: isReading,
        onChanged: (v) {
          formChanged = true;
          if (onChangedAction != null) onChangedAction();
        },
        textInputAction: TextInputAction.newline,
        inputFormatters: [LengthLimitingTextInputFormatter(2000)],
        onFieldSubmitted: (term) {},
        decoration: InputDecoration(
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AmbulanceColors.green),
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(color: AmbulanceColors.green),
          ),
          contentPadding: EdgeInsets.fromLTRB(15.0, 15.0, 20.0, 15.0),
        ),
        style: TextStyle(color: AmbulanceColors.grayDark, fontSize: 18),
        validator: validator,
      ),
    ];
  }

  Widget _btnCloseService(BuildContext context) {
    return BlocListener<CloseAttendanceCubit, CloseAttendanceState>(
      listener: (context, state) {
        if (state is CloseAttendanceSuccesState) {
          Navigator.popUntil(context, (route) => route.isFirst);
        }
      },
      child: BlocListener<AttendanceMovementsCubit, AttendanceMovementsState>(
        listener: (context, state) {
          if (state is UpdatedMovementsState) {
            final currentAttendance =
                context.read<AttendanceCubit>().currentAttendance();
            context
                .read<AttendanceListCubit>()
                .updateAttendanceLocalList(attendance: currentAttendance);
            setState(
              () {
                _indexMovement = state.indexStatusApp;
              },
            );
          }
        },
        child: Visibility(
          visible: (_indexMovement == 5 ||
                      _indexMovement ==
                          AttendanceTimelineStepsEnum.newAttendance.index) &&
                  (widget.attendanceModel.observacaoConduta != "")
              ? true
              : false,
          child: ElevatedButton(
            key: Key('button_end_service'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.redClose,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: (_indexMovement == 5 ||
                        _indexMovement ==
                            AttendanceTimelineStepsEnum.newAttendance.index) &&
                    (widget.attendanceModel.observacaoConduta.isNotEmpty)
                ? () {
                    if (descriptionController.text !=
                            (widget.attendanceModel.descricaoTerapeutica.isEmpty
                                ? ""
                                : widget
                                    .attendanceModel.descricaoTerapeutica) ||
                        (cidController.text !=
                            _validateUnsavedBehaviorChanges()) ||
                        (observationController.text !=
                            widget.attendanceModel.observacaoConduta)) {
                      _alertError(
                        I18nHelper.translate(
                            context, '$_baseTranslate.alertTextNoSave'),
                      );
                    } else {
                      if (widget.attendanceModel.uniurgProtocoloDoencas !=
                          null) {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AssistanceProtocolsAttendancePopup(
                              attendanceModel: widget.attendanceModel,
                            );
                          },
                        );
                      } else if (widget
                          .attendanceModel.solicitaReclassifTipoAtend) {
                        showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return ReclassifyPopup(
                              attendanceModel: widget.attendanceModel,
                            );
                          },
                        );
                      } else {
                        openModalPhotoMedical(
                          context: context,
                          attendanceModel: widget.attendanceModel,
                          recordType: 'Encerramento',
                        );
                      }
                    }
                  }
                : null,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 54),
              child: Text(
                I18nHelper.translate(context, '$_baseTranslate.closeService'),
                style: TextStyle(fontSize: 20),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buttonGroup(BuildContext context) {
    return BlocListener<ConductCubit, ConductState>(
      listener: (context, state) {
        if (state is SuccessDeleteAttachmentState) {
          pendingAttachment = true;
        }
        if (state is SuccessSendPhotoOperator) {
          context.read<ConductCubit>().sendConductEvent(
                conductModel: ConductModel(
                  numAtendimento: widget.attendanceModel.numAtendimento,
                  observacaoConduta: observationController.text,
                  codDiagnostico:
                      cidSelected != null ? cidSelected!.codCid ?? "" : "",
                  descricaoDiagnostico:
                      cidSelected != null ? cidSelected!.descricao ?? "" : "",
                  descricaoTerapeutica: descriptionController.text,
                  attachments: context.read<ConductCubit>().getAttachments(),
                  operatorImage: state.imageOperator,
                ),
              );
        }

        if (state is SuccessAttachmentState) {
          state.listAttachment.forEach((attachmentImage) {
            if (attachmentImage.isNewAttachment && attachmentImage.isCache) {
              pendingAttachment = true;
            }
          });
        }

        if (state is ErrorSendConductState) {
          context.read<ConductCubit>().amitListAttachment();
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.greenChart,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: () {
              handleFormValidation(context: context);
            },
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 54),
                child: Text(
                  I18nHelper.translate(context, '$_baseTranslate.save'),
                  style: TextStyle(fontSize: 20),
                )),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.redClose,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: () {
              if (formChanged || pendingAttachment) {
                _alertDiscardChanges(
                  context: context,
                  attendaceModel: widget.attendanceModel,
                  recordType: 'Conduta',
                );
              } else {
                Navigator.pop(context);
              }
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 54),
              child: Text(
                I18nHelper.translate(context, '$_baseTranslate.close'),
                style: TextStyle(fontSize: 20),
              ),
            ),
          )
        ],
      ),
    );
  }

  void _alertError(text) {
    Alert.open(
      context,
      textButtonClose: I18nHelper.translate(context, '$_baseTranslate.close'),
      title: I18nHelper.translate(context, '$_baseTranslate.alert'),
      text: text,
    );
  }

  void _alertDiscardChanges({required context, attendaceModel, recordType}) {
    Alert.open(
      context,
      buttonCloseColor: AmbulanceColors.green,
      title:
          I18nHelper.translate(context, '$_baseTranslate.warningDiscard.title'),
      text:
          I18nHelper.translate(context, '$_baseTranslate.warningDiscard.text'),
      textButtonClose:
          I18nHelper.translate(context, '$_baseTranslate.warningDiscard.save'),
      callbackClose: () {
        handleFormValidation(context: context);
      },
      actions: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.redClose,
              textStyle: TextStyle(
                color: Colors.white,
              )),
          child: Text(I18nHelper.translate(
              context, '$_baseTranslate.warningDiscard.discard')),
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }

  bool _validateServiceTeamProfile() {
    if (widget.attendanceModel.equipe.isEmpty) {
      return false;
    }

    for (var funcionario in widget.attendanceModel.equipe) {
      if (funcionario.codFuncao == CodFuncoes.MEDICO_SOCORRISTA) {
        return true;
      }
    }

    if (context.read<AttendanceTeamCubit>().selectDoctorModel != null &&
        context
                .read<AttendanceTeamCubit>()
                .selectDoctorModel!
                .numbAtendimento ==
            widget.attendanceModel.numAtendimento.toString() &&
        context
                .read<AttendanceTeamCubit>()
                .selectDoctorModel!
                .selectedDoctor
                .codFuncionario !=
            null) {
      return true;
    }

    return false;
  }

  String _validateUnsavedBehaviorChanges() {
    if (cidSelected != null) {
      return '${cidSelected!.codCid} - ${cidSelected!.descricao}';
    } else {
      return "";
    }
  }

  void handleFormValidation({required BuildContext context}) {
    if (_formKeyConduct.currentState!.validate()) {
      if (cidController.text.isNotEmpty) {
        if (cidController.text != _validateUnsavedBehaviorChanges() ||
            cidSelected == null) {
          return _alertError(
            I18nHelper.translate(
                context, '$_baseTranslate.alertTextInvalidDiagnosis'),
          );
        }
      }
      if (_validateServiceTeamProfile()) {
        if (cidController.text == "" || cidSelected == null) {
          return _alertError(
              I18nHelper.translate(context, '$_baseTranslate.warningText'));
        }
      }
      if (formChanged || pendingAttachment) {
        openModalPhotoRegister(
          context: context,
          model: widget.attendanceModel,
          recordType: 'Conduta',
          reasonAnnexPhoto: photoReasonAnnexEnum.conduta.value,
        );
      } else {
        Navigator.pop(context);
      }
    }
  }
}

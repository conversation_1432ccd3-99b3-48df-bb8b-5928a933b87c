import 'funcionario_model.dart';

class TeamAttendanceModel {
  List<FuncionarioModel>? equipe;

  TeamAttendanceModel({this.equipe});

  TeamAttendanceModel.fromJson(Map<String, dynamic> json) {
    if (json['equipe'] != null) {
      equipe = [];
      json['equipe'].forEach((v) {
        equipe!.add(new FuncionarioModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.equipe != null) {
      data['equipe'] = this.equipe!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

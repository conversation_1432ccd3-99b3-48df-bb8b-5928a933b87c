import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AttendanceCardHeader extends StatelessWidget {
  final AttendanceModel attendance;
  final bool statusCache;
  AttendanceCardHeader(
      {Key? key, required this.attendance, this.statusCache = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: AmbulanceColors.greenLight,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(ConstantsTheme.borderRadius),
                topRight: Radius.circular(ConstantsTheme.borderRadius),
                bottomRight: Radius.circular(0),
              ),
            ),
            child: FittedBox(
              fit: BoxFit.fill,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: ConstantsTheme.doublePadding),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Nº',
                          style: TextStyle(
                            fontSize: 15,
                            color: AmbulanceColors.green,
                          ),
                        ),
                        SizedBox(width: ConstantsTheme.doublePadding),
                        Container(
                          padding: EdgeInsets.only(
                            top: ConstantsTheme.padding,
                            bottom: ConstantsTheme.padding,
                            left: ConstantsTheme.doublePadding,
                            right: ConstantsTheme.padding,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(
                                ConstantsTheme.borderRadius),
                          ),
                          child: Text(
                            attendance.numAtendimento.toString(),
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                        SizedBox(width: ConstantsTheme.doublePadding),
                        Text(
                          _getNameStatus(context)!,
                          style: TextStyle(
                            fontSize: 21,
                            fontWeight: FontWeight.w800,
                            color: AmbulanceColors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                        vertical: ConstantsTheme.padding,
                        horizontal: ConstantsTheme.doublePadding),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: attendanceColor[attendance.codTipoAtendimento],
                      borderRadius: BorderRadius.only(
                          topRight:
                              Radius.circular(ConstantsTheme.borderRadius),
                          bottomRight: Radius.circular(0)),
                    ),
                    child: Text(
                      attendance.codTipoAtendimento,
                      style: TextStyle(
                        fontSize: 26,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  String? _getNameStatus(context) {
    if (!statusCache) {
      final index = AttendanceStatus.statusApi2StatusApp(attendance.codStatus);
      if (index >= 0) {
        final nomeStatusApp =
            attendanceTimelineComplete.entries.toList()[index].value;
        return nomeStatusApp.replaceAll('\n', ' ');
      } else
        return attendance.nomeStatus;
    }

    final index =
        BlocProvider.of<AttendanceMovementsCubit>(context, listen: true)
            .getCurrentIndexStatusApp();
    if (index == null || index < 0) {
      return attendance.nomeStatus;
    } else {
      final nomeStatusApp =
          attendanceTimelineComplete.entries.toList()[index].value;
      return nomeStatusApp.replaceAll('\n', ' ');
    }
  }
}

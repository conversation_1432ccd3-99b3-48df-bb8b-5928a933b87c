buildscript {
    ext.kotlin_version = '2.1.10'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.4'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.0'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}

// subprojects {
//         afterEvaluate { project ->
//             if (project.hasProperty('android')) {
//                 project.android { compileSdkVersion 34 }
//             }
//         }
// }

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

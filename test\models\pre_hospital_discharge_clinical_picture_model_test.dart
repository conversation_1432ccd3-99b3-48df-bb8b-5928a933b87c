import 'package:ambulancia_app/models/pre_hospital_discharge_clinical_picture_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PreHospitalDischargeClinicalPictureModel', () {
    test('fromJson deve criar uma instância corretamente', () {
      final json = {
        'code': 1,
        'nameClinicalPicture': 'Dor torácica',
      };

      final model = PreHospitalDischargeClinicalPictureModel.fromJson(json);

      expect(model.code, 1);
      expect(model.nameClinicalPicture, 'Dor torácica');
    });

    test('toJson deve retornar um mapa correto', () {
      final model = PreHospitalDischargeClinicalPictureModel(
        code: 2,
        nameClinicalPicture: 'Dispneia',
      );

      final json = model.toJson();

      expect(json['code'], 2);
      expect(json['nameClinicalPicture'], 'Dispneia');
    });
  });
}

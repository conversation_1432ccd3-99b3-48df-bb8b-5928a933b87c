import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

class ClinicalRadio extends StatefulWidget {
  final String text;
  final Color textColor;
  final title;
  final Function(bool?)? onPress;
  final forceValue;
  ClinicalRadio({
    Key? key,
    this.text = '',
    this.textColor = AmbulanceColors.green,
    this.title = '',
    this.onPress,
    this.forceValue,
  }) : super(key: key);

  @override
  _ClinicalRadioState createState() => _ClinicalRadioState();
}

class _ClinicalRadioState extends State<ClinicalRadio> {
  bool? isChecked = false;

  @override
  Widget build(BuildContext context) {
    if (widget.forceValue != null) isChecked = widget.forceValue;
    return InkWell(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: TextStyle(
              color: widget.textColor,
            ),
          ),
          SizedBox(height: 5),
          Row(
            children: [
              Flexible(
                child: ClipOval(
                  child: Icon(
                    isChecked!
                        ? Icons.radio_button_checked
                        : Icons.radio_button_unchecked_outlined,
                    color: widget.textColor,
                    size: 35,
                  ),
                ),
              ),
              SizedBox(width: 5),
              FittedBox(
                fit: BoxFit.fill,
                child: Text(
                  widget.text,
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: 20,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
      onTap: () {
        setState(() => isChecked = !isChecked!);
        if (widget.onPress != null) widget.onPress!(isChecked);
      },
    );
  }
}

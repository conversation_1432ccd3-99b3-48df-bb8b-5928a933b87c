import 'package:ambulancia_app/models/reponse-table.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  ResponseTableModel? testResponseTableModel;
  Map<String, dynamic>? jsonResponseTableModel;
  setUpAll(
    () {
      testResponseTableModel =
          ResponseTableModel(value: "1", customId: "1", category: "1");

      jsonResponseTableModel = {
        "value": "1",
        "custom_id": "1",
        "category": "1",
      };
    },
  );

  group(
    "isInstanceOf ResponseTableModel model tests",
    () {
      test("Should be return instance of ResponseTableModel", () {
        expect(testResponseTableModel, isInstanceOf<ResponseTableModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of ResponseTableModel to json", () {
      expect(testResponseTableModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of ResponseTableModel from json", () {
      expect(ResponseTableModel.fromJson(jsonResponseTableModel!),
          isInstanceOf<ResponseTableModel>());
    });
  });

  group(
    "isInstanceOf ResponseTableModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonResponseTableModel!["value"], isInstanceOf<String>());
        expect(jsonResponseTableModel!["custom_id"], isInstanceOf<String>());
        expect(jsonResponseTableModel!["category"], isInstanceOf<String>());
      });
    },
  );
}

import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/subscription/cubit/subscription_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/models/movements_model.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/buttons/unimed_buttons.dart';
import 'package:ambulancia_app/shared/widgets/dashed_rect.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AmbulanceStepper extends StatefulWidget {
  final int? currentStep;
  final AttendanceModel? attendance;
  final Function? getCodStatus;

  AmbulanceStepper({
    this.currentStep,
    this.attendance,
    this.getCodStatus,
  });

  @override
  _AmbulanceStepperState createState() => _AmbulanceStepperState();
}

class _AmbulanceStepperState extends State<AmbulanceStepper> {
  static const double _size = 50.0;
  static const double _ambulanceSize = 50.0;
  double _ambulancePosition = 0.0;
  int _duration = 350;
  List<GlobalKey> _keysSteps = [];
  int currentStep = 0;
  late bool _isButtonDisabled;
  int? allowJump = 0;
  final _baseTranslateWarningNotInit = 'attendanceScreen.warningNotInit';
  final _baseTranslateServiceAlreadyHasThisStatus =
      'attendanceScreen.serviceAlreadyHasThisStatus';
  final _baseTranslateAlertConfirmStatusUpdate =
      'attendanceScreen.alertConfirmStatusUpdate';

  int stepTemp = 0;

  @override
  void initState() {
    super.initState();
    _isButtonDisabled = false;
    _initKeys();
  }

  void _initKeys() async {
    attendanceTimelineSteps.entries.forEach((element) async {
      _keysSteps.add(GlobalKey());
    });

    await context
        .read<AttendanceMovementsCubit>()
        .getAttendanceMovements(widget.attendance!);

    if (widget.attendance!.codStatus == AttendanceStatus.NOVO_ATENDIMENTO) {
      MovementsModel? _movimentHistory =
          await context.read<AttendanceMovementsCubit>().getMovements();
      if (_movimentHistory != null) {
        stepTemp = AttendanceStatus.statusApi2StatusApp((_movimentHistory
                .moviments[_movimentHistory.moviments.length - 2].statusCode)!
            .toInt());
      }
    } else {
      stepTemp =
          AttendanceStatus.statusApi2StatusApp(widget.attendance!.codStatus);
    }

    if (stepTemp != -1) {
      currentStep = stepTemp;
      allowJump = currentStep;
    }
    allowJump = widget.attendance!.codStatus;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _moveAmbulance(currentStep);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AttendanceMovementsCubit, AttendanceMovementsState>(
      listener: (context, state) {
        if (state is ErrorMovementsState) {
          Alert.open(context, text: state.message!);
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _isButtonDisabled = false;
            });
          });
        }
      },
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(ConstantsTheme.padding),
            child: Stack(
              children: [
                AnimatedPositioned(
                  left: _ambulancePosition,
                  curve: Curves.ease,
                  child: Container(
                    height: _ambulanceSize,
                    child: Image.asset('assets/images/ambulance.png'),
                  ),
                  duration: Duration(milliseconds: _duration),
                ),
                Container(
                  decoration: BoxDecoration(
                      border: Border(
                    top: BorderSide(
                      color: AmbulanceColors.border,
                      width: 1.0,
                    ),
                  )),
                  margin: EdgeInsets.only(top: _ambulanceSize),
                  padding: EdgeInsets.only(top: ConstantsTheme.padding),
                  child: _steps(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _steps() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(
        attendanceTimelineSteps.length,
        (index) => _step(index),
      ).toList(),
    );
  }

  Widget _step(indexClicked) {
    return Flexible(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          key: Key('item_step_$indexClicked'),
          onTap: !_isButtonDisabled
              ? () => _onTapStep(context, indexClicked, currentStep)
              : null,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      if (indexClicked == 0) _emptySpace(),
                      if (indexClicked > 0) _divider(indexClicked),
                      _buildStepCircle(indexClicked),
                      if (indexClicked < attendanceTimelineSteps.length - 1)
                        _divider(indexClicked),
                      if (indexClicked == attendanceTimelineSteps.length - 1)
                        _emptySpace(),
                    ],
                  ),
                  Text(
                    attendanceTimelineSteps.entries
                        .toList()[indexClicked]
                        .value,
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16),
                  ),
                  _buildTimeCircles(indexClicked),
                ],
              ),
              if (currentStep == indexClicked) _selectedSpotlight(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepCircle(int index) {
    return BlocBuilder<AttendanceMovementsCubit, AttendanceMovementsState>(
      buildWhen: (preState, state) {
        if (state is UpdatedMovementsState) {
          if (state.indexStatusApp != 6) {
            _updateSteps(state.indexStatusApp);
            widget.getCodStatus!(state.codeStatus);
          }
        }
        return true;
      },
      builder: (context, state) {
        if (state is UpdatingMovementsState) {
          if (state.indexStatusApp == index)
            return CircularProgressIndicator();
          else
            return _stepCircle(index);
        } else
          return _stepCircle(index);
      },
    );
  }

  Widget _stepCircle(index) {
    final dataFormatted = BlocProvider.of<AttendanceMovementsCubit>(context)
        .getMovementByIndex(index);
    return Container(
      key: _keysSteps[index],
      alignment: Alignment.center,
      height: _size,
      width: _size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: _getBackgroundColorCircle(index, dataFormatted),
        border: Border.all(
          color: _getBorderColorCircle(index),
          width: 2,
        ),
      ),
      child: Text(
        '${index + 1}',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 25,
          fontWeight:
              currentStep == index ? FontWeight.w600 : FontWeight.normal,
          color: _getTextColorCircle(index, dataFormatted),
        ),
      ),
    );
  }

  Widget _buildTimeCircles(index) {
    return BlocBuilder<AttendanceMovementsCubit, AttendanceMovementsState>(
      builder: (context, state) {
        if (state is LoadingMovementsState)
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40.0),
            child: LinearProgressIndicator(),
          );
        else
          return _getTimeCachedBloc(index);
      },
    );
  }

  Widget _getTimeCachedBloc(index) {
    final dataFormatted =
        BlocProvider.of<AttendanceMovementsCubit>(context, listen: true)
            .getMovementByIndex(index);
    return Text(
      '${dataFormatted ?? '\n'}',
      textAlign: TextAlign.center,
      style: TextStyle(
        color: AmbulanceColors.greenDark,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _divider(int index) {
    final dataFormatted =
        BlocProvider.of<AttendanceMovementsCubit>(context, listen: true)
            .getMovementByIndex(index);

    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            width: 1,
            color: dataFormatted == null && index < currentStep
                ? Colors.white
                : AmbulanceColors.greenLight3,
          ),
        ),
        child: dataFormatted == null && index < currentStep
            ? DashedRect(
                color: Colors.grey,
                strokeWidth: 3.0,
                gap: 5.0,
              )
            : Container(),
      ),
    );
  }

  Widget _emptySpace() {
    return Expanded(
      child: Container(
          decoration: BoxDecoration(
        color: Colors.white,
      )),
    );
  }

  _getBackgroundColorCircle(index, dataFormatted) {
    if (index == currentStep)
      return AmbulanceColors.greenLight;
    else if (index < currentStep) {
      if (dataFormatted == null)
        return AmbulanceColors.grayLight3;
      else
        return AmbulanceColors.greenDark;
    } else if (index > currentStep) {
      return Colors.white;
    }
  }

  _getBorderColorCircle(index) =>
      index > currentStep ? AmbulanceColors.greenChart : Colors.transparent;

  _getTextColorCircle(index, dataFormatted) => index >= currentStep
      ? AmbulanceColors.greenChart
      : dataFormatted == null
          ? AmbulanceColors.grayLight2
          : Colors.white;

  Widget _selectedSpotlight() {
    return Container(
      height: _size * 2.6,
      width: _size * 2.6,
      alignment: Alignment.center,
      margin: EdgeInsets.only(right: 5, bottom: 5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
        boxShadow: [
          CustomBoxShadow(
            color: AmbulanceColors.greenLight,
            offset: Offset(5.0, 5.0),
            blurRadius: 10.0,
            blurStyle: BlurStyle.outer,
          )
        ],
      ),
    );
  }

  void _updateSteps(int? index) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (index != AttendanceStatus.NOVO_ATENDIMENTO) {
        _moveAmbulance(index!);
      }
      if (this.mounted) {
        setState(() {
          currentStep = index!;
          _isButtonDisabled = false;
          allowJump = AttendanceStatus.statusApp2StatusApi(currentStep);
        });
      }
    });
  }

  void _moveAmbulance(int index) {
    final RenderBox _box =
        _keysSteps[index].currentContext!.findRenderObject() as RenderBox;
    final Offset _position = _box.localToGlobal(Offset.zero);
    final int _dur = (index - widget.currentStep!) * 350;

    setState(() {
      _ambulancePosition = _position.dx - 30;
      _duration = index == 0 ? 350 : _dur;
    });
  }

  void _onTapStep(BuildContext context, int indexClicked, int currentStep) {
    if (widget.attendance!.codStatus != AttendanceStatus.NOVO_ATENDIMENTO) {
      if (AttendanceStatus.verifyInitAttendance(indexClicked, allowJump)) {
        if (stepTemp != -1 && indexClicked == currentStep) {
          Alert.open(
            context,
            title: I18nHelper.translate(
                context, '$_baseTranslateServiceAlreadyHasThisStatus.title'),
            text: I18nHelper.translate(
                context, '$_baseTranslateServiceAlreadyHasThisStatus.text'),
          );
          return;
        } else if (indexClicked < currentStep) {
          Alert.open(
            context,
            title: I18nHelper.translate(
                context, '$_baseTranslateServiceAlreadyHasThisStatus.title'),
            text: I18nHelper.translate(
                context, '$_baseTranslateServiceAlreadyHasThisStatus.text'),
          );
        } else {
          if (indexClicked != 0) {
            _statusUpdateConfirmationAlert(indexClicked: indexClicked);
          } else {
            _isButtonDisabled = true;
            context.read<AttendanceMovementsCubit>().updateStatusMovement(
                  attendance: '${widget.attendance!.numAtendimento}',
                  indexStatusApp: indexClicked,
                );

            if (indexClicked == 1 || indexClicked == 5) {
              Future.delayed(
                Duration.zero,
                () {
                  context.read<SubscriptionCubit>().verifySubscription(
                        widget.attendance!.numAtendimento,
                        AttendanceStatus.statusApp2StatusApi(indexClicked),
                      );
                },
              );
            }
          }
        }
      } else {
        Alert.open(
          context,
          title: I18nHelper.translate(
              context, '$_baseTranslateWarningNotInit.title'),
          text: I18nHelper.translate(
              context, '$_baseTranslateWarningNotInit.text'),
        );
      }
    } else {
      Alert.open(
        context,
        title: I18nHelper.translate(
            context, '$_baseTranslateWarningNotInit.title'),
        text:
            'Não foi possível atualizar o status desse atendimento, pois foi cadastrado um novo atendimento.',
      );
    }
  }

  String _serviceStatusTitle({required int indexStatus}) {
    switch (indexStatus) {
      case 0:
        return _formatedTextStatusAlert(title: "Início do Atendimento");
      case 1:
        return _formatedTextStatusAlert(title: "Chegada à Origem");
      case 2:
        return _formatedTextStatusAlert(title: "Saída da Origem");
      case 3:
        return _formatedTextStatusAlert(title: "Chegada ao Destino");
      case 4:
        return _formatedTextStatusAlert(title: "Saída do Destino");
      case 5:
        return _formatedTextStatusAlert(title: "Chegada à Base");
      default:
        return "";
    }
  }

  String _formatedTextStatusAlert({required String title}) {
    return '"$title"';
  }

  void _statusUpdateConfirmationAlert({required int indexClicked}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.topCenter,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
            ),
            insetPadding: EdgeInsets.only(
              top: ConstantsTheme.doublePadding * 3,
              bottom: ConstantsTheme.doublePadding,
              left: ConstantsTheme.doublePadding,
              right: ConstantsTheme.doublePadding,
            ),
            title: Text(
              I18nHelper.translate(
                context,
                '$_baseTranslateAlertConfirmStatusUpdate.title',
              ),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AmbulanceColors.greenDark,
              ),
            ),
            content: SingleChildScrollView(
              child: Wrap(
                alignment: WrapAlignment.center,
                children: [
                  Text(
                    I18nHelper.translate(
                      context,
                      '$_baseTranslateAlertConfirmStatusUpdate.text',
                      params: {
                        'de': '${_serviceStatusTitle(
                          indexStatus: currentStep,
                        )}',
                        'para': '${_serviceStatusTitle(
                          indexStatus: indexClicked,
                        )}'
                      },
                    ),
                    style: TextStyle(
                      color: AmbulanceColors.grayDark2,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                ],
              ),
            ),
            actions: [
              UnimedFlatButton(
                borderColor: AmbulanceColors.redClose,
                text: I18nHelper.translate(
                  context,
                  '$_baseTranslateAlertConfirmStatusUpdate.toClose',
                ).toUpperCase(),
                textColor: Colors.white,
                color: AmbulanceColors.redClose,
                onPressed: () => Navigator.pop(context),
              ),
              UnimedFlatButton(
                text: I18nHelper.translate(
                  context,
                  '$_baseTranslateAlertConfirmStatusUpdate.confirm',
                ).toUpperCase(),
                onPressed: () {
                  _isButtonDisabled = true;
                  context.read<AttendanceMovementsCubit>().updateStatusMovement(
                      attendance: '${widget.attendance!.numAtendimento}',
                      currentStep: currentStep,
                      indexStatusApp: indexClicked);

                  if (indexClicked == 1 || indexClicked == 5) {
                    Future.delayed(
                      Duration.zero,
                      () {
                        context.read<SubscriptionCubit>().verifySubscription(
                              widget.attendance!.numAtendimento,
                              AttendanceStatus.statusApp2StatusApi(
                                  indexClicked),
                            );
                      },
                    );
                  }
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class CustomBoxShadow extends BoxShadow {
  final BlurStyle blurStyle;

  const CustomBoxShadow({
    Color color = const Color(0xFF000000),
    Offset offset = Offset.zero,
    double blurRadius = 0.0,
    this.blurStyle = BlurStyle.normal,
  }) : super(color: color, offset: offset, blurRadius: blurRadius);

  @override
  Paint toPaint() {
    final Paint result = Paint()
      ..color = color
      ..maskFilter = MaskFilter.blur(this.blurStyle, blurSigma);
    assert(() {
      if (debugDisableShadows) result.maskFilter = null;
      return true;
    }());
    return result;
  }
}

import 'package:ambulancia_app/models/cid.model.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  CidModel? testCidModel;
  Map<String, dynamic>? jsonCidModel;
  RequestCidModel? testCidModelTrue;
  Map<String, dynamic>? jsonCidModelTrue;
  setUpAll(
    () {
      testCidModel = CidModel(
        codCid: "1",
        descricao: "1",
        dvCid: "1",
      );
      jsonCidModel = {
        "codCid": "1",
        "descricao": "1",
        "dvCid": "1",
      };
      testCidModelTrue = RequestCidModel(
        descTerapeutica: "1",
        obsConduta: "1",
        cidRecordSQLite: CidRecordSQLite(
          codCid: "1",
          descricao: "1",
          dvCid: "1",
        ),
      );
      jsonCidModelTrue = {
        "descTerapeutica": "1",
        "obsConduta": "1",
        "cidRecordSQLite": {
          "codCid": "1",
          "descricao": "1",
          "dvCid": "1",
        },
      };
    },
  );

  group(
    "isInstanceOf CidModel model tests",
    () {
      test("Should be return instance of CidModel", () {
        expect(testCidModel, isInstanceOf<CidModel>());
      });
      test("Should be return instance of RequestCidModel", () {
        expect(testCidModelTrue, isInstanceOf<RequestCidModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of CidModel to json", () {
      expect(testCidModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of CidModel from json", () {
      expect(CidModel.fromJson(jsonCidModel!), isInstanceOf<CidModel>());
    });
    test("Should be return instance of RequestCidModel to json", () {
      expect(testCidModelTrue!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of RequestCidModel from json2", () {
      expect(RequestCidModel.fromJson(jsonCidModelTrue!),
          isInstanceOf<RequestCidModel>());
    });
  });

  group(
    "isInstanceOf CidModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonCidModel!["codCid"], isInstanceOf<String>());
        expect(jsonCidModel!["dvCid"], isInstanceOf<String>());
        expect(jsonCidModel!["descricao"], isInstanceOf<String>());
      });

      test("Should be return type of the json2", () {
        expect(jsonCidModelTrue!["descTerapeutica"], isInstanceOf<String>());
        expect(jsonCidModelTrue!["obsConduta"], isInstanceOf<String>());
        expect(jsonCidModelTrue!["cidRecordSQLite"], isInstanceOf<Map>());
      });
    },
  );
}

import 'package:json_annotation/json_annotation.dart';
part 'geolocation-config.model.g.dart';

@JsonSerializable(explicitToJson: true)
class GeolocationConfigModel {
  int? distanceFilter;
  int? timeIntervalSec;
  GeolocationConfigModel({this.distanceFilter, this.timeIntervalSec});
  factory GeolocationConfigModel.fromJson(Map json) =>
      _$GeolocationConfigModelFromJson(json as Map<String, dynamic>);
  Map<String, dynamic> toJson() => _$GeolocationConfigModelToJson(this);

  @override
  String toString() {
    return toJson().toString();
  }
}

class PreHospitalDischargeClinicalPictureModel {
  final int code;
  final String nameClinicalPicture;

  PreHospitalDischargeClinicalPictureModel({
    required this.code,
    required this.nameClinicalPicture,
  });

  factory PreHospitalDischargeClinicalPictureModel.fromJson(
      Map<String, dynamic> json) {
    return PreHospitalDischargeClinicalPictureModel(
      code: json['code'],
      nameClinicalPicture: json['nameClinicalPicture'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'nameClinicalPicture': nameClinicalPicture,
    };
  }
}

class VehicleModel {
  String? codVeiculo;
  String? nomeVeiculo;

  VehicleModel({this.codVeiculo, this.nomeVeiculo});

  VehicleModel.fromJson(Map<String, dynamic> json) {
    codVeiculo = json['codVeiculo'];
    nomeVeiculo = json['nomeVeiculo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['codVeiculo'] = this.codVeiculo;
    data['nomeVeiculo'] = this.nomeVeiculo;
    return data;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is VehicleModel &&
        other.codVeiculo == codVeiculo &&
        other.nomeVeiculo == nomeVeiculo;
  }

  @override
  int get hashCode => codVeiculo.hashCode ^ nomeVeiculo.hashCode;
}

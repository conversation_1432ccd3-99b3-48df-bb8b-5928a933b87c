import 'package:ambulancia_app/models/vehicle_model.dart';
import 'package:ambulancia_app/shared/api/vehicle.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'vehicle_state.dart';

class VehicleCubit extends Cubit<VehicleState> {
  VehicleCubit() : super(VehicleCubitInitial());

  List<VehicleModel>? _vehicles;
  late VehicleModel selectedVehicle;
  setVehicle(VehicleModel selectedVehicle) =>
      this.selectedVehicle = selectedVehicle;

  Future<void> loadVehicles(String codUnimed) async {
    try {
      emit(LoadingVehicleState());
      _vehicles =
          await Locator.instance.get<VehicleApi>().listVehicle(codUnimed);
      emit(LoadedVehicleState(_vehicles));
    } catch (e) {
      emit(ErrorVehicleState('${e.toString()}'));
    }
  }
}

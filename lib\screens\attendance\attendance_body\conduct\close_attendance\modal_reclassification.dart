import 'package:ambulancia_app/bloc/conduct/reclassification/reclassification_attendance_bloc_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:ambulancia_app/models/type_reclassification_attendance.dart';
import 'package:ambulancia_app/shared/widgets/modal/modal_photo_medical.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/unimed-select.dart';

class ReclassifyPopup extends StatefulWidget {
  final AttendanceModel attendanceModel;
  final int? codProtocoloDoenca;
  final String? obsProtocoloDoenca;
  final CloseAttendanceV2Model? closeAttendanceV2;

  const ReclassifyPopup({
    required this.attendanceModel,
    this.codProtocoloDoenca,
    this.obsProtocoloDoenca,
    this.closeAttendanceV2,
  });

  @override
  _ReclassifyPopupState createState() => _ReclassifyPopupState();
}

class _ReclassifyPopupState extends State<ReclassifyPopup> {
  TextEditingController reasonController = TextEditingController();
  bool formChanged = false;
  final AutocompleteOptionToString displayStringForOption =
      RawAutocomplete.defaultStringForOption;

  TextEditingController typeReclassificationAttendanceSelectedController =
      TextEditingController();

  bool isSelected = false;

  TypeReclassificationAttendanceModel? typeReclassificationAttendanceSelected;

  bool isDisabledCloseButton = false;

  @override
  void initState() {
    super.initState();
    context
        .read<ReclassificationtAttendanceCubit>()
        .getListReclassificationItens();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Deseja reclassificar o atendimento?"),
      actionsAlignment: MainAxisAlignment.spaceAround,
      content: Container(
        width: MediaQuery.of(context).size.width * 0.75,
        child: BlocConsumer<ReclassificationtAttendanceCubit,
            ReclassificationtAttendanceState>(
          listener: (context, state) {
            if (state is SetReclassificationtAttendanceState) {
              isSelected = state.value;
            }

            if (state is SetTypeReclassificationAttendanceSelected) {
              typeReclassificationAttendanceSelected = state.value;
            }
          },
          builder: (context, state) {
            if (state is ListReclassificationSucessState ||
                state is SetReclassificationtAttendanceState ||
                state is SetTypeReclassificationAttendanceSelected) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    minLeadingWidth: 0,
                    title: const Text('Sim'),
                    leading: Transform.scale(
                      scale: 1.5,
                      child: Radio<bool>(
                        key: Key('reclassification_radio_yes'),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        value: true,
                        groupValue: isSelected,
                        onChanged: (bool? value) {
                          context
                              .read<ReclassificationtAttendanceCubit>()
                              .setReclassificationtAttendance(value: value!);
                        },
                      ),
                    ),
                  ),
                  ListTile(
                    key: Key('reclassification_radio_no'),
                    contentPadding: EdgeInsets.zero,
                    minVerticalPadding: 0,
                    title: const Text('Não'),
                    leading: Transform.scale(
                      scale: 1.5,
                      child: Radio<bool>(
                        value: false,
                        groupValue: isSelected,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        onChanged: (bool? value) {
                          if (typeReclassificationAttendanceSelected != null ||
                              typeReclassificationAttendanceSelectedController
                                  .text.isNotEmpty) {
                            typeReclassificationAttendanceSelected = null;
                            typeReclassificationAttendanceSelectedController
                                .clear();

                            typeReclassificationAttendanceSelected = null;
                          }
                          context
                              .read<ReclassificationtAttendanceCubit>()
                              .setReclassificationtAttendance(value: value!);
                        },
                      ),
                    ),
                  ),
                  if (isSelected)
                    Padding(
                      padding: const EdgeInsets.only(top: 16, bottom: 16),
                      child: _dropDownItensReclassificationAttendance(
                        listReclassificationAttendanceItens: context
                            .read<ReclassificationtAttendanceCubit>()
                            .listTypeReclassification(),
                      ),
                    ),
                ],
              );
            }

            if (state is ListReclassificationErrorState) {
              return Container(
                height: MediaQuery.of(context).size.height * 0.15,
                width: MediaQuery.of(context).size.width * 0.75,
                child: Column(
                  children: [
                    Image.asset(
                      'assets/images/roberta/roberta-triste.png',
                      height: MediaQuery.of(context).size.height * 0.1,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          state.message,
                        ),
                        IconButton(
                          onPressed: () {
                            context
                                .read<ReclassificationtAttendanceCubit>()
                                .getListReclassificationItens();
                          },
                          icon: Icon(
                            Icons.refresh,
                            size: 28,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            }

            return _loadingListReclassificationtAttendance();
          },
        ),
      ),
      actions: [
        BlocConsumer<ReclassificationtAttendanceCubit,
            ReclassificationtAttendanceState>(
          listener: (context, state) {
            if (state is SetReclassificationtAttendanceState) {
              if (state.value) {
                if (typeReclassificationAttendanceSelected == null) {
                  isDisabledCloseButton = true;
                } else {
                  isDisabledCloseButton = false;
                }
              } else {
                isDisabledCloseButton = false;
              }
            } else if (state is SetTypeReclassificationAttendanceSelected) {
              isDisabledCloseButton = false;
            }
          },
          builder: (context, state) {
            return Visibility(
              visible: state is ListReclassificationSucessState ||
                  state is SetReclassificationtAttendanceState ||
                  state is SetTypeReclassificationAttendanceSelected,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AmbulanceColors.greenChart,
                      padding: EdgeInsets.symmetric(vertical: 18.0),
                    ),
                    onPressed: isDisabledCloseButton
                        ? null
                        : () {
                            if (isSelected) {
                              Navigator.pop(context);
                              return openModalPhotoMedical(
                                context: context,
                                attendanceModel: widget.attendanceModel,
                                recordType: 'Encerramento',
                                reclassify: true,
                                codTypeReclassify:
                                    typeReclassificationAttendanceSelected!
                                        .codTipoAtend,
                                codProtocoloDoenca: widget.codProtocoloDoenca,
                                obsProtocoloDoenca: widget.obsProtocoloDoenca,
                                closeAttendanceV2:
                                    widget.closeAttendanceV2?.copyWith(
                                  reclassify: true,
                                  codTypeReclassify:
                                      typeReclassificationAttendanceSelected!
                                          .codTipoAtend,
                                  codProtocoloDoenca: widget.codProtocoloDoenca,
                                  obsProtocoloDoenca: widget.obsProtocoloDoenca,
                                ),
                              );
                            } else {
                              Navigator.pop(context);
                              openModalPhotoMedical(
                                context: context,
                                attendanceModel: widget.attendanceModel,
                                recordType: 'Encerramento',
                                codProtocoloDoenca: widget.codProtocoloDoenca,
                                obsProtocoloDoenca: widget.obsProtocoloDoenca,
                                closeAttendanceV2:
                                    widget.closeAttendanceV2?.copyWith(
                                  codTypeReclassify:
                                      typeReclassificationAttendanceSelected
                                          ?.codTipoAtend,
                                  codProtocoloDoenca: widget.codProtocoloDoenca,
                                  obsProtocoloDoenca: widget.obsProtocoloDoenca,
                                ),
                              );
                            }
                          },
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 54),
                      child: Text(
                        "Encerrar",
                        style: TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AmbulanceColors.redClose,
                      padding: EdgeInsets.symmetric(vertical: 18.0),
                    ),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 54),
                      child: Text(
                        "Cancelar",
                        style: TextStyle(fontSize: 20),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        )
      ],
    );
  }

  Widget _dropDownItensReclassificationAttendance(
      {required List<TypeReclassificationAttendanceModel>
          listReclassificationAttendanceItens}) {
    return UnimedSelect<TypeReclassificationAttendanceModel?>(
      boxDecoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ConstantsTheme.padding),
        border: Border.all(
          color: AmbulanceColors.lightBlue,
          width: 1,
        ),
      ),
      controller: typeReclassificationAttendanceSelectedController,
      showClearButton: false,
      tittleWidget: Text(
        'Selecione uma nova classificação *',
        style: TextStyle(
          color: AmbulanceColors.grayDark,
          fontSize: 20,
        ),
      ),
      onSelect: (typeReclassificationAttendance) {
        context
            .read<ReclassificationtAttendanceCubit>()
            .setTypeReclassificationAttendanceSelected(
              value: typeReclassificationAttendance!,
            );
      },
      items: listReclassificationAttendanceItens
          .map(
            (entry) =>
                UnimedSelectItemModel<TypeReclassificationAttendanceModel?>(
              value: TypeReclassificationAttendanceModel(
                codTipoAtend: entry.codTipoAtend,
                nomeTipoAtend: entry.nomeTipoAtend,
              ),
              label: "${entry.codTipoAtend} - ${entry.nomeTipoAtend}",
            ),
          )
          .toList(),
    );
  }

  Widget _loadingListReclassificationtAttendance() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.13,
      width: MediaQuery.of(context).size.width * 0.75,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SpinKitThreeBounce(
            color: AmbulanceColors.green,
          ),
        ],
      ),
    );
  }
}

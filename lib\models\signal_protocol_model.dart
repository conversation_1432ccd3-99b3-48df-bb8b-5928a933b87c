import 'package:json_annotation/json_annotation.dart';

part 'signal_protocol_model.g.dart';

@JsonSerializable(anyMap: true, explicitToJson: true)
class SignalProtocolModel {
  final int codSignalProtocol;
  final String nameSignalProtocol;

  SignalProtocolModel({
    required this.codSignalProtocol,
    required this.nameSignalProtocol,
  });

  factory SignalProtocolModel.fromJson(Map<String, dynamic> json) =>
      _$SignalProtocolModelFromJson(json);

  Map<String, dynamic> toJson() => _$SignalProtocolModelToJson(this);
}

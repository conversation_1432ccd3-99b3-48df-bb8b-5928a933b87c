import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_team/attendance_team_cubit.dart';
import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/vehicle/vehicle_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/attendance_cancel.dart';
import 'package:ambulancia_app/screens/attendance/attendance_screen.dart';
import 'package:ambulancia_app/screens/home/<USER>/actions/dialog_update_team.dart';
import 'package:ambulancia_app/screens/home/<USER>/actions/modal_details.dart';
import 'package:ambulancia_app/shared/api/websocket.api.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/screen-transitions/fade.transition.dart';
import 'package:ambulancia_app/shared/utils/constants.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AttendanceActions extends StatefulWidget {
  final AttendanceModel attendance;

  AttendanceActions({Key? key, required this.attendance}) : super(key: key);

  @override
  State<AttendanceActions> createState() => _AttendanceActionsState();
}

class _AttendanceActionsState extends State<AttendanceActions> {
  final _baseTranslate = 'home.main.itemAttendance';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Ink(
        padding: EdgeInsets.symmetric(vertical: 20, horizontal: 5),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(ConstantsTheme.borderRadius),
            bottomRight: Radius.circular(ConstantsTheme.borderRadius),
          ),
        ),
        child: FittedBox(
          child: widget.attendance.codStatus == AttendanceStatus.ENCERRADO ||
                  widget.attendance.codStatus == AttendanceStatus.CANCELADO ||
                  _unmappedStatusCheck()
              ? InkWell(
                  onTap: () => _unmappedStatusCheck()
                      ? _alertUnmappedStatusCheck()
                      : _showDetailsModal(context),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          I18nHelper.translate(
                              context, '$_baseTranslate.toView'),
                          style: TextStyle(
                              fontSize: 12, fontWeight: FontWeight.w800),
                        ),
                        SizedBox(width: 10),
                        Icon(
                          _unmappedStatusCheck()
                              ? Icons.warning_amber
                              : Icons.remove_red_eye_outlined,
                          color: UnimedColors.greenChart,
                          size: 35,
                        ),
                      ],
                    ),
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () => _openCancelModal(context),
                      child: Row(
                        children: [
                          Text(
                            I18nHelper.translate(
                                context, '$_baseTranslate.cancel'),
                            style: TextStyle(
                              fontSize: 100,
                              fontWeight: FontWeight.w800,
                            ),
                          ),
                          Icon(
                            Icons.block_outlined,
                            color: Colors.red,
                            size: 300,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 100),
                    InkWell(
                      onTap: () {
                        if (widget.attendance.enderecoAtendimento != null) {
                          if (widget.attendance.codStatus ==
                              AttendanceStatus.DESPACHO)
                            _updateTeamAttendanceModal(context);
                          else
                            _openAttendance(context);
                        } else {
                          _openAlert(context);
                        }
                      },
                      child: Row(
                        children: [
                          Text(
                              I18nHelper.translate(
                                  context, '$_baseTranslate.toMeet'),
                              style: TextStyle(
                                  fontSize: 100, fontWeight: FontWeight.w800)),
                          Icon(
                            Icons.power_settings_new_rounded,
                            color: UnimedColors.greenChart,
                            size: 400,
                            key: ValueKey(
                                'power_${widget.attendance.numAtendimento}'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  void _openAttendance(BuildContext context) {
    var state = context.read<ConnectivityCubit>().state;

    context
        .read<AttendanceTeamCubit>()
        .getAttendanceTeam(codUnimed: widget.attendance.codUnimed);

    if (state is ConnectivityOnlineState) {
      Navigator.push(
        context,
        FadeRoute(
          page: AttendanceScreen(attendance: widget.attendance),
        ),
      );
    }
    if (state is ConnectivityOfflineState) {
      return Alert.open(
        context,
        title: I18nHelper.translate(
          context,
          '$_baseTranslate.attentionNoInternet.title',
        ),
        text: I18nHelper.translate(
          context,
          '$_baseTranslate.attentionNoInternet.text',
        ),
      );
    }
  }

  void _showDetailsModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          actions: [
            TextButton(
                style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    backgroundColor: AmbulanceColors.redClose),
                onPressed: () {
                  Navigator.pop(context);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'Fechar',
                    style: TextStyle(fontSize: 20),
                  ),
                ))
          ],
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius)),
          insetPadding: EdgeInsets.only(
            top: ConstantsTheme.doublePadding * 3,
            bottom: ConstantsTheme.doublePadding,
            left: ConstantsTheme.doublePadding,
            right: ConstantsTheme.doublePadding,
          ),
          content: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              child: DetailsAttendanceModal(attendance: widget.attendance)),
        );
      },
    );
  }

  void _openAlert(context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.topCenter,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(ConstantsTheme.borderRadius)),
            insetPadding: EdgeInsets.only(
              top: ConstantsTheme.doublePadding * 3,
              bottom: ConstantsTheme.doublePadding,
              left: ConstantsTheme.doublePadding,
              right: ConstantsTheme.doublePadding,
            ),
            content: Container(
                width: MediaQuery.of(context).size.width * 0.86,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    I18nHelper.translate(
                        context, '$_baseTranslate.warning.title'),
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                )),
            actions: [
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AmbulanceColors.greenLight3,
                  textStyle: TextStyle(color: Colors.white),
                ),
                child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 50, vertical: 15),
                    child: Text(
                      I18nHelper.translate(
                          context, '$_baseTranslate.warning.back'),
                      style: TextStyle(fontSize: 18),
                    )),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              )
            ],
          ),
        );
      },
    );
  }

  void _openCancelModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(ConstantsTheme.borderRadius)),
            insetPadding: EdgeInsets.only(
              top: ConstantsTheme.doublePadding * 3,
              bottom: ConstantsTheme.doublePadding,
              left: ConstantsTheme.doublePadding,
              right: ConstantsTheme.doublePadding,
            ),
            content: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              child: AttendanceCancel(
                attendance: widget.attendance,
                onConfirm: () {
                  Locator.instance.get<WebSocketApi>().dispose();
                  final codVeiculo = BlocProvider.of<VehicleCubit>(context)
                      .selectedVehicle
                      .codVeiculo;
                  context.read<AttendanceListCubit>().getAttendances(
                      codUnimed: Constants.COD_UNIMED,
                      codVeiculo: codVeiculo,
                      paginaAtual: BlocProvider.of<AttendanceListCubit>(context)
                          .currentPage,
                      quantidadeRegistros: Constants.NUM_ATTENDANCE_PAGE);
                  Navigator.pop(context);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  void _updateTeamAttendanceModal(BuildContext context) {
    context
        .read<AttendanceTeamCubit>()
        .getAttendanceTeam(codUnimed: widget.attendance.codUnimed);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.topCenter,
          child: AlertDialog(
            shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(ConstantsTheme.borderRadius)),
            insetPadding: EdgeInsets.only(
              top: ConstantsTheme.doublePadding * 3,
              bottom: ConstantsTheme.doublePadding,
              left: ConstantsTheme.doublePadding,
              right: ConstantsTheme.doublePadding,
            ),
            content: Container(
              child: DialogUpdateTeam(attendance: widget.attendance),
            ),
          ),
        );
      },
    );
  }

  bool _unmappedStatusCheck() {
    return (AttendanceStatus.statusApi2StatusApp(widget.attendance.codStatus) ==
            -1 &&
        widget.attendance.codStatus != AttendanceStatus.DESPACHO);
  }

  void _alertUnmappedStatusCheck() {
    Alert.open(
      context,
      text:
          "O status do atendimento está inválido.\n N° do status: ${widget.attendance.codStatus}\n Descrição: ${widget.attendance.nomeStatus.isNotEmpty ? widget.attendance.nomeStatus : "NÃO CADASTRADO"}",
    );
  }
}

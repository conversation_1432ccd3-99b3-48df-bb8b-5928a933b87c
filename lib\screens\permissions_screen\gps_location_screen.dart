import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../bloc/permissions_force/permission_cubit.dart';
import '../../shared/widgets/header/header.dart';
import '../../theme/colors.dart';

class GpsLocationScreen extends StatefulWidget {
  @override
  _GpsLocationScreenState createState() => _GpsLocationScreenState();
}

class _GpsLocationScreenState extends State<GpsLocationScreen> {
  @override
  void initState() {
    context.read<PermissionCubit>().onGpsAtivePage = true;
    super.initState();
  }

  @override
  void dispose() {
    context.read<PermissionCubit>().onGpsAtivePage = false;
    print(context.read<PermissionCubit>().onGpsAtivePage);
    debugPrint('Deixou a tela GPS LOCK SCREEN');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AmbulanceHeader(),
        body: Padding(
          padding: const EdgeInsets.only(top: 48.0, left: 15.0, right: 15),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 30),
                      Center(
                          child: Icon(Icons.info_outline,
                              size: MediaQuery.of(context).size.width / 4,
                              color: AmbulanceColors.greenDark)),
                      const SizedBox(height: 30),
                      _descriptionText(),
                      const SizedBox(height: 30),
                      _gpsTutorialWidget(),
                      const SizedBox(height: 100),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AmbulanceColors.green,
                              padding: EdgeInsets.symmetric(vertical: 16.0),
                            ),
                            key: Key('concluir_button'),
                            onPressed: () async {
                              bool result = await context
                                  .read<PermissionCubit>()
                                  .isGpsEnabled();
                              if (result) {
                                context.read<PermissionCubit>().onGpsAtivePage =
                                    false;
                                Navigator.pop(context);
                              } else {
                                ScaffoldMessengerState scaffoldMessenger =
                                    ScaffoldMessenger.of(context);
                                scaffoldMessenger.clearSnackBars();

                                scaffoldMessenger.showSnackBar(
                                  SnackBar(
                                    content: Center(
                                      child: Text(
                                        'Ative a localização GPS primeiro!',
                                        style: TextStyle(fontSize: 35),
                                      ),
                                    ),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              }
                            },
                            child: Text(
                              'Concluir'.toUpperCase(),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _descriptionText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: TextStyle(fontSize: 18, color: UnimedColors.purple),
          children: [
            const TextSpan(
              text: 'Para funcionamento correto ',
            ),
            const TextSpan(
              text: 'do aplicativo ',
            ),
            const TextSpan(
                text: 'Unimed Urgente ',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const TextSpan(
              text: 'ative a ',
            ),
            TextSpan(
                text: 'localização GPS ',
                style: const TextStyle(fontWeight: FontWeight.bold)),
            const TextSpan(
              text: 'do aparelho e pressione ',
            ),
            const TextSpan(
                text: 'CONCLUIR.',
                style: TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
      ),
    );
  }

  Widget _gpsTutorialWidget() {
    return Center(
      child: Image.asset(
        'assets/images/tutorial_ativar_gps.gif',
      ),
    );
  }
}

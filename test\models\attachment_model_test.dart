import 'package:ambulancia_app/models/attachment_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AttachmentModel', () {
    test('fromJson() must fill in the properties correctly', () {
      final jsonMap = {
        'sequencial': 123,
        'numAtendimento': 456,
        'nomeArquivo': 'arquivo.jpg',
        'tipoAnexo': 1,
        'descricaoTipoAnexo': 'Descrição do anexo',
        'motivoAnexo': 2,
      };

      final attachment = AttachmentModel.fromJson(jsonMap);

      expect(attachment.sequencial, 123);
      expect(attachment.numAtendimento, 456);
      expect(attachment.nomeArquivo, 'arquivo.jpg');
      expect(attachment.tipoAnexo, 1);
      expect(attachment.descricaoTipoAnexo, 'Descrição do anexo');
      expect(attachment.motivoAnexo, 2);
    });

    test('toJson() should return a map with the correct properties', () {
      final attachment = AttachmentModel(
        sequencial: 123,
        numAtendimento: 456,
        nomeArquivo: 'arquivo.txt',
        tipoAnexo: 1,
        descricaoTipoAnexo: 'Descrição do anexo',
        motivoAnexo: 2,
      );

      final jsonMap = attachment.toJson();

      expect(jsonMap, isA<Map<String, dynamic>>());
      expect(jsonMap['sequencial'], 123);
      expect(jsonMap['numAtendimento'], 456);
      expect(jsonMap['nomeArquivo'], 'arquivo.txt');
      expect(jsonMap['tipoAnexo'], 1);
      expect(jsonMap['descricaoTipoAnexo'], 'Descrição do anexo');
      expect(jsonMap['motivoAnexo'], 2);
    });
  });
}

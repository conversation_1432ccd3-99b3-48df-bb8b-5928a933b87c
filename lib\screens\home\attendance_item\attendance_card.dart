import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/shared/widgets/attendance/attendance_card_header.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AttendanceCard extends StatefulWidget {
  final AttendanceModel attendance;
  const AttendanceCard({Key? key, required this.attendance}) : super(key: key);

  @override
  State<AttendanceCard> createState() => _AttendanceCardState();
}

class _AttendanceCardState extends State<AttendanceCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.6,
      padding: EdgeInsets.all(ConstantsTheme.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
        boxShadow: [
          ConstantsTheme.boxShadow,
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AttendanceCardHeader(attendance: widget.attendance),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(ConstantsTheme.padding),
                  decoration: BoxDecoration(
                    color: Color.fromARGB(120, 196, 203, 207),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(ConstantsTheme.borderRadius),
                      bottomRight: Radius.circular(ConstantsTheme.borderRadius),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _infoColumn(
                        title: 'Nome',
                        subTitle: widget.attendance.nome,
                      ),
                      SizedBox(height: ConstantsTheme.padding * .5),
                      _infoColumn(
                        title: 'Data de nascimento',
                        subTitle: formatDate(
                          dateString: widget.attendance.dataNascimento,
                        ),
                      ),
                      SizedBox(height: ConstantsTheme.padding * .5),
                      _infoColumn(
                        title: 'Nome da mãe',
                        subTitle: widget.attendance.nomeMae,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String formatDate({required String dateString}) {
    if (dateString.isEmpty) {
      return '';
    } else {
      DateTime dateTime = DateTime.parse(dateString);

      String formattedDate = DateFormat('dd/MM/yyyy').format(dateTime);

      return formattedDate;
    }
  }

  Widget _infoColumn({required String title, required String subTitle}) {
    return Visibility(
      visible: subTitle.isNotEmpty,
      child: Padding(
        padding: const EdgeInsets.only(right: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: ConstantsTheme.padding * .5),
            Text(
              subTitle,
              style: TextStyle(
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

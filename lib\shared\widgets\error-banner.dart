import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

class ErrorBanner extends StatelessWidget {
  final String message;
  final IconData icon;
  final String title;
  final Color backgroundColor;
  final Color iconColor;
  final double elevation;
  ErrorBanner({
    required this.message,
    this.title = '',
    this.icon = Icons.report_problem_rounded,
    this.backgroundColor = Colors.white,
    this.iconColor = AmbulanceColors.greenDark,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.all(8.0),
        child: Card(
          color: backgroundColor,
          elevation: elevation,
          child: Container(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Icon(
                  icon,
                  size: 48,
                  color: iconColor,
                ),
                const SizedBox(height: 2.0),
                Text(title,
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center),
                const SizedBox(height: 4.0),
                Text(message, textAlign: TextAlign.center),
              ],
            ),
          ),
        ));
  }
}

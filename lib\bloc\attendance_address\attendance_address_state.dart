part of 'attendance_address_cubit.dart';

abstract class AttendanceAddressState extends Equatable {
  const AttendanceAddressState();

  @override
  List<Object> get props => [];
}

class AttendanceAddressInitial extends AttendanceAddressState {}

class LoadingAddressState extends AttendanceAddressState {}

class SendingAddressState extends AttendanceAddressState {}

class SentAddressState extends AttendanceAddressState {}

class LoadedAddressState extends AttendanceAddressState {
  final List<AddressInfos> addresses;
  LoadedAddressState(this.addresses);
}

class EmptyAddressState extends AttendanceAddressState {}

class AdressErrorState extends AttendanceAddressState {
  final message;
  AdressErrorState(this.message);
}

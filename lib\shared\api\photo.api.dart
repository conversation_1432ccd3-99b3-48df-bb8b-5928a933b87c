import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/shared/api/attendance/notification_of_amounts_of_data_pending_synchronization.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/offline_first.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:http/http.dart' as http;

class PhotoOperatorApi {
  final UnimedHttpClient httpClient;

  final logger = UnimedLogger(className: 'PhotoOperatorApi');

  PhotoOperatorApi(this.httpClient);

  Future<String?> sendPhoto(
      {String? numAtendimento,
      required String pathPhotoOperator,
      String? recordType,
      required String reasonAnnexPhoto}) async {
    final url =
        '${const String.fromEnvironment('ambulanciaOsbUrl')}/unimedurgente/anexo/operador';
    try {
      final Map<String, dynamic> json = {
        "numAtendimento": numAtendimento,
        "uniurgMotivoAnexoEnum": reasonAnnexPhoto
      };
      final request = http.MultipartRequest('POST', Uri.parse(url));
      final fileHttp = await http.MultipartFile.fromPath(
          'arquivos', pathPhotoOperator,
          filename: _generateFileName(recordType, numAtendimento));
      request.headers['Authorization'] =
          HttpUtils.getAuthorizationBasicAmbulanciaOsb();
      request.fields['obj'] = jsonEncode(json);
      request.files.add(fileHttp);

      final response = await request.send().timeout(Duration(seconds: 30));
      final bodyResponse = await response.stream.bytesToString();

      if (response.statusCode == 200 || response.statusCode == 201) {
        final bodyDecode = jsonDecode(bodyResponse);
        logger.i('sendPhoto sucess - statusCode: ${response.statusCode}');
        return bodyDecode['mensagem'];
      } else {
        logger.e(
            'sendPhoto error - statusCode ${response.statusCode} $bodyResponse');
        throw OperatorPhotoException();
      }
    } on NoInternetException catch (e) {
      logger.e('sendPhoto NoInternetException: $e');
      _sendPhotoOffline(
        numAtendimento: numAtendimento,
        pathPhotoOperator: pathPhotoOperator,
        recordType: recordType,
        reasonAnnexPhoto: reasonAnnexPhoto,
        url: url,
        authorization: HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
        unimedException: NoInternetException(),
      );
    } on SocketException catch (e) {
      logger.e('sendPhoto SocketException: $e');
      _sendPhotoOffline(
        numAtendimento: numAtendimento,
        pathPhotoOperator: pathPhotoOperator,
        reasonAnnexPhoto: reasonAnnexPhoto,
        url: url,
        unimedException: NotFoundException(),
        authorization: HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
        recordType: recordType,
      );
    } on TimeoutException catch (e) {
      logger.e('sendPhoto error timeout $e');
      _sendPhotoOffline(
        numAtendimento: numAtendimento,
        pathPhotoOperator: pathPhotoOperator,
        reasonAnnexPhoto: reasonAnnexPhoto,
        url: url,
        unimedException: ServiceTimeoutException(),
        authorization: HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
        recordType: recordType,
      );
    } catch (e) {
      logger.e('sendPhoto error exception: $e');
      _sendPhotoOffline(
        numAtendimento: numAtendimento,
        pathPhotoOperator: pathPhotoOperator,
        reasonAnnexPhoto: reasonAnnexPhoto,
        url: url,
        unimedException: UnimedException(""),
        authorization: HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
        recordType: recordType,
      );
    }
    return null;
  }

  _persistSendPhoto(
      {required numAtendimento,
      required String url,
      required String pathFile,
      required String fileName,
      required String authorization,
      required String jsonFields,
      required exception}) async {
    try {
      await OfflineFirst.persistRequestWithFile(
        customId:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.sendClinicEvaluation.name}$numAtendimento',
        url: url,
        pathFile: pathFile,
        fileName: fileName,
        authorization: authorization,
        jsonFields: jsonFields,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistSendPhoto catch Exception $e');
      throw exception;
    }
  }

  String _generateFileName(recordType, serviceNumber) {
    String fileName = '';
    switch (recordType) {
      case 'Avaliação Clínica':
        return fileName = 'ac_$serviceNumber.png';
      case 'Conduta':
        return fileName = 'c_$serviceNumber.png';
      case 'Encerramento':
        return fileName = 'en_$serviceNumber.png';
    }
    return fileName;
  }

  void _sendPhotoOffline({
    String? numAtendimento,
    required String pathPhotoOperator,
    String? recordType,
    required String reasonAnnexPhoto,
    required String url,
    required String authorization,
    required UnimedException unimedException,
  }) {
    final Map<String, dynamic> fields = {
      "numAtendimento": numAtendimento,
      "uniurgMotivoAnexoEnum": reasonAnnexPhoto
    };
    final fieldsString = jsonEncode(fields);
    _persistSendPhoto(
      numAtendimento: numAtendimento,
      url: url,
      pathFile: pathPhotoOperator,
      fileName: _generateFileName(recordType, numAtendimento),
      authorization: authorization,
      jsonFields: fieldsString,
      exception: unimedException,
    );
  }
}

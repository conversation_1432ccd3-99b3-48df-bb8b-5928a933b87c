import 'dart:convert';

import 'package:ambulancia_app/models/signal_protocol_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';

class SinalProtocolpi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'SinalProtocolpi');

  SinalProtocolpi(this.httpClient);

  Future<List<SignalProtocolModel>> getSinalProtocol() async {
    final String endpoint = 'ambulance/attendance/protocol-signals';

    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final headers = {
        'Authorization': "Bearer $token",
      };

      final response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);
        logger.d('getSinalProtocol retorno : $bodyRetorno');
        return (bodyRetorno as List)
            .map<SignalProtocolModel>((sa) => SignalProtocolModel.fromJson(sa))
            .toList();
      } else {
        logger.e('getSinalProtocol found');
        throw MotiveException(MessageException.general);
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e(
          'getSinalProtocol - Error AttendanceApi ServiceTimeoutException $ex');
      throw ServiceTimeoutException();
    } on MotiveException catch (ex) {
      logger.i('getSinalProtocol AutorizacoesException ${ex.message}');
      throw MotiveException(MessageException.general);
    } on NoInternetException catch (ex) {
      logger.i('getSinalProtocol NoInternetException ${ex.message}');
      throw NoInternetException();
    } on NotFoundException catch (ex) {
      logger.i('getSinalProtocol NotFoundException ${ex.message}');
      throw NotFoundException();
    } catch (ex) {
      logger.i('getSinalProtocol exception $ex');
      throw MotiveException(MessageException.general);
    }
  }
}

import 'package:ambulancia_app/bloc/attendance/attendance_cubit.dart';
import 'package:ambulancia_app/bloc/supply/supply_cubit.dart';
import 'package:ambulancia_app/bloc/supply/supply_state.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/material_expenses_model.dart';
import 'package:ambulancia_app/models/supply_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_screen.dart';
import 'package:ambulancia_app/screens/attendance/supplies/circular_button.dart';
import 'package:ambulancia_app/screens/attendance/supplies/supply_attendance_item.dart';
import 'package:ambulancia_app/shared/formatters.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/screen-transitions/fade.transition.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/unimed-select.dart';

const int MAX_ITENS = 20;

class AttendanceMaterialExpenses extends StatefulWidget {
  final AttendanceModel attendance;

  const AttendanceMaterialExpenses({Key? key, required this.attendance})
      : super(key: key);
  @override
  _AttendanceMaterialExpensesState createState() =>
      _AttendanceMaterialExpensesState();
}

class _AttendanceMaterialExpensesState
    extends State<AttendanceMaterialExpenses> {
  TextEditingController typeController = TextEditingController();
  TextEditingController quantityController = TextEditingController();
  final _baseTranslate = 'spendingForm';

  List<MaterialExpensesModel> materialExpenses = [];
  AttendanceModel? attendance;
  SupplyModel? supplySelected;

  @override
  void initState() {
    super.initState();
    Future.delayed(
        Duration.zero,
        () => typeController.text =
            I18nHelper.translate(context, '$_baseTranslate.selectMaterial'));
    attendance = BlocProvider.of<AttendanceCubit>(context).currentAttendance();
    context
        .read<SupplyCubit>()
        .listAvailableSupplies(attendance!.numAtendimento);

    final supplies = attendance!.materiais;
    BlocProvider.of<SupplyCubit>(context).setSuppliesAttendance(supplies);
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SupplyCubit, SupplyState>(
      listener: (context, state) {
        if (state is CRUDErrorSupplyState)
          _alertError(state.message);
        else if (state is CRUDDoneSupplyState) {
          typeController.text =
              I18nHelper.translate(context, '$_baseTranslate.selectMaterial');
          quantityController.text = '';
          supplySelected = null;
          _alertSucess('Alteração salva com sucesso');
        }
      },
      child: SingleChildScrollView(
        child: Column(
          children: [
            Align(
              alignment: Alignment.topLeft,
              child: Text(
                I18nHelper.translate(context, '$_baseTranslate.title'),
                style: TextStyle(
                    fontWeight: FontWeight.w900,
                    fontSize: 22,
                    color: AmbulanceColors.grayDark),
              ),
            ),
            SizedBox(height: ConstantsTheme.doublePadding),
            _addSupplies(),
            SizedBox(height: ConstantsTheme.doublePadding),
            _listSupplyAttendance(),
            SizedBox(height: ConstantsTheme.doublePadding),
            _btnClose(context)
          ],
        ),
      ),
    );
  }

  Widget _listSupplyAttendance() {
    final suppliesAttendance =
        BlocProvider.of<SupplyCubit>(context, listen: true)
            .suppliesAttendance();

    return ListView.builder(
      primary: false,
      shrinkWrap: true,
      itemCount: suppliesAttendance.length,
      itemBuilder: (context, index) {
        return SupplyAttendanceItem(
          supply: suppliesAttendance[index],
          attendance: widget.attendance,
        );
      },
    );
  }

  Widget _addSupplies() {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: ConstantsTheme.padding * 1.5,
          vertical: ConstantsTheme.padding * 2.5),
      decoration: BoxDecoration(
        color: AmbulanceColors.greenLight,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(ConstantsTheme.padding),
          topRight: Radius.circular(ConstantsTheme.padding),
          bottomLeft: Radius.circular(ConstantsTheme.padding),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 5,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  I18nHelper.translate(context, '$_baseTranslate.type'),
                  style: TextStyle(
                    fontSize: 19,
                    color: AmbulanceColors.greenDark,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                _buildInputSupplies(),
              ],
            ),
          ),
          SizedBox(width: ConstantsTheme.doublePadding),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  I18nHelper.translate(context, '$_baseTranslate.amount'),
                  style: TextStyle(
                    fontSize: 15,
                    color: AmbulanceColors.greenDark,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 30),
                _inputQuantity(),
              ],
            ),
          ),
          SizedBox(width: ConstantsTheme.doublePadding),
          Column(
            children: [
              SizedBox(height: 40),
              _btnAdd(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInputSupplies() {
    return BlocBuilder<SupplyCubit, SupplyState>(
      builder: (context, state) {
        if (state is LoadingSupplyState)
          return SpinKitThreeBounce(color: AmbulanceColors.green, size: 30);
        else if (state is ErrorLoadingSupplyState)
          return Row(
            children: <Widget>[
              Flexible(
                  child: Text('${state.message}',
                      style: TextStyle(color: Colors.red, fontSize: 18))),
              IconButton(
                icon: Icon(Icons.refresh, color: unimedGreen),
                onPressed: () => context
                    .read<SupplyCubit>()
                    .listAvailableSupplies(attendance!.numAtendimento),
              )
            ],
          );
        else if (state is LoadedSupplyState) {
          return _dropDownItens(state.supplies!);
        } else {
          final supplies = BlocProvider.of<SupplyCubit>(context).supplies();
          return supplies != null
              ? _dropDownItens(supplies)
              : DropdownButtonFormField<SupplyModel?>(
                  hint: Text(
                      I18nHelper.translate(context, '$_baseTranslate.select')),
                  onChanged: null,
                  items: [],
                );
        }
      },
    );
  }

  Widget _btnAdd() {
    return BlocBuilder<SupplyCubit, SupplyState>(
      builder: (context, state) {
        if (state is SavingSupplyState)
          return SpinKitThreeBounce(color: AmbulanceColors.green);
        else if (state is LoadingSupplyState ||
            state is ErrorLoadingSupplyState ||
            state is RemovingSupplyState ||
            state is UpdatingSupplyState)
          return CircularButton(
              color: AmbulanceColors.green,
              icon: Icons.add,
              onPressed: () => {});
        else
          return CircularButton(
              color: AmbulanceColors.greenDark,
              icon: Icons.add,
              onPressed: () {
                if (supplySelected == null ||
                    quantityController.text == '' ||
                    int.tryParse(quantityController.text)! <= 0) {
                  _alertError(I18nHelper.translate(
                      context, '$_baseTranslate.warningText'));
                  return null;
                }

                context.read<SupplyCubit>().saveSupply(
                      attendanceNumber: attendance!.numAtendimento,
                      quantity: int.parse(quantityController.text),
                      supply: supplySelected!,
                    );
              });
      },
    );
  }

  Widget _dropDownItens(List<SupplyModel> supplies) {
    return UnimedSelect<SupplyModel?>(
      boxDecoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ConstantsTheme.padding),
      ),
      controller: typeController,
      showClearButton: false,
      title: '',
      onSelect: (supply) {
        setState(() {
          supplySelected = supply;
          typeController.text = supply?.description ??
              I18nHelper.translate(context, '$_baseTranslate.selectMaterial');
        });
      },
      items: supplies
          .map(
            (entry) => UnimedSelectItemModel(
              value: entry,
              label: entry.description!,
            ),
          )
          .toList(),
    );
  }

  Widget _inputQuantity() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5),
      child: Container(
        padding: EdgeInsets.all(13),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(ConstantsTheme.padding),
        ),
        child: TextFormField(
          key: ValueKey('TextFormFieldQuantity'),
          validator: (value) {
            if (value == null || value.isEmpty)
              return I18nHelper.translate(
                  context, '$_baseTranslate.amountWarning');
            else
              return null;
          },
          controller: quantityController,
          inputFormatters: FormatterField.inputNumberSuppliesFormatter,
          keyboardType: TextInputType.number,
          style: TextStyle(fontSize: 16),
          decoration: InputDecoration.collapsed(
            hintText: I18nHelper.translate(context, '$_baseTranslate.eg'),
          ),
        ),
      ),
    );
  }

  Widget _btnClose(BuildContext context) {
    return BlocBuilder<SupplyCubit, SupplyState>(builder: (context, state) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.redClose,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: () {
              Navigator.pop(context);
              if (state is CRUDDoneSupplyState) {
                Navigator.pushReplacement(
                    context,
                    FadeRoute(
                        page: AttendanceScreen(attendance: widget.attendance)));
              }
            },
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 80),
                child: Text(
                  I18nHelper.translate(context, '$_baseTranslate.close'),
                  style: TextStyle(fontSize: 20),
                )),
          )
        ],
      );
    });
  }

  _alertSucess(text) {
    Alert.open(context,
        title: I18nHelper.translate(context, '$_baseTranslate.sent'),
        text: text);
  }

  _alertError(text) {
    Alert.open(
      context,
      textButtonClose:
          I18nHelper.translate(context, '$_baseTranslate.alert.closed'),
      title: I18nHelper.translate(context, '$_baseTranslate.alert.title'),
      text: text,
    );
  }
}

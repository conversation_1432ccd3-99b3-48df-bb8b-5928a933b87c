import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:bloc/bloc.dart';

class NewServiceAbleCubit extends Cubit<bool> {
  NewServiceAbleCubit() : super(false);

  void updateButtonNewServiceAble({required int serviceStatus}) {
    if (serviceStatus == AttendanceStatus.SAIDA_BASE ||
        serviceStatus == AttendanceStatus.SAIDA_ORIGEM ||
        serviceStatus == AttendanceStatus.SAIDA_DESTINO) {
      emit(true);
    } else {
      emit(false);
    }
  }
}

import 'dart:convert';

import 'package:ambulancia_app/models/type_reclassification_attendance.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:http/http.dart' as http;

class ConductReclassificationAPI {
  final UnimedHttpClient httpClient;

  final logger = UnimedLogger(className: 'ConductReclassificationAPI');

  ConductReclassificationAPI(this.httpClient);

  Future<List<TypeReclassificationAttendanceModel>>
      getListReclassificationItens() async {
    final endpoint = 'ambulance/attendance/reclassification/63/codUnimed';
    final String url =
        '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final Map<String, String> headers = {'Authorization': 'Bearer $token'};

      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);
      // 'https://perfilappshmg.unimedfortaleza.com.br/api/ambulance/attendance/reclassification/63/codUnimed'
      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);

        final List<TypeReclassificationAttendanceModel>
            _listItensReclassification = (bodyRetorno as List)
                .map<TypeReclassificationAttendanceModel>((funcao) {
          return TypeReclassificationAttendanceModel.fromJson(funcao);
        }).toList();
        logger.d('getListReclassificationItens - response: ${response.body}');

        return _listItensReclassification;
      } else {
        logger.e('getListReclassificationItens ${response.statusCode} found');
        throw TeamException(MessageException.general);
      }
    } on NotFoundException catch (e) {
      logger
          .e('getListReclassificationItens NotFoundException: ${e.toString()}');
      throw NotFoundException();
    } on NoInternetException catch (e) {
      logger.e(
          'getListReclassificationItens NoInternetException: ${e.toString()}');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e(
          'getListReclassificationItens ServiceTimeoutException: ${e.toString()}');
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('getListReclassificationItens ${ex.runtimeType}: $ex');
      throw UnimedException(ex.message);
    } catch (e) {
      logger.e('getListReclassificationItens Exception: ${e.toString()}');
      throw UnimedException(MessageException.general);
    }
  }
}

import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';

class DetailsTable extends StatelessWidget {
  final String nameTable;
  final Database database;

  const DetailsTable(
      {Key? key, required this.nameTable, required this.database})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: Text('Detalhes da Tabela $nameTable'),
        backgroundColor: UnimedColors.greenDark,
      ),
      body: FutureBuilder<List<Map<String, dynamic>>>(
        future: database.rawQuery('SELECT * FROM $nameTable'),
        builder: (BuildContext context,
            AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
          if (!snapshot.hasData) {
            return Center(child: CircularProgressIndicator());
          }
          return ListView.builder(
            itemCount: snapshot.data!.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(snapshot.data![index].toString()),
              );
            },
          );
        },
      ),
    );
  }
}

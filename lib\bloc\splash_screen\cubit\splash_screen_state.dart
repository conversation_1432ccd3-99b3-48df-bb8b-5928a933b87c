part of 'splash_screen_cubit.dart';

abstract class SplashScreenState extends Equatable {
  const SplashScreenState();

  @override
  List<Object> get props => [];
}

class SplashScreenStateInitial extends SplashScreenState {}

class InterruptedStartupSplashScreen extends SplashScreenState {
  final String message;
  InterruptedStartupSplashScreen({required this.message});
}

class ContinuedStartupSplashScreen extends SplashScreenState {}

import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-text.dart';
import 'package:ambulancia_app/shared/formatters.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class VenousAccess extends StatefulWidget {
  final ResponseClinicEvaluation clinicEvaluation;
  final backgroundColor;
  final textColor;
  final Function? emmitChangedForParent;
  VenousAccess({
    Key? key,
    required this.clinicEvaluation,
    this.backgroundColor = AmbulanceColors.beige,
    this.textColor = AmbulanceColors.greenDark,
    this.emmitChangedForParent,
  }) : super(key: key);

  @override
  _VenousAccessState createState() => _VenousAccessState();
}

class _VenousAccessState extends State<VenousAccess> {
  bool showObservations = false;
  late List<bool> checkValues;
  TextEditingController textObservation = TextEditingController();
  TextEditingController diluicaoController = TextEditingController();
  final _baseTranslate = 'evaluationClinicalForm.venousAccess';
  RequestClinicEvaluation? requestClinicEvaluation;

  @override
  void initState() {
    requestClinicEvaluation =
        BlocProvider.of<ClinicEvaluationCubit>(context).requestClinicEvaluation;
    checkValues =
        List<bool>.filled(widget.clinicEvaluation.acessoVenoso.length, false);

    showObservations = BlocProvider.of<ClinicEvaluationCubit>(context)
            .requestClinicEvaluation!
            .acessoVenoso ==
        CEVenousAccessCode.VasopressorAmines;

    textObservation.text =
        requestClinicEvaluation!.aminasVasopressorasQuais?.toString() ?? '';

    diluicaoController.text =
        requestClinicEvaluation!.diluicaoVelocidadeInfusao?.toString() ?? '';
    super.initState();
  }

  bool checkCurrentValueIput(index) {
    return checkValues[index];
  }

  @override
  Widget build(BuildContext context) {
    return ClincalCard(
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: Column(
        children: [
          GridView.builder(
            primary: false,
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 3,
            ),
            itemCount: widget.clinicEvaluation.acessoVenoso.length,
            itemBuilder: (context, index) {
              return ClinicalInputCheck(
                itemIndex: index,
                checkParentValue: checkCurrentValueIput,
                label: widget.clinicEvaluation.acessoVenoso[index].descricao,
                labelColor: widget.textColor,
                checkColor: widget.textColor,
                forceValue: BlocProvider.of<ClinicEvaluationCubit>(context)
                        .requestClinicEvaluation!
                        .acessoVenoso ==
                    widget.clinicEvaluation.acessoVenoso[index].codigo,
                onPresss: (isChecked) {
                  final length = widget.clinicEvaluation.acessoVenoso.length;
                  final code =
                      widget.clinicEvaluation.acessoVenoso[index].codigo;

                  setState(() {
                    if (code == CEVenousAccessCode.VasopressorAmines &&
                        isChecked == false) {
                      showObservations = false;
                    } else {
                      showObservations =
                          code == CEVenousAccessCode.VasopressorAmines;
                    }

                    if (code == CEVenousAccessCode.VasopressorAmines) {
                      textObservation.text = "";
                      diluicaoController.text = "";
                    }

                    for (int i = 0; i < length; ++i) checkValues[i] = false;
                    checkValues[index] = isChecked!;
                    BlocProvider.of<ClinicEvaluationCubit>(context)
                            .requestClinicEvaluation!
                            .acessoVenoso =
                        isChecked
                            ? widget.clinicEvaluation.acessoVenoso[index].codigo
                            : null;
                  });
                  widget.emmitChangedForParent!();
                },
              );
            },
          ),
          if (showObservations)
            ClinicalInputText(
              title: I18nHelper.translate(context, '$_baseTranslate.which'),
              validator: (value) {
                if (value!.isEmpty)
                  return I18nHelper.translate(
                      context, 'filedsValidatorsWarnings.required');
                else
                  return null;
              },
              textColor: widget.textColor,
              width: MediaQuery.of(context).size.width * 0.8,
              controller: textObservation,
              inputFormatters: [LengthLimitingTextInputFormatter(50)],
              onChange: (text) {
                BlocProvider.of<ClinicEvaluationCubit>(context)
                    .requestClinicEvaluation!
                    .aminasVasopressorasQuais = text;
                widget.emmitChangedForParent!();
              },
            ),
          SizedBox(height: 10),
          if (showObservations)
            ClinicalInputText(
              validator: (value) {
                if (value!.isEmpty)
                  return I18nHelper.translate(
                      context, 'filedsValidatorsWarnings.required');
                else
                  return null;
              },
              title: I18nHelper.translate(context, '$_baseTranslate.dilution'),
              mesure: I18nHelper.translate(context, '$_baseTranslate.ml/h'),
              textColor: widget.textColor,
              controller: diluicaoController,
              keyboardType: TextInputType.number,
              inputFormatters: FormatterField.inputNumberSuppliesFormatter,
              onChange: (value) {
                BlocProvider.of<ClinicEvaluationCubit>(context)
                    .requestClinicEvaluation!
                    .diluicaoVelocidadeInfusao = int.tryParse(value);
                widget.emmitChangedForParent!();
              },
            ),
        ],
      ),
    );
  }
}

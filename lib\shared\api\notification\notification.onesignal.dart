// import '../onsignal.service.dart';
// import 'notification.api.dart';

// class NotificacaoOneSignal extends NotificacaoApi {
//   final OneSignalService service;

//   NotificacaoOneSignal(this.service);

//   @override
//   void listenNotificationsReceived() {
//     service.notificationReceived();
//   }

//   // @override
//   // void listenNotificationOpened() {
//   //   service.notificationOpened();
//   // }
// }

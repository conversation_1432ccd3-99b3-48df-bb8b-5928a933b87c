import 'package:ambulancia_app/models/vehicle_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  VehicleModel? testVehicleModel;
  Map<String, dynamic>? jsonVehicleModel;
  setUpAll(
    () {
      testVehicleModel = VehicleModel(
        codVeiculo: "dsa-ds",
        nomeVeiculo: "ambulancia",
      );
      jsonVehicleModel = {"codVeiculo": "dsa-ds", "nomeVeiculo": "ambulancia"};
    },
  );

  group(
    "isInstanceOf VehicleModel model tests",
    () {
      test("Should be return instance of VehicleModel", () {
        expect(testVehicleModel, isInstanceOf<VehicleModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of VehicleModel to json", () {
      expect(testVehicleModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of VehicleModel from json", () {
      expect(VehicleModel.fromJson(jsonVehicleModel!),
          isInstanceOf<VehicleModel>());
    });
  });

  group(
    "isInstanceOf VehicleModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonVehicleModel!["codVeiculo"], isInstanceOf<String>());
        expect(jsonVehicleModel!["nomeVeiculo"], isInstanceOf<String>());
      });
      test("Can´t return if is null", () {
        expect(jsonVehicleModel!["nomeVeiculo"] == null, false);
        expect(jsonVehicleModel!["nomeVeiculo"] == null, false);
      });
    },
  );
}

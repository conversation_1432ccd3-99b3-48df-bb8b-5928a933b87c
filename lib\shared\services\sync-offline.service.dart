import 'dart:convert';

import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/api/clinic-evaluation.api.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/sync.table.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:http/http.dart' as http;

// ignore: camel_case_types
enum SYNC_TYPE_REQUEST { POST, GET, DELETE, PUT, PATCH, POST_WITH_FILE }

class SyncOfflineService {
  final logger = UnimedLogger(className: 'SyncOfflineService');

  final UnimedHttpClient httpClient;
  SyncOfflineService(this.httpClient);

  Future syncTable() async {
    logger.i('syncTable sincronização iniciada');
    Set<String> errorSet = {};
    try {
      final sqliteTable = SyncTableSQLite();
      final valuesList = await sqliteTable.loadAll();

      if (valuesList != null && valuesList.length > 0) {
        for (final element in valuesList) {
          String key = '${element.customId}';
          if (errorSet.contains(key)) {
            continue;
          }
          try {
            await _request(
              id: element.id!,
              customId: element.customId,
              headers: element.header,
              body: element.jsonRequest,
              url: element.url,
              typeRequest: element.typeRequest,
              authorization: element.authorization,
              fileName: element.fileName,
              jsonFields: element.jsonFields,
              pathFile: element.pathFile,
            );
          } catch (e) {
            logger.e('syncTable error $e');
            errorSet.add(key);
          }
        }

        logger.i(
            'syncTable sincronização finalizada com ${valuesList.length} itens');
      } else {
        logger.i('syncTable Nenhuma requisição em cache');
      }
    } catch (e) {
      logger.e('syncTable error $e');
    }
  }

  Future<void> _request({
    required int id,
    required String customId,
    required String url,
    required SYNC_TYPE_REQUEST typeRequest,
    Map<String, String>? headers,
    String? body,
    String? pathFile,
    String? fileName,
    String? authorization,
    String? jsonFields,
  }) async {
    try {
      late http.Response response;
      late int statusCode;
      String responseStringMultipartFile = "";
      switch (typeRequest) {
        case SYNC_TYPE_REQUEST.PATCH:
          response = await this
              .httpClient
              .patch(Uri.parse(url), headers: headers, body: body);
          statusCode = response.statusCode;
          logger.i(
              "request offline: $url - type: $typeRequest - statusCode: $statusCode - body: ${response.body}");
          break;
        case SYNC_TYPE_REQUEST.POST:
          response = await this
              .httpClient
              .post(Uri.parse(url), headers: headers, body: body);
          statusCode = response.statusCode;
          logger.i("request offline body: $body");
          logger.i(
              "request offline: $url - type: $typeRequest - statusCode: $statusCode - body: ${response.body}");
          break;
        case SYNC_TYPE_REQUEST.GET:
          response =
              await this.httpClient.get(Uri.parse(url), headers: headers);
          statusCode = response.statusCode;
          logger.i(
              "request offline: $url - type: $typeRequest - statusCode: $statusCode - body: ${response.body}");
          break;
        case SYNC_TYPE_REQUEST.PUT:
          response = await this
              .httpClient
              .put(Uri.parse(url), headers: headers, body: body);
          statusCode = response.statusCode;
          logger.i(
              "request offline: $url - type: $typeRequest - statusCode: $statusCode - body: ${response.body}");
          break;
        case SYNC_TYPE_REQUEST.POST_WITH_FILE:
          final request = http.MultipartRequest('POST', Uri.parse(url));
          final fileHttp = await http.MultipartFile.fromPath(
              'arquivos', pathFile ?? '',
              filename: fileName ?? '');
          request.headers['Authorization'] = authorization ?? '';
          request.fields['obj'] = jsonFields ?? '';
          request.files.add(fileHttp);
          final responseFile = await request.send();
          statusCode = responseFile.statusCode;
          responseStringMultipartFile =
              await responseFile.stream.bytesToString();
          logger.i(
              "request offline: $url - type: $typeRequest - statusCode: $statusCode");
          break;
        default:
      }
      if (statusCode == 200 || statusCode == 201) {
        final sqliteTable = SyncTableSQLite();
        await sqliteTable.delete(id: id);
      }
      if (statusCode == 500 &&
          (typeRequest == SYNC_TYPE_REQUEST.PATCH ||
              typeRequest == SYNC_TYPE_REQUEST.POST ||
              typeRequest == SYNC_TYPE_REQUEST.PATCH ||
              typeRequest == SYNC_TYPE_REQUEST.PUT)) {
        await _checkServiceSync(
          customId: customId,
          idServiceSync: id,
          body: body!,
          respondeBody: response.body,
        );
      }
      if (statusCode == 500 &&
          typeRequest == SYNC_TYPE_REQUEST.POST_WITH_FILE) {
        await removeImage(
          idServiceSync: id,
          fileName: fileName!,
          numAtendimento: jsonDecode(jsonFields!)['numAtendimento'],
          responseStringMultipartFile: responseStringMultipartFile,
        );
      }

      if (statusCode == 401 && typeRequest == SYNC_TYPE_REQUEST.POST) {
        await _checkServiceSync(
          customId: customId,
          idServiceSync: id,
          body: body!,
          respondeBody: response.body,
          statusCode: statusCode,
          url: url,
        );
      }
    } catch (e) {
      logger.e('request Offline First catch Exception $e.');
      throw Exception();
    }
  }

  Future<void> _checkServiceSync({
    required String customId,
    required int idServiceSync,
    required String body,
    String? respondeBody,
    int? statusCode,
    String? url,
  }) async {
    final sqliteTable = SyncTableSQLite();

    try {
      final bodyRetorno = jsonDecode(body);
      final numAtendimento = bodyRetorno['numAtendimento'];

      final String updateStatusMovementsEndpoint =
          '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.updateStatusMovements.name}${numAtendimento}';

      final sendConductEndpoint =
          '${SYNC_CATEGORY_API.ConductApi.name}${SYNC_CONDUCTAPI_REQUEST.sendConduct.name}${numAtendimento}';
      final String sendClinicEvaluation =
          '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.sendClinicEvaluation.name}${numAtendimento}';

      if (_substringUserOffline(text: customId) ==
          updateStatusMovementsEndpoint) {
        AttendanceModel? _attendance = await Locator.instance<AttendanceApi>()
            .getAttendanceDetailsOfflineSync(
                '${bodyRetorno['numAtendimento']}');
        if (_attendance != null) {
          if (_attendance.codStatus.toString() == bodyRetorno['codStatus']) {
            await sqliteTable.delete(id: idServiceSync);
          } else if (respondeBody != null &&
              (respondeBody ==
                      "\"Atendimento [$numAtendimento] está com o status mais avançado e não pode regredir para esse status\"" ||
                  respondeBody ==
                      "\"Atendimento já possui esse status de atendimento.\"")) {
            await sqliteTable.delete(id: idServiceSync);
          }
        }
      } else if (_substringUserOffline(text: customId) == sendConductEndpoint) {
        if (statusCode == 401) {
          await sqliteTable.addOrUpdate(
            SyncRecordSQLite(
                customId: customId,
                url: url!,
                typeRequest: SYNC_TYPE_REQUEST.POST,
                jsonRequest: body,
                header: {
                  'Content-Type': 'application/json',
                  'Authorization':
                      'Bearer ${await Locator.instance.get<AuthApi>().tokenPerfilApps()}'
                }),
          );
        } else {
          AttendanceModel _currentAttendance = await Locator.instance<AttendanceApi>()
              .getAttendanceDetail('${numAtendimento}');
          if (_currentAttendance.observacaoConduta ==
                  bodyRetorno['observacaoConduta'] &&
              _currentAttendance.codDiagnostico ==
                  bodyRetorno['codDiagnostico'] &&
              _currentAttendance.descricaoDiagnostico ==
                  bodyRetorno['descricaoDiagnostico'] &&
              _currentAttendance.descricaoTerapeutica ==
                  bodyRetorno['descricaoTerapeutica']) {
            await sqliteTable.delete(id: idServiceSync);
          }
        }
      } else if (_substringUserOffline(text: customId) ==
          sendClinicEvaluation) {
        RequestClinicEvaluation? requestClinicEvaluation = await Locator
            .instance.get<ClinicEvaluationApi>()
            .getClinicEvaluationData('${numAtendimento}');

        if (requestClinicEvaluation != null) {
          String originalJson = jsonEncode(requestClinicEvaluation);
          if (originalJson == body) {
            await sqliteTable.delete(id: idServiceSync);
          } else if (respondeBody != null &&
              respondeBody ==
                  "\"Atendimento [$numAtendimento] já possui avaliação clinica cadastrada.\"") {
            await sqliteTable.delete(id: idServiceSync);
          }
        }
      }
    } catch (e) {}
  }

  Future<void> removeImage({
    required String fileName,
    required String numAtendimento,
    required String responseStringMultipartFile,
    required int idServiceSync,
  }) async {
    final bodyRetorno = jsonDecode(responseStringMultipartFile);
    if (bodyRetorno["mensagem"] ==
        "Anexo [$fileName] já existe no atendimento [$numAtendimento]") {
      final sqliteTable = SyncTableSQLite();
      await sqliteTable.delete(id: idServiceSync);
    }
  }

  String _substringUserOffline({required String text}) {
    final indexOfUnderscore = text.indexOf('_');

    final substringUntilUnderscore = text.substring(0, indexOfUnderscore);

    return substringUntilUnderscore;
  }
}

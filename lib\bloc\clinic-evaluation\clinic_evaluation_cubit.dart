import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/shared/api/clinic-evaluation.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'clinic_evaluation_state.dart';

class ClinicEvaluationCubit extends Cubit<ClinicEvaluationState> {
  ClinicEvaluationCubit() : super(ClinicEvaluationInitial());

  ResponseClinicEvaluation? clinicEvaluationConfig;
  RequestClinicEvaluation? requestClinicEvaluation = RequestClinicEvaluation();
  final logger = UnimedLogger(className: 'ClinicEvaluationCubit');

  loadClinicEvaluation(String numAttendance) async {
    try {
      emit(LoadingClinicEvaluationState());

      clinicEvaluationConfig = await Locator.instance.get<ClinicEvaluationApi>()
          .getClinicEvaluationConfig(numAttendance);

      requestClinicEvaluation = await Locator.instance.get<ClinicEvaluationApi>()
          .getClinicEvaluationData(numAttendance);

      emit(LoadedClinicEvaluationState(
          clinicEvaluationConfig, requestClinicEvaluation));
    } catch (e) {
      logger.e('loadClinicEvaluation Exception $e');
      emit(ErrorLoadClinicEvaluationState('$e'));
    }
  }

  sendClinicEvaluation({required int? numAtendimento}) async {
    try {
      emit(SavingClinicEvaluationState());
      final api = Locator.instance.get<ClinicEvaluationApi>();
      final bool isUpdate = requestClinicEvaluation!.numAtendimento != null;
      rulesToSend();

      requestClinicEvaluation!.numAtendimento = numAtendimento;
      final response = await api.sendClinicEvaluation(
          requestClinicEvaluation: requestClinicEvaluation!,
          isUpdate: isUpdate);

      emit(SuccessSendClinicEvaluationState(response));
    } catch (e) {
      emit(ErrorSendClinicEvaluationState('$e'));
    }
  }

  updateClinicEvaluation(RequestClinicEvaluation requestClinicEvaluation) {
    this.requestClinicEvaluation = requestClinicEvaluation;
  }

  rulesToSend() {
    if (requestClinicEvaluation!.ventilacaoMecanica == 'N') {
      requestClinicEvaluation!.duracaoVentilacaoQtdeDias = null;
      requestClinicEvaluation!.duracaoVentilacaoQtdeHorasDia = null;
    }
    if (!(requestClinicEvaluation!.acessoVenoso ==
        CEVenousAccessCode.VasopressorAmines)) {
      requestClinicEvaluation!.diluicaoVelocidadeInfusao = null;
      requestClinicEvaluation!.aminasVasopressorasQuais = "";
    }
  }
}

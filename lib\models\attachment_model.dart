class AttachmentModel {
  int? sequencial;
  int? numAtendimento;
  String? nomeArquivo;
  int? tipoAnexo;
  String? descricaoTipoAnexo;
  int? motivoAnexo;

  AttachmentModel({
    this.sequencial,
    this.numAtendimento,
    this.nomeArquivo,
    this.tipoAnexo,
    this.descricaoTipoAnexo,
    this.motivoAnexo,
  });

  AttachmentModel.fromJson(Map<String, dynamic> json) {
    sequencial = json['sequencial'];
    numAtendimento = json['numAtendimento'];
    nomeArquivo = json['nomeArquivo'];
    tipoAnexo = json['tipoAnexo'];
    descricaoTipoAnexo = json['descricaoTipoAnexo'];
    motivoAnexo = json['motivoAnexo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['sequencial'] = this.sequencial;
    data['numAtendimento'] = this.numAtendimento;
    data['nomeArquivo'] = this.nomeArquivo;
    data['tipoAnexo'] = this.tipoAnexo;
    data['descricaoTipoAnexo'] = this.descricaoTipoAnexo;
    data['motivoAnexo'] = this.motivoAnexo;
    return data;
  }
}

import 'dart:convert';

import 'package:ambulancia_app/models/service-update.dart';
import 'package:collection/collection.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LastSynchronizationDates {
  final SharedPreferences _prefs;

  const LastSynchronizationDates(this._prefs);

  final String _key = 'lastSynchronizationDates';

  Future<List<RequestServiceDataTime>> getRequestServicesDataTime() async {
    final jsonString = await _prefs.getString(_key);
    if (jsonString != null) {
      final List<dynamic> decodedUpdates = jsonDecode(jsonString);
      return decodedUpdates
          .map((map) => RequestServiceDataTime.fromMap(map))
          .toList();
    }
    return [];
  }

  Future<void> saveServiceDateTimeUpdate(
      {required RequestServiceDataTime serviceUpdate}) async {
    final currentUpdates = await getRequestServicesDataTime();
    final existingUpdateIndex = currentUpdates
        .indexWhere((u) => u.serviceName == serviceUpdate.serviceName);
    if (existingUpdateIndex != -1) {
      currentUpdates[existingUpdateIndex].lastUpdate = serviceUpdate.lastUpdate;
    } else {
      currentUpdates.add(serviceUpdate);
    }
    final serializedUpdates =
        currentUpdates.map((update) => update.toMap()).toList();
    final jsonString = jsonEncode(serializedUpdates);
    await _prefs.setString(_key, jsonString);
  }

  Future<DateTime?> getLastDateTimeUpdate(String serviceName) async {
    final currentUpdates = await getRequestServicesDataTime();
    final existingUpdate = currentUpdates.firstWhereOrNull(
      (update) => update.serviceName == serviceName,
    );
    if (existingUpdate != null) {
      return existingUpdate.lastUpdate;
    }
    return null;
  }

  Future<void> removeAllLastSynchronizationDates() async {
    await _prefs.remove(_key);
  }
}

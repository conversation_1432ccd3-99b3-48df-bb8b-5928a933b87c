class ResponseTableModel {
  late String value;
  late String customId;
  late String category;

  ResponseTableModel({
    required this.value,
    required this.customId,
    required this.category,
  });

  ResponseTableModel.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    customId = json['custom_id'];
    category = json['category'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['value'] = this.value;
    data['custom_id'] = this.customId;
    data['category'] = this.category;

    return data;
  }
}

// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum BatteryStatusBloc {
  unknown,
  charging,
  discharging,
  full,
  attention,
  criticism,
}

class BaterryMode {
  int level;
  BatteryStatusBloc batteryStatusBloc;
  BaterryMode({
    required this.level,
    required this.batteryStatusBloc,
  });
}

final logger = UnimedLogger(className: 'BatteryStatusCubit');

class BatteryStatusCubit extends Cubit<BaterryMode> {
  StreamSubscription<BatteryState>? _batteryStateSubscription;
  late Battery _battery;

  BatteryStatusCubit()
      : super(BaterryMode(
            level: 0, batteryStatusBloc: BatteryStatusBloc.unknown)) {
    _battery = Battery();

    _batteryStateSubscription = _battery.onBatteryStateChanged.listen(
      (batteryState) async {
        int level = await _battery.batteryLevel;
        switch (batteryState) {
          case BatteryState.unknown:
            emit(
              BaterryMode(
                  level: level, batteryStatusBloc: BatteryStatusBloc.unknown),
            );
            break;
          case BatteryState.charging:
            emit(BaterryMode(
                level: level, batteryStatusBloc: BatteryStatusBloc.charging));
            break;
          case BatteryState.discharging:
            emit(BaterryMode(
                level: level,
                batteryStatusBloc: BatteryStatusBloc.discharging));
            break;
          case BatteryState.full:
            emit(BaterryMode(
                level: level, batteryStatusBloc: BatteryStatusBloc.full));
            break;
          case BatteryState.connectedNotCharging:
            emit(BaterryMode(
                level: level, batteryStatusBloc: BatteryStatusBloc.charging));
            break;
        }
      },
      onError: (e) {
        logger.e('BatteryStatusCubit error: ${e.toString()}');
      },
    );
  }

  Future<void> getBatteryLevel() async {
    int level = await _battery.batteryLevel;

    if (state.batteryStatusBloc != BatteryStatusBloc.charging) {
      if (level > 20 && level <= 40) {
        if (state.batteryStatusBloc != BatteryStatusBloc.attention) {
          emit(
            BaterryMode(
                level: level, batteryStatusBloc: BatteryStatusBloc.attention),
          );
        }
      } else if (level <= 20) {
        if (state.batteryStatusBloc != BatteryStatusBloc.criticism) {
          emit(
            BaterryMode(
                level: level, batteryStatusBloc: BatteryStatusBloc.criticism),
          );
        }
      }
    }
  }

  @override
  Future<void> close() {
    _batteryStateSubscription?.cancel();
    return super.close();
  }
}

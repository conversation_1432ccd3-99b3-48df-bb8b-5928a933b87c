import 'package:flutter/material.dart';

class ExpandableText extends StatefulWidget {
  ExpandableText({this.text, this.maxWidhPercent = 0.5});
  final String? text;
  final maxWidhPercent;
  @override
  _ExpandableTextState createState() => new _ExpandableTextState();
}

class _ExpandableTextState extends State<ExpandableText>
    with TickerProviderStateMixin<ExpandableText> {
  bool isExpanded = false;
  final maxHeigh = 50.0;
  @override
  Widget build(BuildContext context) {
    return Column(children: <Widget>[
      AnimatedSize(
          // vsync: this,
          duration: const Duration(milliseconds: 500),
          child: ConstrainedBox(
              constraints: isExpanded
                  ? BoxConstraints(
                      maxWidth: MediaQuery.of(context).size.width *
                          widget.maxWidhPercent)
                  : BoxConstraints(
                      maxHeight: 50.0,
                      maxWidth: MediaQuery.of(context).size.width *
                          widget.maxWidhPercent),
              child: Text(
                widget.text!,
                softWrap: true,
                overflow: TextOverflow.fade,
              ))),
      TextButton(
        child: Icon(Icons.arrow_drop_down),
        onPressed: () => setState(() => isExpanded = !isExpanded),
      )
    ]);
  }
}

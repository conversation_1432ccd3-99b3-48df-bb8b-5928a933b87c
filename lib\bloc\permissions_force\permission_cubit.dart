import 'dart:async';

import 'package:ambulancia_app/bloc/permissions_force/permission_cubit_state.dart';
import 'package:bloc/bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';



class PermissionCubit extends Cubit<PermissionCubitState> {
 PermissionCubit() : super(PermissionInitial()) {
  
  }

  bool onGpsAtivePage = false;

  void gpsDeativated() {
   emit(GPSdeativatedState('GPS desativado'));
  }

  Future<void> checkPermissions() async {
    final locationStatus = await Permission.location.status;
    final cameraStatus = await Permission.camera.status;
    final storageStatus = await Permission.storage.status;

      if (locationStatus != PermissionStatus.granted ||
        cameraStatus != PermissionStatus.granted ||
        storageStatus != PermissionStatus.granted) {
          print('Permission denied');
      emit(PermissionAppDeniedState('Uma ou mais permissões foram negadas.'));
    } else {
      print('Permission granted');
      emit(PermissionLoaded());
    }
  }

 Future<PermissionStatus> checkLocationPermission() async {
    final status = await Permission.location.status;
    return status;
  }

  Future<PermissionStatus> checkCameraPermission() async {
    final status = await Permission.camera.status;
    return status;
  }

  Future<PermissionStatus> checkStoragePermission() async {
    final status = await Permission.storage.status;
    return status;
  }

   Future<PermissionStatus> checkNotificationPermission() async {
    final status = await Permission.notification.status;
    return status;
  }


   Future<bool> isGpsEnabled() async {
    try {
     
      final isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
      print('isGpsEnabled : $isLocationServiceEnabled ');
      return isLocationServiceEnabled;
    }  catch (e) {
     
      print('Erro ao verificar o status do GPS: $e');
      emit(GpsDetectorError('Erro ao verificar permissões: $e'));
      print('emit GpsDetectorError isGpsEnabled'); 
      return false;
    }
  }

}


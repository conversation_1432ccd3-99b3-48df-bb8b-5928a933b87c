import 'dart:async';
import 'dart:io';

import 'package:ambulancia_app/bloc/conduct/conduct_cubit.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class CameraAttachmentPhoto extends StatefulWidget {
  final bool isAttachment;
  final int numAtendimento;
  final int maxQuality;
  final int maxHeight;

  CameraAttachmentPhoto({
    required this.numAtendimento,
    this.isAttachment = false,
    this.maxQuality = 60,
    this.maxHeight = 720,
  });

  @override
  _CameraAttachmentPhotoState createState() => _CameraAttachmentPhotoState();
}

class _CameraAttachmentPhotoState extends State<CameraAttachmentPhoto> {
  final logger = UnimedLogger(className: "CameraAttachmentPhoto");
  CameraController? _cameraController;
  CameraDescription? _cameraFront;
  CameraDescription? _cameraBack;
  List<CameraDescription>? cameras;

  late bool isCameraFront;
  bool imagePickerIsOpen = false;
  String? photoPath;
  bool canPressButton = true;
  bool showAlertCadastrar = false;
  bool _initingCamera = false;
  bool inited = false;
  bool _closing = false;
  bool _loadingSendPhoto = false;

  double cameraButtonSize = 75;
  Color cameraButtonColor = AmbulanceColors.green;

  ResolutionPreset get _resolutionBasedOnBioData {
    if (widget.maxHeight <= 352) {
      return ResolutionPreset.low;
    } else if (widget.maxHeight <= 720) {
      return ResolutionPreset.medium;
    } else if (widget.maxQuality <= 1280) {
      return ResolutionPreset.high;
    } else if (widget.maxQuality <= 1920) {
      return ResolutionPreset.veryHigh;
    } else {
      return ResolutionPreset.ultraHigh;
    }
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!inited) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _initCamera(context);
        inited = true;
      });
    }
    final waitOpenCamera =
        (_cameraController == null || !_cameraController!.value.isInitialized);

    if (waitOpenCamera) {
      return Scaffold(
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SpinKitThreeBounce(
              color: AmbulanceColors.green,
              size: 20,
            )
          ],
        )),
      );
    } else {
      return Scaffold(
        backgroundColor: Colors.white,
        body: photoPath == null
            ? _cameraPreview(context)
            : _photoPreview(context),
      );
    }
  }

  void onTakePhoto({
    required String filePath,
    required BuildContext context,
    required int serviceNumber,
  }) {
    BlocProvider.of<ConductCubit>(context).savePhotoInAppDocumentsAttachment(
      pathFile: filePath,
      serviceNumber: serviceNumber,
    );
  }

  void _initCamera(BuildContext context, {bool front = false}) async {
    try {
      if (_initingCamera) return;
      setState(() => _initingCamera = true);

      cameras ??= await availableCameras();

      if (cameras!.isEmpty)
        throw UnimedException('Dispositivo não possui câmera.');

      cameras!.forEach((camera) {
        _getCameraBackFront(camera);
      });

      isCameraFront = front;
      CameraDescription camera =
          isCameraFront ? _cameraFront ?? cameras!.first : _cameraBack!;

      _cameraController = CameraController(
        camera,
        _resolutionBasedOnBioData,
        imageFormatGroup: ImageFormatGroup.jpeg,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      if (!mounted) {
        return;
      }
      await _cameraController!
          .lockCaptureOrientation(DeviceOrientation.portraitUp);
    } on UnimedException catch (e) {
      logger.e("Error on _initCamera UnimedException $e");

      Alert.open(context,
          title: "Alerta",
          text: '${e.message}',
          callbackClose: () => _close(context));
    } catch (e) {
      logger.e("Error on _initCamera Exception $e");

      Alert.open(context,
          title: "Alerta",
          text: 'Não foi possível acessar a câmera.',
          callbackClose: () => _close(context));
    } finally {
      Future.delayed(Duration(milliseconds: 250), () {
        setState(() => _initingCamera = false);
      });
    }
  }

  void _getCameraBackFront(CameraDescription camera) {
    if (camera.lensDirection == CameraLensDirection.back) {
      _cameraBack ??= camera;
    } else if (camera.lensDirection == CameraLensDirection.front) {
      _cameraFront ??= camera;
    }
  }

  Widget _cameraPreview(BuildContext context) {
    return Stack(
      alignment: AlignmentDirectional.topCenter,
      children: [
        CameraPreview(_cameraController!),
        SafeArea(
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              margin: const EdgeInsets.only(bottom: 0.0),
              height: 75,
              color: AmbulanceColors.green,
              padding: const EdgeInsets.all(12.0),
              child: _cameraOptions(context),
            ),
          ),
        )
      ],
    );
  }

  Widget _photoPreview(BuildContext context) {
    return BlocListener<ConductCubit, ConductState>(
      listenWhen: (previousState, state) {
        if (state is ErrorSendPhotoAttachment) {
          Alert.open(context, title: 'Atenção', text: MessageException.general);
        }

        if (state is SuccessAttachmentState) {
          Navigator.pop(context);
        }
        return true;
      },
      listener: (context, state) {
        if (state is LoadingSendPhotoAttachment) {
          setState(() {
            _loadingSendPhoto = true;
          });
        } else {
          setState(() {
            _loadingSendPhoto = false;
          });
        }
      },
      child: Column(
        children: [
          Image.file(
            File(photoPath!),
            height: 500,
            width: 500,
            filterQuality: FilterQuality.medium,
          ),
          Container(
            padding: const EdgeInsets.all(24.0),
            child: _photoOptions(context),
          ),
          if (_loadingSendPhoto) ...[
            Text(
              "Enviando informações, aguarde...",
              style: TextStyle(fontSize: 24),
            ),
            SpinKitThreeBounce(
              size: 30,
              color: AmbulanceColors.green,
            )
          ],
        ],
      ),
    );
  }

  Widget _cameraOptions(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          flex: 1,
          child: _buttonClose(context),
        ),
        Expanded(
          flex: 1,
          child: _buttonTakePicture(context),
        ),
        Expanded(flex: 1, child: _buttonChangeCameraOrientation(context)),
      ],
    );
  }

  Widget _photoOptions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: _loadingSendPhoto
              ? null
              : () {
                  Future.delayed(Duration(milliseconds: 250), () {
                    setState(() {
                      photoPath = null;
                    });
                  });
                },
          child: Icon(
            Icons.cached,
            color: _loadingSendPhoto
                ? AmbulanceColors.grayDark
                : AmbulanceColors.greenLight3,
            size: 32,
          ),
        ),
        SizedBox(
          width: 20,
        ),
        FloatingActionButton(
          backgroundColor: _loadingSendPhoto
              ? AmbulanceColors.grayDark
              : AmbulanceColors.greenLight3,
          onPressed: _closing || _loadingSendPhoto
              ? null
              : () {
                  onTakePhoto(
                    filePath: File(photoPath!).path,
                    context: context,
                    serviceNumber: widget.numAtendimento,
                  );
                },
          child: Icon(
            Icons.check,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buttonTakePicture(BuildContext context) {
    return InkWell(
      key: Key('button_take_picture'),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 100),
        width: cameraButtonSize,
        height: cameraButtonSize,
        padding: EdgeInsets.all(3),
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 3)),
        child: Container(
          decoration: BoxDecoration(
            color: cameraButtonColor,
            shape: BoxShape.circle,
          ),
        ),
      ),
      onTap: !canPressButton
          ? null
          : () async {
              setState(() {
                cameraButtonSize = 85;
                cameraButtonColor = Colors.red;
              });

              await _takePhoto(context);

              setState(() {
                cameraButtonSize = 75;
                cameraButtonColor = AmbulanceColors.green;
              });
            },
    );
  }

  Future<void> _takePhoto(BuildContext context) async {
    try {
      if (canPressButton) {
        setState(() {
          canPressButton = false;
        });

        if (!_cameraController!.value.isInitialized) {
          await _cameraController!.initialize();
        }

        final XFile file = await _cameraController!.takePicture();

        File? compressedFile = await _compressAndGetFile(File(file.path));

        //wait to save file
        await Future.delayed(Duration(milliseconds: 250));
        setState(() {
          photoPath = compressedFile!.path;
        });
      }
    } catch (e) {
      _initCamera(context);
      logger.e("Error on _takePhoto $e");
      Alert.open(context,
          title: 'Anexar Foto',
          text: 'Erro ao anexar foto. Tente novamente',
          callbackClose: () {});
    } finally {
      setState(() {
        canPressButton = true;
      });
    }
  }

  Widget _buttonChangeCameraOrientation(BuildContext context) {
    return TextButton(
      onPressed: _initingCamera || !canPressButton
          ? null
          : () {
              _initCamera(context, front: !isCameraFront);
            },
      child: Icon(
        Icons.flip_camera_ios_outlined,
        color: Colors.white,
        size: 32,
      ),
    );
  }

  Widget _buttonClose(BuildContext context) {
    return Platform.isAndroid
        ? Container()
        : TextButton(
            child: FittedBox(
              child: Text(
                "Cancelar",
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            onPressed: () => _close(context));
  }

  void _close(BuildContext context) {
    if (!_closing && Navigator.of(context).canPop()) {
      _closing = true;
      Navigator.of(context).pop();
    }
  }

  Future<File?> _compressAndGetFile(File file) async {
    try {
      final filePath = file.absolute.path;
      final lastIndex = filePath.lastIndexOf(RegExp(r'.jp'));
      final splitted = filePath.substring(0, (lastIndex));
      final targetPath = "${splitted}_out${filePath.substring(lastIndex)}";
      final result = await FlutterImageCompress.compressAndGetFile(
        filePath,
        targetPath,
        quality: widget.maxQuality,
      );

      return File(result!.path);
    } catch (e) {
      return file;
    }
  }
}

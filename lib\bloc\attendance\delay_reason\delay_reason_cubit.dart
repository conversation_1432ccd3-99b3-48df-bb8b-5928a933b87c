import 'package:ambulancia_app/models/delay_reason_model.dart';
import 'package:ambulancia_app/shared/api/attendance/delay_reason_api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'delay_reason_state.dart';

class DelayReasonCubit extends Cubit<DelayReasonState> {
  DelayReasonCubit() : super(DelayReasonInitial());

  List<DelayReasonModel>? _motives;
  List<DelayReasonModel>? getMotives() => _motives;

  Future<void> getAttendanceDelayReasson() async {
    try {
      emit(LoadingDelayReasonsState());

      _motives = await Locator.instance<DelayReasonApi>().getDelayReasons();

      emit(LoadedDelayReasonsState(delayReasons: _motives));
    } catch (ex) {
      emit(ErrorLoadDelayReasonsState('$ex'));
    }
  }
}

part of 'conduct_attachment_cubit.dart';

abstract class ConductAttachmentState extends Equatable {
  const ConductAttachmentState();

  @override
  List<Object?> get props => [];
}

class ConductAttachmentInit extends ConductAttachmentState {}

class LoadingAttachmentState extends ConductAttachmentState {
  @override
  List<Object> get props => [];
}

class ErrorLoadAttachmentState extends ConductAttachmentState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorLoadAttachmentState(this.message);
}

class LoadedAttachmentState extends ConductAttachmentState {
  final String message;
  @override
  List<Object> get props => [message];

  LoadedAttachmentState(this.message);
}

class SuccessAttachmentState extends ConductAttachmentState {
  final List<AttachmentModel> listAttachment;
  @override
  List<Object?> get props => [listAttachment];

  SuccessAttachmentState(this.listAttachment);
}

class LoadingDeleteAttachmentState extends ConductAttachmentState {
  @override
  List<Object> get props => [];
}

class ErrorDeleteAttachmentState extends ConductAttachmentState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorDeleteAttachmentState(this.message);
}

class SuccessDeleteAttachmentState extends ConductAttachmentState {
  @override
  List<Object?> get props => [];
}

import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_notification/offline_first_notification_cubit.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/header/function/logout_handler.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class SyncInformationAlert {
  static void open(
    BuildContext context, {
    bool barrierDismissible = false,
  }) async {
    final _baseTranslate = 'home.header';
    final BuildContext contextLogout = context;

    if (context
            .read<OfflineFirstNotificationCubit>()
            .amountsOfDataToBeSynchronizedawait !=
        0) {
      return showGeneralDialog<void>(
        context: context,
        barrierDismissible: barrierDismissible,
        barrierColor: Colors.black.withOpacity(0.5),
        transitionDuration: Duration(milliseconds: 400),
        pageBuilder: (BuildContext context, Animation animation,
            Animation secondaryAnimation) {
          return Container();
        },
        transitionBuilder: (BuildContext context, a1, a2, widget) {
          final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

          return PopScope(
            canPop: barrierDismissible,
            child: Transform(
              transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
              child: Opacity(
                opacity: a1.value,
                child: BlocConsumer<OfflineFirstCubit, OfflineFirstState>(
                  listener: (context, stateOfflineFirstCubit) {
                    if (stateOfflineFirstCubit
                            is OfflineFinishingTheSynchronization &&
                        context
                                .read<OfflineFirstNotificationCubit>()
                                .amountsOfDataToBeSynchronizedawait ==
                            0) {
                      Navigator.of(context).pop();
                      LogoutHandler().logout(contextLogout);
                    }

                    if (stateOfflineFirstCubit is OfflineFirstInitial) {
                      Alert.open(context,
                          text: "Aguarde a inicialização da aplicação.");
                    }
                  },
                  builder: (context, stateOfflineFirstCubit) {
                    bool _auxSoundCheck =
                        (context.read<OfflineFirstCubit>().forceSync == true ||
                            context.read<OfflineFirstCubit>().initSync == true);

                    bool _valitadLoad = _auxSoundCheck &&
                        stateOfflineFirstCubit is OfflineFirstStartingSync;
                    return AlertDialog(
                      backgroundColor: Colors.white,
                      title: Text(
                        I18nHelper.translate(
                            context, '$_baseTranslate.dataSyncMessage.title'),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AmbulanceColors.greenDark,
                        ),
                      ),
                      content: Text(
                        I18nHelper.translate(
                          context,
                          '$_baseTranslate.dataSyncMessage.text',
                        ),
                      ),
                      actions: [
                        BlocBuilder<OfflineFirstCubit, OfflineFirstState>(
                          builder: (context, state) {
                            return BlocBuilder<ConnectivityCubit,
                                    ConnectivityState>(
                                builder: (context, stateConnection) {
                              return ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AmbulanceColors.green,
                                  textStyle: TextStyle(
                                    color: Colors.white,
                                  ),
                                ),
                                child: _valitadLoad
                                    ? SizedBox(
                                        width: 80,
                                        child: SpinKitThreeBounce(
                                          size: 22,
                                          color: AmbulanceColors.gray,
                                        ),
                                      )
                                    : Text(
                                        I18nHelper.translate(
                                          context,
                                          '$_baseTranslate.dataSyncMessage.textButton',
                                        ).toUpperCase(),
                                      ),
                                onPressed: _valitadLoad
                                    ? null
                                    : () {
                                        if (stateConnection
                                            is ConnectivityOnlineState) {
                                          context
                                              .read<OfflineFirstCubit>()
                                              .forceSyncDatabase(
                                                  connectedToTheInternet: true);
                                        } else {
                                          Alert.open(
                                            context,
                                            title: I18nHelper.translate(
                                              context,
                                              '$_baseTranslate.attentionNoInternet.title',
                                            ),
                                            text: I18nHelper.translate(
                                              context,
                                              '$_baseTranslate.attentionNoInternet.text',
                                            ),
                                          );
                                        }
                                      },
                              );
                            });
                          },
                        ),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AmbulanceColors.orange,
                            textStyle: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                          child: Text('Sair mesmo assim'.toUpperCase()),
                          onPressed: () {
                            context
                                .read<AttendanceListCubit>()
                                .listattendances
                                .clear();
                            context.read<AuthCubit>().signout();
                            Navigator.of(context).pop();
                          },
                        ),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AmbulanceColors.redClose,
                            textStyle: TextStyle(
                              color: Colors.white,
                            ),
                          ),
                          child: Text('Fechar'.toUpperCase()),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          );
        },
      );
    } else {
      LogoutHandler().logout(context);
    }
  }
}

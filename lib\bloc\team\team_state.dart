part of 'team_cubit.dart';

abstract class TeamState extends Equatable {
  const TeamState();

  @override
  List<Object> get props => [];
}

class TeamInitial extends TeamState {}

class LoadingTeamState extends TeamState {}

class LoadedTeamStateCache extends TeamState {
  final TeamVehicle teamVehicle;
  LoadedTeamStateCache({required this.teamVehicle});
}

class LoadedTeamState extends TeamState {
  final FuncionarioModel? motoristaSocrorrista, tecnicoEnfermagem;
  LoadedTeamState({this.motoristaSocrorrista, this.tecnicoEnfermagem});
}

class ErrorTeamState extends TeamState {
  final message;
  ErrorTeamState(this.message);
}

import 'package:ambulancia_app/bloc/alert_baterry/cubit/baterry_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_cancel/attendance_cancel_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_movemets/motive_attendance/motive_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_route/attendance_route_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/closse_attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/delay_reason/delay_reason_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/pre_hospital_discharge/pre_hospital_discharge_clinical_picture_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/pre_hospital_discharge/pre_hospital_discharge_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/signal_protocol/signal_protocol_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/subscription/cubit/subscription_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_address/attendance_address_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_team/attendance_team_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/bloc/conduct/conduct_attachment_img/conduct_attachment_img_cubit.dart';
import 'package:ambulancia_app/bloc/conduct/conduct_cubit.dart';
import 'package:ambulancia_app/bloc/conduct/reclassification/reclassification_attendance_bloc_cubit.dart';
import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/destiny/destiny_cubit.dart';
import 'package:ambulancia_app/bloc/new_service/cubit/is_button_save_disabled.dart';
import 'package:ambulancia_app/bloc/new_service/cubit/new_service_able_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/last-synchronization-dates/last_synchronization_dates_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_notification/offline_first_notification_cubit.dart';
import 'package:ambulancia_app/bloc/permissions_force/permission_cubit.dart';
import 'package:ambulancia_app/bloc/register_photo/photo_cubit.dart';
import 'package:ambulancia_app/bloc/signature_pad/signature_cubit.dart';
import 'package:ambulancia_app/bloc/splash_screen/cubit/splash_screen_cubit.dart';
import 'package:ambulancia_app/bloc/supply/supply_cubit.dart';
import 'package:ambulancia_app/bloc/team/team_cubit.dart';
import 'package:ambulancia_app/bloc/update-version/update_version_cubit.dart';
import 'package:ambulancia_app/bloc/vehicle/vehicle_cubit.dart';
import 'package:ambulancia_app/screens/splash/splash-screen.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/services/connectivity.service.dart';
import 'package:ambulancia_app/shared/services/version.service.dart';
import 'package:ambulancia_app/shared/utils/router_observer.dart';
import 'package:ambulancia_app/shared/widgets/flavor_banner.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_i18n/flutter_i18n_delegate.dart';
import 'package:flutter_i18n/loaders/e2e_file_translation_loader.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

class AmbulanciaUnimed extends StatelessWidget {
  final OfflineFirstCubit offlineFirstCubit;
  final ConnectivityCubit connectivityCubit;
  AmbulanciaUnimed({
    required this.offlineFirstCubit,
    required this.connectivityCubit,
  }) {
    Locator.setup();
    Locator.registerRemoteConfigService();
    Locator.initializeSharedPreferences();

    Locator.instance.get<ConnectivityService>().init();

    Locator.instance.get<VersionService>().getInfo();
  }
  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    return MultiBlocProvider(
      providers: [
        BlocProvider<PermissionCubit>(
          create: (context) => PermissionCubit(),
        ),
        BlocProvider<DestinyCubit>(create: (context) => DestinyCubit()),
        BlocProvider<AttendanceCancelCubit>(
            create: (context) => AttendanceCancelCubit()),
        BlocProvider<SupplyCubit>(create: (context) => SupplyCubit()),
        BlocProvider<AuthCubit>(create: (context) => AuthCubit()),
        BlocProvider<TeamCubit>(create: (context) => TeamCubit()),
        BlocProvider<AttendanceTeamCubit>(
            create: (context) => AttendanceTeamCubit()),
        BlocProvider<VehicleCubit>(create: (context) => VehicleCubit()),
        BlocProvider<AttendanceListCubit>(
            create: (context) => AttendanceListCubit()),
        BlocProvider<AttendanceCubit>(create: (context) => AttendanceCubit()),
        BlocProvider<AttendanceRouteCubit>(
            create: (context) => AttendanceRouteCubit()),
        BlocProvider<ConnectivityCubit>(create: (context) => connectivityCubit),
        BlocProvider<ConductCubit>(create: (context) => ConductCubit()),
        BlocProvider<ClinicEvaluationCubit>(
            create: (context) => ClinicEvaluationCubit()),
        BlocProvider<AttendanceMovementsCubit>(
            create: (context) => AttendanceMovementsCubit()),
        BlocProvider<MotiveCubit>(create: (context) => MotiveCubit()),
        BlocProvider<AttendanceAddressCubit>(
            create: (context) => AttendanceAddressCubit()),
        BlocProvider<UpdateVersionCubit>(
            create: (context) => UpdateVersionCubit()),
        BlocProvider<RegisterPhotoCubit>(
            create: (context) => RegisterPhotoCubit()),
        BlocProvider<SignatureCubit>(create: (context) => SignatureCubit()),
        BlocProvider<SplashScreenCubit>(
            create: (context) => SplashScreenCubit()),
        BlocProvider<BatteryStatusCubit>(
            create: (context) => BatteryStatusCubit()),
        BlocProvider<SubscriptionCubit>(
            create: (context) => SubscriptionCubit()),
        BlocProvider<ConductAttachmentImgCubit>(
            create: (context) => ConductAttachmentImgCubit()),
        BlocProvider<NewServiceAbleCubit>(
          create: (context) => NewServiceAbleCubit(),
        ),
        BlocProvider<IsButtonSaveDisabledNewServiceAbleCubit>(
          create: (context) => IsButtonSaveDisabledNewServiceAbleCubit(),
        ),
        BlocProvider<OfflineFirstCubit>(
          create: (context) => offlineFirstCubit,
        ),
        BlocProvider<OfflineFirstNotificationCubit>(
          create: (context) => OfflineFirstNotificationCubit(),
        ),
        BlocProvider<LastSynchronizationDatesCubit>(
          create: (context) => LastSynchronizationDatesCubit(),
        ),
        BlocProvider<ReclassificationtAttendanceCubit>(
          create: (context) => ReclassificationtAttendanceCubit(),
        ),
        BlocProvider<DelayReasonCubit>(
          create: (context) => DelayReasonCubit(),
        ),
        BlocProvider<CloseAttendanceCubit>(
          create: (context) => CloseAttendanceCubit(),
        ),
        BlocProvider<SignalProtocolCubit>(
          create: (context) => SignalProtocolCubit(),
        ),
        BlocProvider<PreHospitalDischargeCubit>(
          create: (context) => PreHospitalDischargeCubit(),
        ),
        BlocProvider<PreHospitalDischargeClinicalPictureCubit>(
          create: (context) => PreHospitalDischargeClinicalPictureCubit(),
        ),
      ],
      child: MaterialApp(
        title: 'Ambulancia Unimed',
        theme: ThemeAmbulancia.green(),
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          DefaultCupertinoLocalizations.delegate,
          FlutterI18nDelegate(
            translationLoader: E2EFileTranslationLoader(
              useCountryCode: true,
              basePath: 'assets/i18n',
              fallbackFile: 'pt_BR',
              forcedLocale: const Locale('pt', 'BR'),
            ),
          ),
        ],
        supportedLocales: [
          const Locale('pt', 'BR'),
        ],
        home: FlavorBanner(child: SplashScreen()),
        navigatorObservers: [
          routeObserver,
        ],
      ),
    );
  }
}

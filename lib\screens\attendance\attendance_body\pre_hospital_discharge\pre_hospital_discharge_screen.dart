import 'package:ambulancia_app/bloc/attendance/pre_hospital_discharge/pre_hospital_discharge_clinical_picture_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/pre_hospital_discharge/pre_hospital_discharge_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:ambulancia_app/models/config_app_ambulancia_constants_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/conduct/assistance-protocols/assistance_protocols_attendance.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/conduct/close_attendance/modal_reclassification.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/buttons/primary_button.dart';
import 'package:ambulancia_app/shared/widgets/modal/modal_photo_medical.dart';
import 'package:ambulancia_app/shared/widgets/text_field_custom.dart';
import 'package:ambulancia_app/shared/widgets/title/title_form.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:unimed_select/unimed-select.dart';

class PreHospitalDischargeScreen extends StatefulWidget {
  final AttendanceModel attendanceModel;

  const PreHospitalDischargeScreen({
    super.key,
    required this.attendanceModel,
  });

  @override
  State<PreHospitalDischargeScreen> createState() =>
      _PreHospitalDischargeScreenState();
}

class _PreHospitalDischargeScreenState
    extends State<PreHospitalDischargeScreen> {
  int? _selectedHighReason;
  int? _selectedClinicalFramework;

  final TextEditingController _newClinicalTableController =
      TextEditingController();
  final TextEditingController _doctorReceivedontroller =
      TextEditingController();
  final TextEditingController _highDiagnosisController =
      TextEditingController();
  final TextEditingController _observationsHighController =
      TextEditingController();

  final TextEditingController _observationReasonForDischargeController =
      TextEditingController();

  final TextEditingController _reasonForHospitalDischargeController =
      TextEditingController();

  final TextEditingController _clinicalFrameworkController =
      TextEditingController();

  DateTime _dataAlta = DateTime.now();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final String _baseTranslate = 'preHospitalDischarge';

  @override
  void initState() {
    context.read<PreHospitalDischargeCubit>().getReasonPreHospitalDischarge(
        codUnimed: widget.attendanceModel.codUnimed);
    context
        .read<PreHospitalDischargeClinicalPictureCubit>()
        .getPreHospitalDischargeClinicalPicture(
            codUnimed: widget.attendanceModel.codUnimed);
    super.initState();
  }

  @override
  void dispose() {
    _newClinicalTableController.dispose();
    _doctorReceivedontroller.dispose();
    _highDiagnosisController.dispose();
    _observationsHighController.dispose();
    _reasonForHospitalDischargeController.dispose();
    _observationReasonForDischargeController.dispose();
    _clinicalFrameworkController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _dataAlta,
      firstDate: DateTime(2000),
      lastDate: DateTime(2030),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            primaryColor: AmbulanceColors.green,
            colorScheme: ColorScheme.light(primary: AmbulanceColors.green),
            buttonTheme: ButtonThemeData(textTheme: ButtonTextTheme.primary),
          ),
          child: Transform.scale(
            scale: 1.33,
            child: child,
          ),
        );
      },
    );
    if (picked != null && picked != _dataAlta) {
      setState(() {
        _dataAlta = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(_dataAlta),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              dialHandColor: AmbulanceColors.green,
              dialBackgroundColor: AmbulanceColors.grayLight,
              hourMinuteTextColor: AmbulanceColors.green,
              hourMinuteShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: AmbulanceColors.green),
              ),
              dayPeriodTextColor: AmbulanceColors.green,
            ),
          ),
          child: Transform.scale(
            scale: 1.33,
            child: child,
          ),
        );
      },
    );
    if (picked != null) {
      setState(() {
        _dataAlta = DateTime(
          _dataAlta.year,
          _dataAlta.month,
          _dataAlta.day,
          picked.hour,
          picked.minute,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    ConfigAppAmbulanciaConstants _configAppAmbulanciaConstants =
        context.read<AuthCubit>().configAppAmbulanciaConstants;
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius)),
          insetPadding: EdgeInsets.only(
            top: ConstantsTheme.doublePadding * 3,
            bottom: ConstantsTheme.doublePadding,
            left: ConstantsTheme.doublePadding,
            right: ConstantsTheme.doublePadding,
          ),
          content: Container(
            width: constraints.maxWidth * 0.75,
            height: constraints.maxHeight * 0.95,
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TitleForm(
                    title:
                        I18nHelper.translate(context, '$_baseTranslate.title'),
                  ),
                  SizedBox(height: ConstantsTheme.doublePadding * 2),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.only(bottom: 32.0),
                      child: Column(
                        children: [
                          BlocBuilder<PreHospitalDischargeCubit,
                                  PreHospitalDischargeState>(
                              builder: (context, state) {
                            return Padding(
                              padding: EdgeInsets.only(
                                  bottom: ConstantsTheme.doublePadding),
                              child: UnimedSelect<int>(
                                title: I18nHelper.translate(context,
                                        '$_baseTranslate.reasonForDischarge') +
                                    ' *',
                                fontStyleTitle: TextStyle(fontSize: 20),
                                controller:
                                    _reasonForHospitalDischargeController,
                                isLoading:
                                    state is LoadingPreHospitalDischargesState,
                                hasError: state
                                    is ErrorLoadPreHospitalDischargesState,
                                onErrorRetry: () {
                                  context
                                      .read<PreHospitalDischargeCubit>()
                                      .getReasonPreHospitalDischarge(
                                          codUnimed:
                                              widget.attendanceModel.codUnimed);
                                },
                                items: state is LoadedPreHospitalDischargeState
                                    ? (state.listReasonPreHospitalDischargeModel !=
                                                null &&
                                            state
                                                .listReasonPreHospitalDischargeModel!
                                                .isNotEmpty)
                                        ? state
                                            .listReasonPreHospitalDischargeModel!
                                            .map((e) =>
                                                UnimedSelectItemModel<int>(
                                                  value: e.codeReason,
                                                  label: e.nameReason,
                                                ))
                                            .toList()
                                        : <UnimedSelectItemModel<int>>[]
                                    : <UnimedSelectItemModel<int>>[],
                                onSelect: (int? value) {
                                  setState(() {
                                    _selectedHighReason = value;
                                  });
                                },
                                validator: (value) {
                                  final String trimmedValue =
                                      value!.replaceAll(' ', '');
                                  if (trimmedValue.isEmpty)
                                    return I18nHelper.translate(context,
                                        '$_baseTranslate.requiredFieldError');
                                  else
                                    return null;
                                },
                              ),
                            );
                          }),
                          Visibility(
                            visible: _selectedHighReason ==
                                _configAppAmbulanciaConstants
                                    .hospitalDischarge!.optionOutherCode,
                            child: TextFieldCustom(
                              controller:
                                  _observationReasonForDischargeController,
                              validator: (value) {
                                final String trimmedValue =
                                    value!.replaceAll(' ', '');
                                if (trimmedValue.isEmpty)
                                  return I18nHelper.translate(context,
                                      '$_baseTranslate.requiredFieldError');
                                else
                                  return null;
                              },
                              title: I18nHelper.translate(context,
                                      '$_baseTranslate.noteReasonForDischarge') +
                                  ' *',
                              label: '',
                            ),
                          ),
                          _dateTime(),
                          BlocBuilder<PreHospitalDischargeClinicalPictureCubit,
                                  PreHospitalDischargeClinicalPictureState>(
                              builder: (context, state) {
                            return Padding(
                              padding: EdgeInsets.only(
                                  bottom: ConstantsTheme.doublePadding),
                              child: UnimedSelect<int>(
                                title: I18nHelper.translate(context,
                                        '$_baseTranslate.clinicalPicture') +
                                    ' *',
                                fontStyleTitle: TextStyle(fontSize: 20),
                                controller: _clinicalFrameworkController,
                                isLoading: state
                                    is LoadingPreHospitalDischargeClinicalPicturesState,
                                hasError: state
                                    is ErrorLoadPreHospitalDischargeClinicalPicturesState,
                                onErrorRetry: () {
                                  context
                                      .read<
                                          PreHospitalDischargeClinicalPictureCubit>()
                                      .getPreHospitalDischargeClinicalPicture(
                                          codUnimed:
                                              widget.attendanceModel.codUnimed);
                                },
                                items: state
                                        is LoadedPreHospitalDischargeClinicalPictureState
                                    ? (state.listPreHospitalDischargeClinicalPictureModel !=
                                                null &&
                                            state
                                                .listPreHospitalDischargeClinicalPictureModel!
                                                .isNotEmpty)
                                        ? state
                                            .listPreHospitalDischargeClinicalPictureModel!
                                            .map((e) =>
                                                UnimedSelectItemModel<int>(
                                                  value: e.code,
                                                  label: e.nameClinicalPicture,
                                                ))
                                            .toList()
                                        : <UnimedSelectItemModel<int>>[]
                                    : <UnimedSelectItemModel<int>>[],
                                onSelect: (int? value) {
                                  setState(() {
                                    _selectedClinicalFramework = value;
                                  });
                                },
                                validator: (value) {
                                  final String trimmedValue =
                                      value!.replaceAll(' ', '');
                                  if (trimmedValue.isEmpty)
                                    return I18nHelper.translate(context,
                                        '$_baseTranslate.requiredFieldError');
                                  else
                                    return null;
                                },
                              ),
                            );
                          }),
                          Visibility(
                            visible: _selectedClinicalFramework ==
                                _configAppAmbulanciaConstants.hospitalDischarge!
                                    .optionOutherNewFrameCode,
                            child: TextFieldCustom(
                              controller: _newClinicalTableController,
                              title: I18nHelper.translate(context,
                                      '$_baseTranslate.newClinicalPicture') +
                                  ' *',
                              label: '',
                              validator: (value) {
                                final String trimmedValue =
                                    value!.replaceAll(' ', '');
                                if (trimmedValue.isEmpty)
                                  return I18nHelper.translate(context,
                                      '$_baseTranslate.requiredFieldError');
                                else
                                  return null;
                              },
                            ),
                          ),
                          TextFieldCustom(
                            controller: _doctorReceivedontroller,
                            validator: (value) {
                              return null;
                            },
                            title: I18nHelper.translate(
                              context,
                              '$_baseTranslate.doctorWhoReceivedThePatient',
                            ),
                            label: '',
                          ),
                          TextFieldCustom(
                            controller: _highDiagnosisController,
                            validator: (value) {
                              final String trimmedValue =
                                  value!.replaceAll(' ', '');
                              if (trimmedValue.isEmpty)
                                return I18nHelper.translate(context,
                                    '$_baseTranslate.requiredFieldError');
                              else
                                return null;
                            },
                            title: I18nHelper.translate(context,
                                    '$_baseTranslate.dischargeDiagnosis') +
                                ' *',
                            label: '',
                            keyboardType: TextInputType.multiline,
                            maxLines: 4,
                          ),
                          TextFieldCustom(
                            controller: _observationsHighController,
                            validator: (value) {
                              final String trimmedValue =
                                  value!.replaceAll(' ', '');
                              if (trimmedValue.isEmpty)
                                return I18nHelper.translate(context,
                                    '$_baseTranslate.requiredFieldError');
                              else
                                return null;
                            },
                            title: I18nHelper.translate(context,
                                    '$_baseTranslate.highObservations') +
                                ' *',
                            label: '',
                            keyboardType: TextInputType.multiline,
                            maxLines: 4,
                          ),
                          SizedBox(height: 40)
                        ],
                      ),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        PrimaryButton(
                          backgroundColor: AmbulanceColors.green,
                          text: I18nHelper.translate(
                                  context, '$_baseTranslate.endService')
                              .toUpperCase(),
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              if (widget
                                      .attendanceModel.uniurgProtocoloDoencas !=
                                  null) {
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return AssistanceProtocolsAttendancePopup(
                                      attendanceModel: widget.attendanceModel,
                                      closeAttendanceV2: CloseAttendanceV2Model(
                                        recordType: I18nHelper.translate(
                                            context, '$_baseTranslate.closing'),
                                        reclassify: widget
                                            .attendanceModel.isReclassifica,
                                        preHospitalDischargeObs:
                                            _observationsHighController.text,
                                        preHospDischargeDateTime:
                                            '${_dataAlta.day.toString().padLeft(2, '0')}/${_dataAlta.month.toString().padLeft(2, '0')}/${_dataAlta.year} ${_dataAlta.hour.toString().padLeft(2, '0')}:${_dataAlta.minute.toString().padLeft(2, '0')}:${_dataAlta.second.toString().padLeft(2, '0')}',
                                        preHospDischargeReceivingDoctor:
                                            _doctorReceivedontroller.text,
                                        preHospitalDischargeDiagnosis:
                                            _highDiagnosisController.text,
                                        otherPreHospDischargeReason:
                                            _observationReasonForDischargeController
                                                .text,
                                        newPreHospDischargeClinicalPicture:
                                            _newClinicalTableController.text,
                                        preHospDischargeClinicalPictureCode:
                                            _selectedClinicalFramework!,
                                        preHospDischargeReasonCode:
                                            _selectedHighReason!,
                                        numAtendimento: widget
                                            .attendanceModel.numAtendimento
                                            .toString(),
                                      ),
                                    );
                                  },
                                );
                              } else if (widget
                                  .attendanceModel.solicitaReclassifTipoAtend) {
                                showDialog(
                                  context: context,
                                  builder: (BuildContext context) {
                                    return ReclassifyPopup(
                                      attendanceModel: widget.attendanceModel,
                                      closeAttendanceV2: CloseAttendanceV2Model(
                                        recordType: I18nHelper.translate(
                                            context, '$_baseTranslate.closing'),
                                        reclassify: widget
                                            .attendanceModel.isReclassifica,
                                        preHospitalDischargeObs:
                                            _observationsHighController.text,
                                        preHospDischargeDateTime:
                                            '${_dataAlta.day.toString().padLeft(2, '0')}/${_dataAlta.month.toString().padLeft(2, '0')}/${_dataAlta.year} ${_dataAlta.hour.toString().padLeft(2, '0')}:${_dataAlta.minute.toString().padLeft(2, '0')}:${_dataAlta.second.toString().padLeft(2, '0')}',
                                        preHospDischargeReceivingDoctor:
                                            _doctorReceivedontroller.text,
                                        preHospitalDischargeDiagnosis:
                                            _highDiagnosisController.text,
                                        otherPreHospDischargeReason:
                                            _observationReasonForDischargeController
                                                .text,
                                        newPreHospDischargeClinicalPicture:
                                            _newClinicalTableController.text,
                                        preHospDischargeClinicalPictureCode:
                                            _selectedClinicalFramework!,
                                        preHospDischargeReasonCode:
                                            _selectedHighReason!,
                                        numAtendimento: widget
                                            .attendanceModel.numAtendimento
                                            .toString(),
                                      ),
                                    );
                                  },
                                );
                              } else {
                                openModalPhotoMedical(
                                  context: context,
                                  attendanceModel: widget.attendanceModel,
                                  recordType: I18nHelper.translate(
                                      context, '$_baseTranslate.closing'),
                                  closeAttendanceV2: CloseAttendanceV2Model(
                                    recordType: I18nHelper.translate(
                                        context, '$_baseTranslate.closing'),
                                    reclassify:
                                        widget.attendanceModel.isReclassifica,
                                    preHospitalDischargeObs:
                                        _observationsHighController.text,
                                    preHospDischargeDateTime:
                                        '${_dataAlta.day.toString().padLeft(2, '0')}/${_dataAlta.month.toString().padLeft(2, '0')}/${_dataAlta.year} ${_dataAlta.hour.toString().padLeft(2, '0')}:${_dataAlta.minute.toString().padLeft(2, '0')}:${_dataAlta.second.toString().padLeft(2, '0')}',
                                    preHospDischargeReceivingDoctor:
                                        _doctorReceivedontroller.text,
                                    preHospitalDischargeDiagnosis:
                                        _highDiagnosisController.text,
                                    otherPreHospDischargeReason:
                                        _observationReasonForDischargeController
                                            .text,
                                    newPreHospDischargeClinicalPicture:
                                        _newClinicalTableController.text,
                                    preHospDischargeClinicalPictureCode:
                                        _selectedClinicalFramework!,
                                    preHospDischargeReasonCode:
                                        _selectedHighReason!,
                                    numAtendimento: widget
                                        .attendanceModel.numAtendimento
                                        .toString(),
                                  ),
                                );
                              }
                            }
                          },
                        ),
                        PrimaryButton(
                          backgroundColor: AmbulanceColors.redClose,
                          text: I18nHelper.translate(
                                  context, '$_baseTranslate.toClose')
                              .toUpperCase(),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _dateTime() {
    return Padding(
      padding: EdgeInsets.only(bottom: ConstantsTheme.doublePadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            I18nHelper.translate(context, '$_baseTranslate.dischargeDate') +
                ' *',
            style: TextStyle(fontSize: 20),
          ),
          SizedBox(height: ConstantsTheme.padding * 0.5),
          GestureDetector(
            onTap: () async {
              await _selectDate(context);
              await _selectTime(context);
            },
            child: AbsorbPointer(
              child: TextFormField(
                validator: (value) {
                  final String trimmedValue = value!.replaceAll(' ', '');
                  if (trimmedValue.isEmpty)
                    return I18nHelper.translate(
                        context, '$_baseTranslate.requiredFieldError');
                  else
                    return null;
                },
                controller: TextEditingController(
                  text:
                      '${_dataAlta.day.toString().padLeft(2, '0')}/${_dataAlta.month.toString().padLeft(2, '0')}/${_dataAlta.year} ás ${_dataAlta.hour.toString().padLeft(2, '0')}:${_dataAlta.minute.toString().padLeft(2, '0')}',
                ),
                decoration: InputDecoration(
                  hintText: '',
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.green),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.green),
                  ),
                  contentPadding: EdgeInsets.fromLTRB(15.0, 15.0, 20.0, 15.0),
                  suffixIcon: const Icon(Icons.calendar_today),
                ),
                style: TextStyle(
                  color: AmbulanceColors.grayDark,
                  fontSize: 18,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:ambulancia_app/bloc/attendance/attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_notification/offline_first_notification_cubit.dart';
import 'package:ambulancia_app/screens/offline_first/main.dart';
import 'package:ambulancia_app/shared/api/attendance/notification_of_amounts_of_data_pending_synchronization.api.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/screen-transitions/fade.transition.dart';
import 'package:ambulancia_app/shared/services/version.service.dart';
import 'package:ambulancia_app/shared/utils/connectivity_utils.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/header/widget/user_menu.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AmbulanceHeader extends StatefulWidget implements PreferredSizeWidget {
  final bool? arrowBack;

  AmbulanceHeader({this.arrowBack});

  @override
  _AmbulanceHeaderState createState() => _AmbulanceHeaderState();

  @override
  Size get preferredSize {
    return new Size.fromHeight(kToolbarHeight);
  }
}

class _AmbulanceHeaderState extends State<AmbulanceHeader> {
  final _baseTranslate = 'home.header';
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (mounted) {
        await Locator.instance.get<ConnectivityUtils>().listenConnection(
            callback: (bool result) {
          context.read<ConnectivityCubit>().verifyConnection();
        });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 4,
      child: Container(
        color: UnimedColors.greenDark,
        child: Padding(
          padding: EdgeInsets.only(left: 16, top: 24),
          child: Row(
            children: [
              if (widget.arrowBack == true) _popArrow(),
              Container(width: 10),
              GestureDetector(
                onDoubleTap: () {
                  _alertVersion(context);
                },
                onLongPress: () {
                  Navigator.push(
                      context, FadeRoute(page: OfflineFirstTabelaList()));
                },
                child: Image.asset(
                  'assets/images/logo-unimed.png',
                  width: 100,
                ),
              ),
              Expanded(
                child: Container(),
              ),
              offlineDataSyncIcon(),
              Container(
                width: 20,
              ),
              _connectionStatusWidget(),
              Container(
                width: 20,
              ),
              UserMenu()
            ],
          ),
        ),
      ),
    );
  }

  Widget offlineDataSyncIcon() {
    return Container(
      width: 30,
      height: 30,
      child: Stack(
        children: [
          BlocConsumer<OfflineFirstCubit, OfflineFirstState>(
            listener: (context, state) {
              if (state is OfflineFinishingTheSynchronization) {
                Locator.instance
                    .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
                    .addNotificationOfAmountsOfDataPendingSynchronization();
              }
            },
            builder: (context, state) {
              return IconButton(
                padding: EdgeInsets.zero,
                iconSize: 30,
                tooltip: state is OfflineFinishingTheSynchronization
                    ? 'Sincronização'
                    : 'Sincronizando',
                onPressed: () {},
                icon: Icon(
                  state is OfflineFinishingTheSynchronization
                      ? Icons.cloud
                      : Icons.cloud_sync_outlined,
                  color: UnimedColors.greenLight,
                  size: 30,
                ),
              );
            },
          ),
          IgnorePointer(
            child: BlocBuilder<OfflineFirstNotificationCubit,
                OfflineFirstNotificationState>(
              builder: (context, state) {
                return Visibility(
                  visible: !(state is OfflineFirstNotificationFinishing &&
                      state.amountsOfOfflineData == 0),
                  child: Container(
                    width: 30,
                    height: 30,
                    alignment: Alignment.topRight,
                    margin: EdgeInsets.only(top: 5),
                    child: Container(
                      width: 18,
                      height: 18,
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: UnimedColors.green,
                          border: Border.all(color: Colors.white, width: 1)),
                      child: Padding(
                        padding: const EdgeInsets.all(0.0),
                        child: Center(
                          child: state is OfflineFirstNotificationStartingSync
                              ? CircularProgressIndicator()
                              : Text(
                                  (state is OfflineFirstNotificationInitial)
                                      ? "0"
                                      : state is OfflineFirstNotificationError
                                          ? "!"
                                          : state
                                                  is OfflineFirstNotificationFinishing
                                              ? state.amountsOfOfflineData
                                                  .toString()
                                              : "0",
                                  style: TextStyle(
                                    fontSize: 10,
                                    color:
                                        state is OfflineFirstNotificationError
                                            ? Color.fromARGB(255, 255, 230, 0)
                                            : Colors.white,
                                  ),
                                ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  _alertVersion(BuildContext context) {
    VersionInfo info = Locator.instance.get<VersionService>().info!;

    Alert.open(
      context,
      title: I18nHelper.translate(context, '$_baseTranslate.version'),
      text: '${info.version} (${info.buildNumber})',
    );
  }

  Widget _popArrow() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          final currentAttendance =
              context.read<AttendanceCubit>().currentAttendance();
          context
              .read<AttendanceListCubit>()
              .updateAttendanceLocalList(attendance: currentAttendance);
          Navigator.of(context).pop();
        },
        child: Container(
          height: 60,
          width: 60,
          child: Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _connectionStatusWidget() {
    return BlocBuilder<ConnectivityCubit, ConnectivityState>(
        builder: (context, state) {
      if (state is ConnectivityOnlineState)
        return Row(
          children: [
            Icon(
              Icons.wifi_tethering,
              color: UnimedColors.greenLight,
            ),
            Container(width: 10),
            Text('Online', style: TextStyle(color: Colors.white))
          ],
        );
      else if (state is ConnectivityOfflineState)
        return Row(
          children: [
            Icon(
              Icons.portable_wifi_off,
              color: UnimedColors.grayLight2,
            ),
            Container(width: 10),
            Text('Off-line', style: TextStyle(color: Colors.white))
          ],
        );
      else
        return Container();
    });
  }
}

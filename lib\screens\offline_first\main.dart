import 'package:ambulancia_app/screens/offline_first/widgets/details_table.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class OfflineFirstTabelaList extends StatefulWidget {
  @override
  _OfflineFirstTabelaListState createState() => _OfflineFirstTabelaListState();
}

class _OfflineFirstTabelaListState extends State<OfflineFirstTabelaList> {
  late Database _database;
  List<String> _tabelas = [];

  @override
  void initState() {
    super.initState();
    _openDatabase();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: Text('Tabelas do Banco de Dados'),
        backgroundColor: UnimedColors.greenDark,
      ),
      body: ListView.builder(
        itemCount: _tabelas.length,
        itemBuilder: (context, index) {
          return ListTile(
            title: Text(_tabelas[index]),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DetailsTable(
                    nameTable: _tabelas[index],
                    database: _database,
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Future<void> _openDatabase() async {
    String path = join(await getDatabasesPath(), 'ambulancia.db');
    _database = await openDatabase(path);
    _searchTables();
  }

  Future<void> _searchTables() async {
    final result = await _database.rawQuery(
        "SELECT name FROM sqlite_master WHERE type ='table' AND name NOT LIKE 'sqlite_%'");
    List<String> tabelas = [];
    for (Map<String, dynamic> tabela in result) {
      tabelas.add(tabela['name']);
    }
    setState(() {
      _tabelas = tabelas;
    });
  }
}

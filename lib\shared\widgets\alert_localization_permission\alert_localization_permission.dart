import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/services/geolocation.service.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/buttons/unimed_buttons.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

class AlertLocalizationPermission {
  static void grantPermission({required BuildContext context}) {
    final _baseTranslate = 'attendanceScreen.geolocatorWarning';
    Alert.open(
      context,
      text: I18nHelper.translate(context, '$_baseTranslate.text'),
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      addButtonClose: false,
      actions: [
        UnimedFlatButton(
          text: I18nHelper.translate(context, '$_baseTranslate.accepted')
              .toUpperCase(),
          onPressed: () {
            Navigator.pop(context);
            Locator.instance.get<GeolocationService>().openAppConfig();
          },
        ),
        UnimedFlatButton(
          borderColor: AmbulanceColors.redClose,
          text: 'FECHAR',
          textColor: Colors.white,
          color: AmbulanceColors.redClose,
          onPressed: () => Navigator.pop(context),
        ),
      ],
    );
  }
}

import 'package:ambulancia_app/models/sync.model.dart';
import 'package:ambulancia_app/shared/services/sync-offline.service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  SyncModel? testSyncModel;
  Map<String, dynamic>? jsonSyncModel;
  setUpAll(
    () {
      testSyncModel = SyncModel(
        customId: "1",
        jsonRequest: "0",
        header: {},
        typeRequest: SYNC_TYPE_REQUEST.GET,
        url: "http://www.google.com",
      );
      jsonSyncModel = {
        "custom_id": "1",
        "json_request": "0",
        "header": {"Authorization": "asdasd"},
        "url": "http://www.google.com",
        "type_request": SYNC_TYPE_REQUEST.GET,
        "path_file": "0",
        "file_name": "0",
        "authorization": "0",
        "json_fields": "0",
      };
    },
  );

  group(
    "isInstanceOf SyncModel model tests",
    () {
      test("Should be return instance of SyncModel", () {
        expect(testSyncModel, isInstanceOf<SyncModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of SyncModel to json", () {
      expect(testSyncModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of SyncModel from json", () {
      expect(SyncModel.fromJson(jsonSyncModel!), isInstanceOf<SyncModel>());
    });
  });

  group(
    "isInstanceOf SyncModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonSyncModel!["custom_id"], isInstanceOf<String>());
        expect(jsonSyncModel!["header"], isInstanceOf<Map>());
        expect(jsonSyncModel!["url"], isInstanceOf<String>());
        expect(jsonSyncModel!["json_request"], isInstanceOf<String>());
        expect(
            jsonSyncModel!["type_request"], isInstanceOf<SYNC_TYPE_REQUEST>());
      });
    },
  );
}

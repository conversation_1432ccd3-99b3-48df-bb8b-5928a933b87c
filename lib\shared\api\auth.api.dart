import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/models/service-update.dart';
import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/models/vehicle_model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/api/vo/version-validate.vo.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/services/version.service.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/last-synchronization-dates/last_synchronization_dates.dart';
import 'package:encrypt/encrypt.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:http/http.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthApi {
  final logger = UnimedLogger(className: 'AuthApi');
  final UnimedHttpClient httpClient;

  final attributeCredentials = 'cookie-session';
  final firstTime = "firstTime";
  final passwordCredentials = '357538782F413F4428472B4B62506553';
  String? _tokenPerfilApps;

  LoginDataModel? _loginDataModel;
  VehicleModel? get vehicleSelected => _loginDataModel!.vehicleModel;

  AuthApi(this.httpClient);

  Future<UserModel> login(LoginDataModel loginDataModel) async {
    try {
      _setCredentials(loginDataModel);
      final packageInfo = await PackageInfo.fromPlatform();

      final String deviceId = await _deviceId();

      final body = jsonEncode({
        'cpf': '${loginDataModel.userCredentials!.user}',
        'senha': '${loginDataModel.userCredentials!.password}',
        'versao': '${packageInfo.version}',
        'deviceId': '$deviceId}'
      });

      final url =
          '${const String.fromEnvironment('perfilAppsUrl')}auth/login-ambulancia';

      final response = await this.httpClient.post(Uri.parse(url),
          body: body, headers: {"Content-Type": "application/json"});

      if (response.statusCode == 200) {
        UserModel userLogged = UserModel.fromJson(jsonDecode(response.body));
        Locator.instance
            .get<RemoteLog>()
            .setUserId(loginDataModel.userCredentials!.userClean);
        FirebaseCrashlytics.instance
            .setUserIdentifier(loginDataModel.userCredentials!.userClean);
        FirebaseCrashlytics.instance.setCustomKey(
            'environment', const String.fromEnvironment('environment'));
        logger.d(
            'login User ${loginDataModel.userCredentials!.user} - response: ${response.body}');
        return userLogged;
      } else {
        final message = jsonDecode(response.body)['message'];
        logger.e(
            'login statusCode : ${response.statusCode} message: ${response.reasonPhrase}}');
        throw AuthException(message);
      }
    } on NotFoundException catch (e) {
      logger.e('login NotFoundException ${e.runtimeType} => $e');
      throw NotFoundException();
    } on ServiceTimeoutException catch (e) {
      logger.e('login ServiceTimeoutException ${e.runtimeType} => $e');
      throw ServiceTimeoutException();
    } on NoInternetException catch (e) {
      logger.e('login NoInternetException ${e.runtimeType} => $e');
      throw NoInternetException();
    } on UnimedException catch (e) {
      logger.e('login ${e.runtimeType} => $e');
      throw AuthException(e.message);
    } catch (e) {
      logger.e('login ${e.runtimeType} => $e');
      throw AuthException(MessageException.general);
    }
  }

  Future<void> logout({UserCredentials? userCredentials}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final data = prefs.getString(attributeCredentials);
    final dataPage =
        prefs.getInt(Locator.instance<AttendanceApi>().totalRecordsPersist);
    final versionInfo =
        await (Locator.instance.get<VersionService>().getInfo());
    final vehicleSelected = prefs.getString('vehicleSelected');

    final List<RequestServiceDataTime>
        listServiceUpdateSharedPreferencesDateTime = await (Locator.instance
            .get<LastSynchronizationDates>()
            .getRequestServicesDataTime());

    String userCredentialsCpf =
        userCredentials != null && userCredentials.user != null
            ? userCredentials.user!.replaceAll(RegExp(r'\D'), '')
            : '';
    final String deviceId = await _deviceId();

    final url =
        '${const String.fromEnvironment('perfilAppsUrl')}auth/logout-ambulancia?i=$deviceId&v=${versionInfo!.version}&u=${userCredentialsCpf}';

    try {
      final response = await this.httpClient.get(Uri.parse(url));

      if (response.statusCode == 200 || response.statusCode == 201) {
        final bodyDecode = jsonDecode(response.body);
        logger.i('logout sucess - statuscode ${response.statusCode}');

        logger.d('data user logout: $data');

        // final vehicleModel = _loginDataModel!.vehicleModel!;
        // prefs.setString('vehicleSelected', jsonEncode(vehicleModel.toJson()));

        // _loginDataModel = null;

        await _removeDateLogin(
          prefs: prefs,
          dataPage: dataPage,
          data: data,
          vehicleSelected: vehicleSelected,
          listServiceUpdateSharedPreferencesDateTime:
              listServiceUpdateSharedPreferencesDateTime,
        );

        return bodyDecode['mensagem'];
      } else {
        await _removeDateLogin(
          prefs: prefs,
          dataPage: dataPage,
          data: data,
          vehicleSelected: vehicleSelected,
          listServiceUpdateSharedPreferencesDateTime:
              listServiceUpdateSharedPreferencesDateTime,
        );
        logger.e('logout error - statuscode ${response.statusCode}');
      }
    } catch (e) {
      await _removeDateLogin(
        prefs: prefs,
        dataPage: dataPage,
        data: data,
        vehicleSelected: vehicleSelected,
        listServiceUpdateSharedPreferencesDateTime:
            listServiceUpdateSharedPreferencesDateTime,
      );
      LogoutException();
    }
  }

  /// Recupera as credenciais do usuário para uso na biometria
  Future<LoginDataModel?> getCredentials() async {
    if (const String.fromEnvironment('environment') == "TEST") {
      if (_loginDataModel != null) {
        return _loginDataModel;
      } else {
        return LoginDataModel(
          vehicleModel: VehicleModel(codVeiculo: '13', nomeVeiculo: 'UTI'),
          userCredentials:
              UserCredentials(user: '07043125308', password: '123456'),
        );
      }
    } else {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final dataCrypted = prefs.getString(attributeCredentials);

      if (dataCrypted != null && dataCrypted.isNotEmpty) {
        final key = Key.fromUtf8(passwordCredentials);
        final iv = IV.fromLength(16);
        final encrypter = Encrypter(AES(key));

        final encrypted = Encrypted.fromBase64(dataCrypted);
        final data = encrypter.decrypt(encrypted, iv: iv);

        final jsonData = json.decode(data);

        return LoginDataModel.fromJson(jsonData);
      } else {
        return null;
      }
    }
  }

  /// Salva as credenciais do usuário, para ser usado na biometria
  Future<void> _setCredentials(LoginDataModel loginDataModel) async {
    _loginDataModel = loginDataModel;

    if (!(const String.fromEnvironment('environment') == "TEST")) {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      final key = Key.fromUtf8(passwordCredentials);
      final iv = IV.fromLength(16);
      final encrypter = Encrypter(AES(key));

      Map<String, dynamic> jsonToSave = loginDataModel.toJson();

      final jsonString = json.encode(jsonToSave);
      final encrypted = encrypter.encrypt(jsonString, iv: iv);

      await prefs.setString(attributeCredentials, encrypted.base64);
    }
  }

  Future<String?> getTokenPerfilApps() async {
    final endpoint = 'auth/login';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';
    final body = jsonEncode({
      "user": "${const String.fromEnvironment('perfilAppsUser')}",
      "password": "${const String.fromEnvironment('perfilAppsPassword')}"
    });
    try {
      final response = await this.httpClient.post(Uri.parse(url),
          body: body, headers: {"Content-Type": "application/json"});

      if (response.statusCode == 200) {
        final responseJson = jsonDecode(response.body);
        return responseJson['token'];
      } else
        return "";
    } on UnimedException catch (ex) {
      logger.e('getTokenPerfilApps  ${ex.runtimeType}: ${ex.message}');
      throw AuthException(ex.message);
    } catch (ex) {
      logger.e('getTokenPerfilApps  ${ex.runtimeType}: $ex');
      throw AuthException(MessageException.general);
    }
  }

  Future<String?> tokenPerfilApps({String? user, String? password}) async {
    if (!_isValidTokenPerfilApps(_tokenPerfilApps)) {
      if (user == null) user = const String.fromEnvironment('perfilAppsUser');
      if (password == null)
        password = const String.fromEnvironment('perfilAppsPassword');

      try {
        final String url =
            '${const String.fromEnvironment('perfilAppsUrl')}auth/login';
        final body = jsonEncode({"user": user, "password": password});

        final headers = {"Content-Type": "application/json"};

        final response = await this
            .httpClient
            .post(Uri.parse(url), body: body, headers: headers);

        logger.d(
            'AuthApi tokenPerfilApps user ${const String.fromEnvironment('perfilAppsUrl')}');
        logger.d(
            'AuthApi tokenPerfilApps pass ${(const String.fromEnvironment('perfilAppsPassword')).length}');

        if (response.statusCode == 200) {
          logger.d('AuthApi tokenPerfilApps success logged');
          _tokenPerfilApps = (jsonDecode(response.body))['token'];
          logger.d('AuthApi tokenPerfilApps token $_tokenPerfilApps');
          return _tokenPerfilApps;
        } else {
          logger.e('AuthApi error status != 200 ${response.body}');
          throw ProfilesException(
              'AuthApi tokenPerfilApps != 200 => ${response.statusCode}');
        }
      } catch (e) {
        logger.e('AuthApi tokenPerfilApps Exception $e');
        throw ProfilesException('${e.toString()}');
      }
    } else {
      return _tokenPerfilApps;
    }
  }

  Future<String> tokenPerfilAppsNew({String? user, String? password}) async {
    String _tokenPerfilAppsNew = _tokenPerfilApps ?? "";
    if (!_isValidTokenPerfilApps(_tokenPerfilAppsNew)) {
      if (user == null) user = const String.fromEnvironment('perfilAppsUser');
      if (password == null)
        password = const String.fromEnvironment('perfilAppsPassword');

      try {
        final String url =
            '${const String.fromEnvironment('perfilAppsUrl')}auth/login';
        final body = jsonEncode({"user": user, "password": password});

        final headers = {"Content-Type": "application/json"};

        final response = await this
            .httpClient
            .post(Uri.parse(url), body: body, headers: headers);

        logger.d(
            'AuthApi tokenPerfilApps user ${const String.fromEnvironment('perfilAppsUser')}');
        logger.d(
            'AuthApi tokenPerfilApps pass ${(const String.fromEnvironment('perfilAppsPassword')).length}');

        if (response.statusCode == 200) {
          logger.d('AuthApi tokenPerfilApps success logged');
          _tokenPerfilAppsNew = (jsonDecode(response.body))['token'];
          logger.d('AuthApi tokenPerfilApps token $_tokenPerfilAppsNew');
          return _tokenPerfilAppsNew;
        } else {
          return _tokenPerfilAppsNew;
        }
      } catch (e) {
        logger.e('AuthApi tokenPerfilApps Exception $e');
        return _tokenPerfilAppsNew;
      }
    } else {
      return _tokenPerfilAppsNew;
    }
  }

  bool _isValidTokenPerfilApps(String? token) {
    if (_tokenPerfilApps == null || _tokenPerfilApps!.isEmpty) {
      return false;
    } else {
      final parts = token!.split('.');
      if (parts.length != 3) {
        throw Exception('invalid token');
      }

      final payload = _decodeBase64(parts[1]);
      final payloadMap = json.decode(payload);
      if (payloadMap is! Map<String, dynamic>) {
        throw Exception('invalid payload');
      }

      final DateTime exp =
          new DateTime.fromMillisecondsSinceEpoch(payloadMap['exp']);
      return exp.isAfter(DateTime.now());
    }
  }

  String _decodeBase64(String str) {
    String output = str.replaceAll('-', '+').replaceAll('_', '/');

    switch (output.length % 4) {
      case 0:
        break;
      case 2:
        output += '==';
        break;
      case 3:
        output += '=';
        break;
      default:
        throw Exception('Illegal base64url string!"');
    }

    return utf8.decode(base64Url.decode(output));
  }

  Future<VersionValidateResponse?> getCheckVersion(version, build) async {
    VersionValidateResponse? versionInfo;
    final token = await tokenPerfilApps(
        user: const String.fromEnvironment('perfilAppsUser'),
        password: const String.fromEnvironment('perfilAppsPassword'));
    final url =
        "${const String.fromEnvironment('perfilAppsUrl')}version/ambulancia/$version/$build";
    final headers = {'Authorization': 'Bearer $token'};
    try {
      Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);
      if (response.statusCode == 200) {
        final bodyReturn = jsonDecode(response.body);
        logger.i('getCheckVersion sucess - statusCode: ${response.statusCode}');
        versionInfo = VersionValidateResponse.fromJson(bodyReturn);
        versionInfo.isOutOfDate = false;
      } else if (response.statusCode == 500) {
        final bodyReturn = jsonDecode(response.body);
        logger.i('getCheckVersion sucess - statusCode: ${response.statusCode}');
        if (bodyReturn['lastVersion'] != null) {
          versionInfo = VersionValidateResponse.fromJson(bodyReturn);
          versionInfo.isOutOfDate = true;
        }
      } else {
        logger.e(
            'getCheckVersion error - statusCode ${response.statusCode} ${response.body}');
        throw VersionApiException(
            'AuthApi CheckVersion error status != 200 => ${response.statusCode}');
      }
    } on UnimedException catch (e) {
      throw e;
    } catch (e) {
      logger.e('AuthApi checkVersion Exception $e');
      throw UnimedException('Erro ao verificar a versão ${e.toString()}');
    }
    return versionInfo;
  }

  Future<void> _removeDateLogin({
    required SharedPreferences prefs,
    required int? dataPage,
    required String? data,
    required String? vehicleSelected,
    required List<RequestServiceDataTime>
        listServiceUpdateSharedPreferencesDateTime,
  }) async {
    _loginDataModel = null;

    if (vehicleSelected != null && vehicleSelected.isNotEmpty) {
      await prefs.remove("vehicleSelected");
    }

    if (listServiceUpdateSharedPreferencesDateTime.isNotEmpty) {
      await prefs.remove("vehicleSelected");
    }
    if (dataPage != null) {
      await prefs.remove(Locator.instance<AttendanceApi>().totalRecordsPersist);
    }

    if (data != null && data.isNotEmpty) {
      await prefs.remove(attributeCredentials);
    }
  }

  Future<String> _deviceId() async {
    String deviceId = '';
    final deviceInfo =
        await Locator.instance.get<RemoteLog>().deviceInfo?.toJson();
    if (Platform.isAndroid) {
      deviceId = deviceInfo?['androidId'] ?? '';
    }
    return deviceId;
  }
}

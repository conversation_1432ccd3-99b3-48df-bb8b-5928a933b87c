import 'package:ambulancia_app/bloc/destiny/destiny_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/destiny_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_screen.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/screen-transitions/fade.transition.dart';
import 'package:ambulancia_app/shared/utils/constants.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/unimed-select.dart';

class Destiny extends StatefulWidget {
  final AttendanceModel attendance;
  const Destiny({Key? key, required this.attendance}) : super(key: key);

  @override
  _DestinyState createState() => _DestinyState();
}

class _DestinyState extends State<Destiny> {
  TextEditingController controllerDestinies = TextEditingController();
  DestinyObject? selectedDestinyView;

  @override
  void initState() {
    super.initState();
    context.read<DestinyCubit>().listDestinies(Constants.COD_UNIMED);

    selectedDestinyView = DestinyObject(
      codUnimed: widget.attendance.codUnimed,
      codigoDestinoPaciente: widget.attendance.codigoDestinoPaciente,
      nomeDestinoPaciente: widget.attendance.descricaoDestinoPaciente,
    );
    controllerDestinies.text =
        '${widget.attendance.codigoDestinoPaciente ?? "Selecione o destino"} - ${widget.attendance.descricaoDestinoPaciente}';
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DestinyCubit, DestinyState>(
      listener: (context, state) {
        if (state is UpdatedDestiniesState) {
          Navigator.pop(context);
          Navigator.pushReplacement(context,
              FadeRoute(page: AttendanceScreen(attendance: widget.attendance)));
        }
      },
      child: Container(
        width: MediaQuery.of(context).size.width * 0.5,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Lista de Destinos',
                style: TextStyle(
                    color: AmbulanceColors.green,
                    fontSize: 25,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 30),
              Column(
                children: [
                  BlocBuilder<DestinyCubit, DestinyState>(
                    builder: (context, state) {
                      if (state is LoadingDestiniesState)
                        return SpinKitThreeBounce(color: AmbulanceColors.green);
                      else if (state is LoadedDestiniesState)
                        return _listFullDestinies(
                            state.destinyModel!.destinies);
                      else {
                        final destinyModel =
                            BlocProvider.of<DestinyCubit>(context).destinyModel;
                        if (destinyModel != null)
                          return _listFullDestinies(destinyModel.destinies);
                        else
                          return Container();
                      }
                    },
                  ),
                  SizedBox(height: 20),
                  _indicatorState(),
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AmbulanceColors.redClose,
                        ),
                        child: Text(
                            I18nHelper.translate(context, 'common.cancel'),
                            style: TextStyle(color: Colors.white)),
                        onPressed: () => Navigator.pop(context),
                      ),
                      SizedBox(width: 20),
                      BlocBuilder<DestinyCubit, DestinyState>(
                          builder: (context, state) {
                        return ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: state is ErrorDestinyState ||
                                      state is LoadingDestiniesState ||
                                      state is UpdatingDestiniesState
                                  ? AmbulanceColors.grayLight
                                  : AmbulanceColors.greenChart),
                          child: Text(
                              I18nHelper.translate(context, 'common.confirm')),
                          onPressed: selectedDestinyView != null &&
                                  (state is LoadedDestiniesState ||
                                      state is UpdatedDestiniesState)
                              ? () {
                                  context.read<DestinyCubit>().updateDestiny(
                                      attendance: widget.attendance,
                                      selectedDestinyView:
                                          selectedDestinyView!);
                                }
                              : null,
                        );
                      }),
                    ],
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  _listFullDestinies(List<DestinyObject> destinies) {
    return Row(
      children: [
        Flexible(
          child: UnimedSelect<DestinyObject>(
            title: 'Destinos',
            items: destinies
                .map((DestinyObject destiny) => UnimedSelectItemModel(
                      label:
                          "${destiny.codigoDestinoPaciente} - ${destiny.nomeDestinoPaciente}",
                      value: destiny,
                    ))
                .toList(),
            controller: controllerDestinies,
            onSelect: (DestinyObject? value) {
              setState(() {
                selectedDestinyView = value;
                controllerDestinies.text = value != null
                    ? "${value.codigoDestinoPaciente} - ${value.nomeDestinoPaciente}"
                    : "Selecione o destino";
              });
            },
            showClearButton: false,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 10, top: 20),
          child: IconButton(
            color: AmbulanceColors.green,
            iconSize: 40,
            icon: Icon(Icons.refresh),
            onPressed: () {
              context
                  .read<DestinyCubit>()
                  .listDestinies(Constants.COD_UNIMED, forceUpdate: true);
            },
          ),
        ),
      ],
    );
  }

  Widget _indicatorState() {
    return BlocBuilder<DestinyCubit, DestinyState>(builder: (context, state) {
      if (state is UpdatingDestiniesState)
        return SpinKitThreeBounce(color: AmbulanceColors.green, size: 40);
      else if (state is ErrorDestinyState)
        return Center(
            child: Text(
          state.message,
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.red),
        ));
      else
        return Container();
    });
  }
}

import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AlertBaterry {
  static void open(
    BuildContext context, {
    required String level,
    bool criticalAlert = false,
    Widget? button,
  }) async {
    return showGeneralDialog<void>(
      context: context,
      barrierColor: Colors.black.withOpacity(0.5),
      //transitionDuration: Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            backgroundColor: Colors.white,
            title: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(
                  'assets/icon/battery_icon.png',
                  height: 30,
                  width: 30,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: Text(
                    'Bateria Fraca',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AmbulanceColors.greenDark,
                      fontSize: 20,
                    ),
                  ),
                ),
              ],
            ),
            content: Container(
              width: MediaQuery.of(context).size.width * 0.6,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/images/baterry_imgs/${criticalAlert ? 'critical-low-battery.svg' : 'low-battery.svg'}',
                      width: 200,
                      height: 200,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(10),
                      child: RichText(
                        text: TextSpan(
                          text: 'Resta ',
                          style: TextStyle(
                            fontSize: 20,
                            color: AmbulanceColors.grayDark2,
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: '$level%',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            TextSpan(text: ' da carga da bateria.'),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Text(
                      criticalAlert
                          ? 'Conecte ao carregador \npara continuar os atendimentos.'
                          : 'Conecte ao carregador!',
                      style: TextStyle(
                        color: criticalAlert
                            ? AmbulanceColors.error
                            : AmbulanceColors.grayDark2,
                        fontSize: 20,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            actions: [button != null ? button : SizedBox.shrink()],
            actionsPadding: EdgeInsets.symmetric(
              vertical: 20,
            ),
            actionsAlignment: MainAxisAlignment.center,
            contentPadding: EdgeInsets.all(8),
          ),
        );
      },
    );
  }
}

import 'dart:async';

import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/geolocation-config.model.dart';
import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/services/geolocation.service.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:websocket_service/websocket.service.dart';

class WebSocketApi {
  final UnimedLogger logger = UnimedLogger(className: 'WebsocketApi');
  WebSocketService? _websocket;
  WebSocketService? get websocket {
    _websocket ??= WebSocketService(
        endpoint: const String.fromEnvironment('websocketUrl'));
    return _websocket;
  }

  UserCredentials? _credentials;
  UserCredentials? get userCredentials => _credentials;

  StreamController<String> testConnect = StreamController.broadcast();

  void connectWebsocket({
    required UserCredentials userCredentials,
    required Function(GeolocationConfigModel?) ack,
  }) async {
    _credentials = userCredentials;
    final _vehicle = Locator.instance.get<AuthApi>().vehicleSelected!;

    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final user = userCredentials.userClean;
      final path =
          '?c=$user&v=${packageInfo.version}&vc=${_vehicle.codVeiculo}';

      print('PATH SOCKET => $path');

      websocket!.connect(
        path,
        user,
        onConnect: () => getAmbulanceConfig(ack),
        events: [],
      );
    } catch (e) {
      logger.e('Error on connectWebsocket $e');
    }
  }

  void getAmbulanceConfig(Function(GeolocationConfigModel)? ack) {
    logger.d('getAmbulanceConfig ambulance:config');
    websocket!.emitWithAck('ambulance:config', 'geolocation', (data) {
      logger.d('ambulance:config response: $data');
      if (data['key'] == 'geolocation') {
        final GeolocationConfigModel geolocationModel =
            GeolocationConfigModel.fromJson(data['value']);
        ack!(geolocationModel);
      }
    });
  }

  void setAmbulancePositionToLog({
    required AttendanceModel attendance,
    String? lat,
    String? long,
  }) {
    final user = _credentials!.userClean;

    logger.d(
        '''ambulance:position called =>\nuser:$user\nnumAttendance:${attendance.numAtendimento}\nlat:$lat\nlong:$long\ncarteira:${attendance.carteiraNumero}\n''');
  }

  void setAmbulancePosition({
    required AttendanceModel attendance,
    String? lat,
    String? long,
  }) {
    final user = _credentials!.userClean;

    // logger.d(
    //       '''ambulance:position called =>\nuser:$user\nnumAttendance:${attendance.numAtendimento}\nlat:$lat\nlong:$long\ncarteira:${attendance.carteiraNumero}\n''');

    websocket!.emit(
      'ambulance:position',
      {
        'cpf': '$user',
        'atendimento': '${attendance.numAtendimento}',
        'carteira': '${attendance.carteiraNumero}',
        'lat': '$lat',
        'lng': '$long',
      },
    );
  }

  void closeConnectionTestListener() {
    try {
      websocket!.removeListener('connection:test');
    } catch (e) {
      logger.e('Error on closeConnectionTestListener $e');
    }
  }

  void emitWithLog(String event, Object data) {
    logger.d('websocketApi emit event $event data : $data');
    websocket!.emit(event, data);
  }

  void checkConnectionSocketTest() {
    logger.d('checkConnectionSocketTest:test called');
    websocket!.emitWithAck(
      'ambulance:config',
      DateTime.now().toIso8601String(),
      (data) {
        logger.d('checkConnectionSocketTest:test response: $data');
        testConnect.add(data);
      },
    );
  }

  void disconnectWebsocket() async {
    try {
      websocket!.disconnect();
    } catch (e) {
      logger.e('Error on disconnectWebsocket $e');
    }
  }

  void dispose() {
    websocket!.dispose();
    Locator.instance.get<GeolocationService>().cancel();
  }
}

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../bloc/permissions_force/permission_cubit.dart';
import '../../shared/widgets/alert/alert_confirm.dart';
import '../../shared/widgets/header/header.dart';
import '../../theme/colors.dart';

class CheckPermissionsPage extends StatefulWidget {
  @override
  _CheckPermissionsPageState createState() => _CheckPermissionsPageState();
}

class _CheckPermissionsPageState extends State<CheckPermissionsPage>
    with WidgetsBindingObserver {
  bool cameraActive = false;
  bool storageActive = false;
  bool locationActive = false;

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    final permissionCubit = context.read<PermissionCubit>();

    final PermissionStatus cameraStatus =
        await permissionCubit.checkCameraPermission();
    final PermissionStatus storageStatus =
        await permissionCubit.checkStoragePermission();
    final PermissionStatus locationStatus =
        await permissionCubit.checkLocationPermission();

    setState(() {
      cameraActive = cameraStatus == PermissionStatus.granted;
      storageActive = storageStatus == PermissionStatus.granted;
      locationActive = locationStatus == PermissionStatus.granted;
    });
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      debugPrint('Voltou para a tela');
      await _checkPermissions();
    } else {
      debugPrint('Saiu da tela $state');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    debugPrint('Deixou a tela de permissões');
    super.dispose();
  }

  bool _allPermissionsGranted() {
    return cameraActive && storageActive && locationActive;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: AmbulanceHeader(),
        body: Padding(
          padding: const EdgeInsets.only(top: 48.0, left: 15.0, right: 15),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const SizedBox(height: 30),
                      Center(
                          child: Icon(Icons.info_outline,
                              size: MediaQuery.of(context).size.width / 4,
                              color: AmbulanceColors.greenDark)),
                      const SizedBox(height: 30),
                      _descriptionText(),
                      const SizedBox(height: 30),
                      _permissionsWidget(),
                      const SizedBox(height: 100),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AmbulanceColors.green,
                              padding: EdgeInsets.symmetric(vertical: 16.0),
                            ),
                            key: Key('concluir_button'),
                            onPressed: _allPermissionsGranted()
                                ? () {
                                    Navigator.pop(context);
                                  }
                                : null,
                            child: Text(
                              'Concluir'.toUpperCase(),
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _descriptionText() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: TextStyle(fontSize: 16, color: UnimedColors.purple),
          children: [
            const TextSpan(
              text: 'Para funcionamento correto, ',
            ),
            const TextSpan(
              text: 'permita ',
            ),
            const TextSpan(
              text: 'que o app ',
            ),
            const TextSpan(
                text: 'Unimed Urgente ',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const TextSpan(
              text: 'utilize a usa ',
            ),
            TextSpan(
                text: 'câmera, localização e armazenamento interno.',
                style: const TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
      ),
    );
  }

  Widget _permissionsWidget() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: _activePermissionButton(
            text: cameraActive
                ? 'USO DA CÂMERA JÁ PERMITIDO'
                : 'PERMITIR O USO DA CÂMERA',
            icon: Icons.videocam_rounded,
            onTap: _requestCameraPermission,
            active: cameraActive,
          ),
        ),
        if (Platform.isAndroid)
          Padding(
            padding: const EdgeInsets.only(top: 30.0),
            child: _activePermissionButton(
              text: locationActive
                  ? 'USO DA LOCALIZAÇÃO GPS JÁ PERMITIDO'
                  : 'PERMITIR O USO DA LOCALIZAÇÃO GPS',
              icon: Icons.location_pin,
              onTap: _requestLocationPermission,
              active: locationActive,
            ),
          ),
        Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: _activePermissionButton(
            text: storageActive
                ? 'USO DO ARMAZENAMENTO INTERNO JÁ PERMITIDO'
                : 'PERMITIR O USO DO ARMAZENAMENTO INTERNO',
            icon: Icons.snippet_folder_rounded,
            onTap: _requestStoragePermission,
            active: storageActive,
          ),
        )
      ],
    );
  }

  Widget _activePermissionButton({
    required String text,
    required IconData icon,
    required Function onTap,
    required bool active,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SizedBox(
        height: 45,
        child: Material(
          child: InkWell(
            onTap: () async {
              await onTap();
            },
            child: Ink(
              decoration: BoxDecoration(
                color: active ? AmbulanceColors.green : Colors.white,
                border: Border.all(color: AmbulanceColors.green, width: 2),
                borderRadius: const BorderRadius.all(
                  Radius.circular(4.0),
                ),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Icon(
                        icon,
                        color: active ? Colors.white : AmbulanceColors.green,
                      ),
                    ),
                    Text(
                      text,
                      style: TextStyle(
                          color: active ? Colors.white : AmbulanceColors.green),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _openAlertNotificationGranted(String text, {String? title}) {
    AlertConfirm.open(
      context,
      title: title ?? 'Atenção',
      hasButtonClose: false,
      actions: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: AmbulanceColors.acceptColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5.0),
            ),
          ),
          child: const Text('Ok'),
          onPressed: () {
            Navigator.of(context).pop();
          },
        )
      ],
      text: text,
    );
  }

  Future<void> _requestCameraPermission() async {
    final permissionCubit = context.read<PermissionCubit>();
    final status = await permissionCubit.checkCameraPermission();

    if (status == PermissionStatus.permanentlyDenied) {
      openAppSettings();
    } else if (status != PermissionStatus.granted) {
      Permission.camera.request().then(
            (value) => {
              if (value == PermissionStatus.permanentlyDenied)
                {openAppSettings()}
              else
                {
                  setState(
                    () {
                      cameraActive = value == PermissionStatus.granted;
                    },
                  )
                }
            },
          );
    } else {
      _openAlertNotificationGranted(
        'O uso da Câmera já está ativado.',
        title: 'Câmera do dispositivo',
      );
    }
  }

  Future<void> _requestLocationPermission() async {
    final status = await Permission.location.status;
    if (status == PermissionStatus.permanentlyDenied) {
      openAppSettings();
    } else if (status != PermissionStatus.granted) {
      Permission.location.request().then((value) => {
            if (value == PermissionStatus.permanentlyDenied)
              {openAppSettings()}
            else
              {
                setState(() {
                  locationActive = value == PermissionStatus.granted;
                })
              }
          });
    } else {
      _openAlertNotificationGranted(
        'O uso do GPS já está ativado.',
        title: 'Localização GPS',
      );
    }
  }

  Future<void> _requestStoragePermission() async {
    final status = await Permission.storage.status;
    if (status == PermissionStatus.permanentlyDenied) {
      openAppSettings();
    } else if (status != PermissionStatus.granted) {
      Permission.storage.request().then((value) => {
            if (value == PermissionStatus.permanentlyDenied)
              {openAppSettings()}
            else
              {
                setState(() {
                  storageActive = value == PermissionStatus.granted;
                })
              }
          });
    } else {
      _openAlertNotificationGranted(
        'O armazenamento interno já está ativado.',
        title: 'Armazenamento Interno',
      );
    }
  }
}

import 'package:integration_test/integration_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ambulancia_app/main.dart' as app;
import '../../../shared/text/button/text_button.dart';
import '../../../shared/text/messages/messages_information.dart';
import '../../../shared/utils/login_sucess.dart';
import '../utils/signature_test.dart';
import '../utils/take_a_picture_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets(
      "Realizar primeiro atendimento sem selecionar a equipe de atendimento",
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginSucess(tester, '60866109323', '123456');

    final Finder selectItemListAttendaceAnswerButton =
        find.byIcon(Icons.power_settings_new_rounded).last;
    await tester.tap(selectItemListAttendaceAnswerButton);
    await tester.pumpAndSettle();

    final Finder buttonConfirmAtendance = find.text(textButtonConfirm);
    await tester.tap(buttonConfirmAtendance);
    await tester.pumpAndSettle();

    final Finder stepItemOne = find.byKey(const Key('item_step_0'));
    await tester.tap(stepItemOne);
    await tester.pumpAndSettle(Duration(seconds: 4));

    final Finder stepItemTwo = find.byKey(const Key('item_step_1'));
    await tester.tap(stepItemTwo);
    await tester.pumpAndSettle(Duration(seconds: 4));

    await signatureTest(tester);

    final Finder menuitemone = find.byKey(const Key('menu_item_1'));
    await tester.tap(menuitemone);
    await tester.pumpAndSettle();

    final Finder textInputFc = find.byKey(const Key('input_text_fc'));
    await tester.enterText(textInputFc, '100');
    await tester.pumpAndSettle();

    final Finder textInputFr = find.byKey(const Key('input_text_fr'));
    await tester.enterText(textInputFr, '60');
    await tester.pumpAndSettle();

    final Finder textInputPa = find.byKey(const Key('input_text_pa'));
    await tester.enterText(textInputPa, '100');
    await tester.pumpAndSettle();

    final Finder textInputSpo2 = find.byKey(const Key('input_text_spo2'));
    await tester.enterText(textInputSpo2, '100');
    await tester.pumpAndSettle();

    final Finder selectRhythms = find.byKey(const Key('rhythms_Regular'));
    await tester.tap(selectRhythms);
    await tester.pumpAndSettle();

    final Finder listCardsContent = find.byKey(const Key('list_cards_content'));
    await tester.drag(listCardsContent, const Offset(0.0, -350));
    await tester.pumpAndSettle(Duration(seconds: 4));

    for (int i = 0; i < 3; i++) {
      final Finder clinicalInputCheck =
          find.byKey(Key('clinical_inputCheck_$i'));
      await tester.tap(clinicalInputCheck);
      await tester.pumpAndSettle();
    }

    final Finder s = find.byKey(const Key('list_cards_content'));
    await tester.drag(s, const Offset(0.0, -350));
    await tester.pumpAndSettle(Duration(milliseconds: 200));

    final Finder buttonSaveForm = find.text(textButtonSave);
    await tester.tap(buttonSaveForm);
    await tester.pumpAndSettle();

    await takeAPictureTest(tester);

    final Finder messageSucessSaveFormClinicalEvaluation =
        find.text(textSucessSaveFormClinicalEvaluation);
    expect(messageSucessSaveFormClinicalEvaluation, findsOneWidget);

    final Finder buttonCloseAlert = find.text(textButtonClose).last;
    await tester.tap(buttonCloseAlert);
    await tester.pumpAndSettle(Duration(seconds: 3));

    for (int i = 2; i <= 5; i++) {
      final Finder stepItem = find.byKey(Key('item_step_$i'));
      await tester.tap(stepItem);
      await tester.pumpAndSettle(Duration(seconds: 4));
    }

    final Finder menuitemTwo = find.byKey(const Key('menu_item_2'));
    await tester.tap(menuitemTwo);
    await tester.pumpAndSettle();

    final Finder textFieldCid = find.byKey(const Key('text_field_cid'));
    await tester.enterText(textFieldCid, 'a');
    await tester.pumpAndSettle(Duration(seconds: 3));

    final itemFinder = find.byKey(ValueKey("cids_0"));
    await tester.tap(itemFinder);
    await tester.pumpAndSettle(Duration(seconds: 3));

    final Finder buttonSaveConduct = find.text(textButtonSave);
    await tester.tap(buttonSaveConduct);
    await tester.pumpAndSettle();

    await takeAPictureTest(tester);

    final Finder messageAlertConductSaveForm =
        find.text("Dados de conduta salvos com sucesso!");
    expect(messageAlertConductSaveForm, findsOneWidget);

    await tester.tap(buttonCloseAlert);
    await tester.pumpAndSettle(); 


    final Finder menuitemThree = find.byKey(const Key('menu_item_3'));
    await tester.tap(menuitemThree);
    await tester.pumpAndSettle();

    await tester.tap(find.textContaining('Selecione o material'));
    await tester.pumpAndSettle();

    await tester.tap(find.textContaining('AAS'));
    await tester.pumpAndSettle();

    final Finder textFormFieldQuantity = find.byKey(ValueKey('TextFormFieldQuantity'));
    await tester.enterText(textFormFieldQuantity, '5');
    await tester.pumpAndSettle();

    await tester.tap(find.byIcon(Icons.add));
    await tester.pumpAndSettle();

    final Finder buttonCloseServiceMaterial = find.text(textButtonClose).last;
    await tester.tap(buttonCloseServiceMaterial);
    await tester.pumpAndSettle();

    await tester.tap(buttonCloseAlert);
    await tester.pumpAndSettle();

    await tester.tap(menuitemTwo);
    await tester.pumpAndSettle();

    final Finder buttonCloseService = find.text(textButtonCloseService);
    await tester.tap(buttonCloseService);
    await tester.pumpAndSettle();

    await takeAPictureTest(tester);

    final Finder alertMessageCompletedSuccessfully =
        find.text("Atendimento finalizado com sucesso");
    expect(alertMessageCompletedSuccessfully, findsOneWidget);
  });
}

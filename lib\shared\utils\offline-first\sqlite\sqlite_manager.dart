import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/response.table.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/sync.table.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

/// Types SQLite
/// https://www.sqlite.org/datatype3.html
///
abstract class SQLiteManager {
  //Ao modificar as informações do banco também deve-se mudar no DbHeler do projeto Android
  final _databaseName = 'ambulancia.db';
  final int version = 3;
  final String tableName;
  Database? _database;

  String get databaseName => _databaseName;

  SQLiteManager({required this.tableName});

  Future<Database?> database() async {
    if (_database == null || !_database!.isOpen) {
      _database = await openDatabase(
        join(await getDatabasesPath(), _databaseName),
        singleInstance: false,
        version: version,
      );

      onCreate(_database!, version);
    }

    return _database;
  }

  Future<void> closeDatabase() async {
    if (_database != null && _database!.isOpen) {
      await _database!.close();
    } else {
      debugPrint('Database $_databaseName already closed');
    }
  }

  void onCreate(
    Database db,
    int version,
  ) {
    db.execute(CidTableSQLite.createSql);
    db.execute(SyncTableSQLite.createSql);
    db.execute(ResponseTableSQLite.createSql);
  }

  void onUpgrade(
    Database db,
    int oldVersion,
    int newVersion,
  ) {
    onCreate(db, newVersion);
  }
}

abstract class SQLiteTableObject<T> extends SQLiteManager {
  SQLiteTableObject({required String tableName})
      : assert(tableName.isNotEmpty),
        super(
          tableName: tableName,
        );

  Future<void> addOrUpdate(T record);
}

abstract class SQLiteRecordObject {
  /// Converte object para Map, para ser salvo no sqlite
  Map<String, dynamic> toMap();
}

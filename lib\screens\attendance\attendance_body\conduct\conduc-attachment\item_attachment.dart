import 'dart:convert';

import 'package:ambulancia_app/bloc/conduct/conduct_attachment_img/conduct_attachment_img_cubit.dart';
import 'package:ambulancia_app/bloc/conduct/conduct_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/image_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/widgets/popup_img.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/modal/modal_photo_attachment.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ItemAttachment extends StatefulWidget {
  final AttendanceModel attendanceModel;
  const ItemAttachment({super.key, required this.attendanceModel});

  @override
  State<ItemAttachment> createState() => _ItemAttachmentState();
}

class _ItemAttachmentState extends State<ItemAttachment> {
  final _baseTranslate = 'conductAttachment.warningDelete';

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Anexos",
          style: TextStyle(fontSize: 20),
        ),
        SizedBox(height: 5),
        BlocConsumer<ConductCubit, ConductState>(
          listener: (context, state) {
            if (state is ErrorSendPhotoAttachment) {
              Alert.open(context, title: 'Atenção', text: '${state.message}');
            }
            if (state is SuccessSendPhotoAttachment) {
              context.read<ConductCubit>().sendAttachment(
                    numAtendimento: widget.attendanceModel.numAtendimento,
                  );
            }
          },
          builder: (context, state) {
            if (state is ErrorLoadAttachmentState) {
              return Row(
                children: [
                  Text("Erro ao carregar os anexos"),
                  IconButton(
                    onPressed: () {
                      context.read<ConductCubit>().sendAttachment(
                            numAtendimento:
                                widget.attendanceModel.numAtendimento,
                          );
                    },
                    icon: Icon(Icons.refresh),
                  )
                ],
              );
            }
            if (state is LoadingAttachmentState) {
              return SpinKitThreeBounce(
                size: 50,
                color: AmbulanceColors.green,
              );
            }

            if (state is SuccessAttachmentState) {
              return state.listAttachment.isNotEmpty
                  ? GridView.builder(
                      shrinkWrap: true,
                      physics: NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                      ),
                      itemCount: state.listAttachment.length,
                      itemBuilder: (context, index) {
                        final ImageModel item = state.listAttachment[index];
                        List<GlobalKey> keys = List.generate(
                            state.listAttachment.length,
                            (index) => GlobalKey());

                        return BlocProvider(
                          create: (context) => ConductAttachmentImgCubit(),
                          child: BlocBuilder<ConductAttachmentImgCubit,
                              ConductAttachmentImgState>(
                            builder: (context, state) {
                              return Stack(
                                children: [
                                  Container(
                                    width: MediaQuery.of(context).size.width,
                                    height: MediaQuery.of(context).size.height,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(15),
                                      color: Colors.green,
                                    ),
                                    margin: EdgeInsets.all(4),
                                    child: FutureBuilder<dynamic>(
                                      key: keys[index],
                                      future: context
                                          .read<ConductAttachmentImgCubit>()
                                          .previewImg(
                                            isNewAttachment:
                                                item.isNewAttachment,
                                            imgNumber: item.imgNumber,
                                            numAtendimento: widget
                                                .attendanceModel.numAtendimento
                                                .toString(),
                                            imageBase64: item.fileBase64,
                                          ),
                                      builder: (context, snapshot) {
                                        if (snapshot.connectionState ==
                                                ConnectionState.waiting ||
                                            state
                                                is LoadingConductAttachmentImg) {
                                          return Center(
                                            child: CircularProgressIndicator(),
                                          );
                                        } else if (snapshot.hasError) {
                                          return IconButton(
                                            onPressed: () {
                                              setState(() {});
                                            },
                                            icon: Icon(Icons.refresh),
                                          );
                                        } else if (snapshot.hasData) {
                                          return GestureDetector(
                                            onTap: () {
                                              showImageDialog(
                                                  context, snapshot.data);
                                            },
                                            child: ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(15),
                                              child: Image.memory(
                                                base64Decode(snapshot.data),
                                                fit: BoxFit.cover,
                                                filterQuality:
                                                    FilterQuality.medium,
                                              ),
                                            ),
                                          );
                                        }
                                        return Container(
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(15),
                                            color: Colors.green,
                                          ),
                                          margin: EdgeInsets.all(4),
                                          child: Center(
                                            child: state
                                                    is LoadingConductAttachmentImg
                                                ? CircularProgressIndicator()
                                                : Icon(Icons.visibility),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                  Align(
                                    child: Material(
                                      borderRadius: BorderRadius.circular(20),
                                      color: Colors.red,
                                      child: IconButton(
                                        onPressed: () {
                                          _alertDeleteAttachment(
                                            context: context,
                                            attachmentsNumber: item.imgNumber,
                                            isNewAttachment:
                                                item.isNewAttachment,
                                          );
                                        },
                                        icon: Icon(
                                          Icons.close_rounded,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                    alignment: Alignment.topRight,
                                  ),
                                  Align(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(
                                        "N°" + item.imgNumber.toString(),
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    alignment: Alignment.bottomCenter,
                                  ),
                                ],
                              );
                            },
                          ),
                        );
                      },
                    )
                  : Padding(
                      padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                      child: Text("Nenhum item anexado"),
                    );
            }
            return Container();
          },
        ),
        _buttonAddAttachment(context)
      ],
    );
  }

  Widget _buttonAddAttachment(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: ElevatedButton.icon(
        onPressed: () {
          openModalPhotoAttachmentRegister(
            numAtendimento: widget.attendanceModel.numAtendimento,
            context: context,
          );
        },
        style: ButtonStyle(
          padding: WidgetStatePropertyAll(
            EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
          ),
          backgroundColor: WidgetStateProperty.all(UnimedColors.greenDark),
        ),
        label: Text("Adicionar anexos".toUpperCase()),
        icon: Icon(
          Icons.attach_file,
        ),
      ),
    );
  }

  void _alertDeleteAttachment({
    required BuildContext context,
    required int attachmentsNumber,
    required bool isNewAttachment,
  }) {
    Alert.open(
      context,
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      text: I18nHelper.translate(context, '$_baseTranslate.text') +
          " N° $attachmentsNumber",
      actions: [
        BlocConsumer<ConductCubit, ConductState>(
          listener: (context, state) {
            if (state is ErrorDeleteAttachmentState) {
              Navigator.pop(context);
              Alert.open(context, title: 'Atenção', text: '${state.message}');
              context.read<ConductCubit>().amitListAttachment();
            }
            if (state is SuccessDeleteAttachmentState) {
              Navigator.pop(context);

              Alert.open(context,
                  title: 'Atenção', text: 'Anexo excluido com sucesso');
              context.read<ConductCubit>().amitListAttachment();
            }
          },
          builder: (context, state) {
            return ElevatedButton(
              child: state is LoadingDeleteAttachmentState
                  ? Container(
                      height: 30,
                      width: 30,
                      child: CircularProgressIndicator(
                        color: UnimedColors.gray,
                      ),
                    )
                  : Text(I18nHelper.translate(context, 'SIM')),
              onPressed: state is LoadingDeleteAttachmentState
                  ? null
                  : () {
                      context.read<ConductCubit>().deleteAttachment(
                            attachmentsNumber: attachmentsNumber,
                            isNewAttachment: isNewAttachment,
                          );
                    },
            );
          },
        ),
      ],
    );
  }
}

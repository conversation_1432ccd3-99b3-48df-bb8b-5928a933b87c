import 'dart:convert';

import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:collection/collection.dart';
import 'package:http/http.dart' as http;

class TeamApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'TeamApi');
  FuncionarioModel? _motoristaSocrorrista;
  FuncionarioModel? _tecnicoEnfermagem;
  TeamApi(this.httpClient);

  Future<List<FuncionarioModel>?> getTeamVehicle(
      {codUnimed, codVeiculo}) async {
    final endpoint = 'ambulance/$codVeiculo/vehicle/team?codUnimed=$codUnimed';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);
        logger.d('getTeamVehicle retorno : $bodyRetorno');
        if (bodyRetorno.isEmpty) {
          logger.i('getTeamVehicle - List is empty.');
          throw TeamEmptyException();
        }

        final listaFuncionarioFuncao =
            bodyRetorno.first['listaFuncionarioFuncao'];
        final List<FuncionarioModel>? team = listaFuncionarioFuncao
            .map<FuncionarioModel>(
                (funcionario) => FuncionarioModel.fromJson(funcionario))
            .toList();

        _motoristaSocrorrista = team!.firstWhereOrNull(
            (element) => element.codFuncao == CodFuncoes.MOTORISTA_SOCORRISTA);

        _tecnicoEnfermagem = team
            .firstWhereOrNull((funcionario) => _isEnfermagemArea(funcionario));

        if (_motoristaSocrorrista == null && _tecnicoEnfermagem == null) {
          throw TeamNuloDriverAndTechnician();
        }

        return team;
      } else {
        logger.e('getTeamVehicle ${response.statusCode} found');
        throw TeamException('${response.body}');
      }
    } on NotFoundException catch (ex) {
      logger.e('getTeamVehicle NotFoundException ${ex.message}');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.e('getTeamVehicle NoInternetException ${ex.message}');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('getTeamVehicle ServiceTimeoutException ${ex.message}');
      throw ServiceTimeoutException();
    } on TeamEmptyException {
      throw TeamEmptyException();
    } on TeamNuloDriverAndTechnician {
      throw TeamNuloDriverAndTechnician();
    } on UnimedException catch (ex) {
      logger.e('getTeamVehicle UnimeException ${ex.message}');
      throw UnimedException(MessageException.general);
    } catch (e) {
      logger.e('getTeamVehicle Exception: ${e.toString()}');
      throw TeamException(MessageException.general);
    }
  }

  Future<List<FuncoesModel>> getAvailableTeam({required int? codUnimed}) async {
    try {
      final _token = await (Locator.instance.get<AuthApi>().tokenPerfilApps());

      final String url =
          '${const String.fromEnvironment('perfilAppsUrl')}ambulance/employee';

      final response = await this.httpClient.get(Uri.parse(url), headers: {
        'Authorization': 'Bearer $_token',
        'Content-Type': 'application/json',
      });

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);

        final attendanceTeam =
            (bodyRetorno as List).map<FuncoesModel>((funcao) {
          return FuncoesModel.fromJson(funcao);
        }).toList();
        logger.d('getAvailableTeam - response: ${response.body}');

        return attendanceTeam;
      } else {
        logger.e('getAvailableTeam ${response.statusCode} found');
        throw TeamException(MessageException.general);
      }
    } on NotFoundException catch (e) {
      logger.e('getAvailableTeam NotFoundException: ${e.toString()}');
      throw NotFoundException();
    } on NoInternetException catch (e) {
      logger.e('getAvailableTeam NoInternetException: ${e.toString()}');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('getAvailableTeam ServiceTimeoutException: ${e.toString()}');
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('getAvailableTeam ${ex.runtimeType}: $ex');
      throw TeamException(MessageException.general);
    } catch (e) {
      logger.e('getAvailableTeam Exception: ${e.toString()}');
      throw TeamException(MessageException.general);
    }
  }

  Future<bool> updateAttendanceTeam({
    String? numAtendimento,
    String? codMedico,
    String? codMedicoSubstituto,
    String? codEnfermeiro,
    String? codEnfermeiroSubstituto,
  }) async {
    final endpoint = 'ambulance/attendance/update-team';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    final body = jsonEncode({
      "numAtendimento": '$numAtendimento',
      "codMedico": '$codMedico',
      "codMedicoSubstituto": '$codMedicoSubstituto',
      "codEnfermeiro": '$codEnfermeiro',
      "codEnfermeiroSubstituto": '$codEnfermeiroSubstituto'
    });

    try {
      final String _token =
          await Locator.instance.get<AuthApi>().tokenPerfilAppsNew();
      final headers = {
        'Authorization': 'Bearer $_token',
        'Content-Type': 'application/json',
      };

      http.Response response = await this
          .httpClient
          .patch(Uri.parse(url), headers: headers, body: body);
      if (response.statusCode == 200) {
        logger.d('updateAttendanceTeam retorno : ${response.body}');
        return true;
      } else {
        logger.e('updateAttendanceTeam ${response.statusCode} found');
        throw TeamException(MessageException.general);
      }
    } on NotFoundException catch (e) {
      logger.e('updateAttendanceTeam NotFoundException: ${e.toString()}');
      throw NotFoundException();
    } on NoInternetException catch (e) {
      logger.e('updateAttendanceTeam NoInternetException: ${e.toString()}');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('updateAttendanceTeam ServiceTimeoutException: ${e.toString()}');
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('updateAttendanceTeam ${ex.runtimeType}: $ex');
      throw TeamException(MessageException.general);
    } catch (e) {
      logger.e('updateAttendanceTeam Exception: ${e.toString()}');
      throw TeamException(MessageException.general);
    }
  }

  bool _isEnfermagemArea(FuncionarioModel funcionario) =>
      funcionario.codFuncao == CodFuncoes.ENFERMEIRO ||
      funcionario.codFuncao == CodFuncoes.TECNICO_ENFERMAGEM ||
      funcionario.codFuncao == CodFuncoes.AUXILIAR_ENFERMAGEM;
}

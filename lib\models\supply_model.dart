import 'package:json_annotation/json_annotation.dart';

part 'supply_model.g.dart';

class SupplyAttendance {
  int? codigoMaterial;
  String? descricao;
  int? quantidade;

  SupplyAttendance({this.codigoMaterial, this.descricao, this.quantidade});

  SupplyAttendance.fromJson(Map<String, dynamic> json) {
    codigoMaterial = json['codigoMaterial'];
    descricao = json['descricao'];
    quantidade = json['quantidade'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['codigoMaterial'] = this.codigoMaterial;
    data['descricao'] = this.descricao;
    data['quantidade'] = this.quantidade;
    return data;
  }
}

@JsonSerializable(anyMap: true, explicitToJson: true)
class SupplyModel {
  @JsonKey(name: 'code')
  final int? code;
  @JsonKey(name: 'description')
  final String? description;
  SupplyModel({
    required this.code,
    required this.description,
  });
  factory SupplyModel.fromJson(Map json) => _$SupplyModelFromJson(json);
  Map<String, dynamic> toJson() => _$SupplyModelToJson(this);
}

import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/models/motive.model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';

part 'attendance_cancel_state.dart';

class AttendanceCancelCubit extends Cubit<AttendanceCancelState> {
  AttendanceCancelCubit() : super(AttendanceCancelInitial());

  Future cancelAttendance(MotiveModel? motive, String motiveOther,
      AttendanceModel attendance) async {
    try {
      emit(CancelingAttendanceState());

      await Locator.instance<AttendanceApi>().updateStatusMovements(
        attendance: attendance.numAtendimento,
        codStatus: AttendanceStatus.CANCELADO,
        codMotivo: motive!.codigo,
        observacaoMotivo: motiveOther,
        previousStatusCode: attendance.codStatus,
      );

      emit(CanceledAttendanceState());
    } catch (e) {
      emit(ErroCancelAttendanceState(e.toString()));
    }
  }

  resetStateCancel() => emit(AttendanceCancelInitial());
}

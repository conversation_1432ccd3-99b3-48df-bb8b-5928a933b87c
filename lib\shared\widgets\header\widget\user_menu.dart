import 'package:ambulancia_app/bloc/vehicle/vehicle_cubit.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/header/function/sync_information_alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UserMenu extends StatelessWidget {
  UserMenu({super.key});

  final _baseTranslate = 'home.header';

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          "${I18nHelper.translate(context, '$_baseTranslate.vehicle')} ${BlocProvider.of<VehicleCubit>(context).selectedVehicle.nomeVeiculo}",
          style: TextStyle(color: Colors.white, fontSize: 16),
        ),
        Container(
          width: 10,
        ),
        Stack(
          key: Key('home_button_logout'),
          children: [
            Align(
              alignment: Alignment.center,
              child: Padding(
                padding: const EdgeInsets.only(left: 5),
                child: I<PERSON><PERSON><PERSON>on(
                  icon: Icon(
                    Icons.logout,
                    color: Colors.white,
                  ),
                  tooltip: I18nHelper.translate(context, '$_baseTranslate.logout.confirm'),
                  onPressed: () {
                    SyncInformationAlert.open(
                      context,
                    );
                  },
                ),
              ),
            ),
          ],
        )
      ],
    );
  }
}

import 'dart:async';

import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/api/attendance/notification_of_amounts_of_data_pending_synchronization.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'offline_first_notification_state.dart';

class OfflineFirstNotificationCubit
    extends Cubit<OfflineFirstNotificationState> {
  final NotificationOfAmountsOfDataPendingSynchronizationApi servico = Locator
      .instance
      .get<NotificationOfAmountsOfDataPendingSynchronizationApi>();

  int amountsOfDataToBeSynchronizedawait = 0;

  OfflineFirstNotificationCubit() : super(OfflineFirstNotificationInitial()) {
    servico.stream.listen((event) {
      getNumberOfItems();
    });
  }

  Future<void> getNumberOfItems() async {
    try {
      emit(OfflineFirstNotificationStartingSync());
      amountsOfDataToBeSynchronizedawait = await Locator.instance
          .get<AttendanceApi>()
          .AmountsOfDataToBeSynchronized();
      emit(
        OfflineFirstNotificationFinishing(
            amountsOfOfflineData: amountsOfDataToBeSynchronizedawait),
      );
    } catch (e) {
      emit(OfflineFirstNotificationError());
    }
  }
}

import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FuncoesModel codFuncao: 8 descFuncao: ENFERMEIRO', () {
    test('fromJson() should correctly convert JSON to object', () {
      final json = {
        'codFuncao': 00,
        'descFuncao': 'Modelo de teste',
        'funcionarios': [
          {
            'codFuncionario': 9579,
            'nomeFuncionario': 'JOSE AILTON DE SOUZA',
            'codFuncao': 24,
            'descFuncao': 'MOTORISTA SOCORRISTA',
          },
          {
            'codFuncionario': 8326,
            'nomeFuncionario': 'FELIPE PINHEIRO BEZERRA LEMOS',
            'codFuncao': 8,
            'descFuncao': 'ENFERMEIRO',
          },
        ],
      };

      final funcoesModel = FuncoesModel.fromJson(json);

      expect(funcoesModel.codFuncao, 00);
      expect(funcoesModel.descFuncao, 'Modelo de teste');
      expect(funcoesModel.funcionarios.length, 2);
      expect(funcoesModel.funcionarios[0].codFuncionario, 9579);
      expect(
          funcoesModel.funcionarios[0].nomeFuncionario, 'JOSE AILTON DE SOUZA');
      expect(funcoesModel.funcionarios[0].codFuncao, 24);
      expect(funcoesModel.funcionarios[0].descFuncao, 'MOTORISTA SOCORRISTA');
      expect(funcoesModel.funcionarios[1].codFuncionario, 8326);
      expect(funcoesModel.funcionarios[1].nomeFuncionario,
          'FELIPE PINHEIRO BEZERRA LEMOS');
      expect(funcoesModel.funcionarios[1].codFuncao, 8);
      expect(funcoesModel.funcionarios[1].descFuncao, 'ENFERMEIRO');
    });

    test('fromJson() should correctly convert JSON to object', () {
      final json = {
        'codFuncao': 00,
        'descFuncao': 'Modelo de teste',
      };

      final funcoesModel = FuncoesModel.fromJson(json);

      expect(funcoesModel.codFuncao, 00);
      expect(funcoesModel.descFuncao, 'Modelo de teste');
      expect(funcoesModel.funcionarios.isEmpty, isTrue);
    });

    test('toJson() should correctly convert the object to JSON', () {
      final funcoesModel = FuncoesModel(
        codFuncao: 00,
        descFuncao: 'Modelo de teste',
        funcionarios: [
          FuncionarioModel(
            codFuncionario: 9579,
            nomeFuncionario: 'JOSE AILTON DE SOUZA',
            codFuncao: 24,
            descFuncao: 'MOTORISTA SOCORRISTA',
          ),
          FuncionarioModel(
            codFuncionario: 8326,
            nomeFuncionario: 'FELIPE PINHEIRO BEZERRA LEMOS',
            codFuncao: 8,
            descFuncao: 'ENFERMEIRO',
          ),
        ],
      );

      final json = funcoesModel.toJson();

      final jsonFuncionario1 = json['funcionarios'][0].toJson();
      final jsonFuncionario2 = json['funcionarios'][1].toJson();

      expect(json['codFuncao'], 00);
      expect(json['descFuncao'], 'Modelo de teste');
      expect(json['funcionarios'].length, 2);
      expect(jsonFuncionario1['codFuncionario'], 9579);
      expect(jsonFuncionario1['nomeFuncionario'], 'JOSE AILTON DE SOUZA');
      expect(jsonFuncionario1['codFuncao'], 24);
      expect(jsonFuncionario1['descFuncao'], 'MOTORISTA SOCORRISTA');
      expect(jsonFuncionario2['codFuncionario'], 8326);
      expect(
          jsonFuncionario2['nomeFuncionario'], 'FELIPE PINHEIRO BEZERRA LEMOS');
      expect(jsonFuncionario2['codFuncao'], 8);
      expect(jsonFuncionario2['descFuncao'], 'ENFERMEIRO');
    });
  });

  group('FuncoesModel codFuncao: 1 descFuncao: AUXILIAR DE ENFERMAGEM', () {
    test('fromJson() should correctly convert JSON to object', () {
      final json = {
        'codFuncao': 00,
        'descFuncao': 'Modelo de teste',
        'funcionarios': [
          {
            "codFuncionario": 3281,
            "nomeFuncionario": "ORISMAR VELOSO LOIOLA",
            "codFuncao": 24,
            "descFuncao": "MOTORISTA SOCORRISTA"
          },
          {
            "codFuncionario": 5980,
            "nomeFuncionario": "ISRAEL DE CASTRO RAMOS",
            "codFuncao": 1,
            "descFuncao": "AUXILIAR DE ENFERMAGEM"
          }
        ],
      };

      final funcoesModel = FuncoesModel.fromJson(json);

      expect(funcoesModel.codFuncao, 00);
      expect(funcoesModel.descFuncao, 'Modelo de teste');
      expect(funcoesModel.funcionarios.length, 2);
      expect(funcoesModel.funcionarios[0].codFuncionario, 3281);
      expect(funcoesModel.funcionarios[0].nomeFuncionario,
          'ORISMAR VELOSO LOIOLA');
      expect(funcoesModel.funcionarios[0].codFuncao, 24);
      expect(funcoesModel.funcionarios[0].descFuncao, 'MOTORISTA SOCORRISTA');
      expect(funcoesModel.funcionarios[1].codFuncionario, 5980);
      expect(funcoesModel.funcionarios[1].nomeFuncionario,
          'ISRAEL DE CASTRO RAMOS');
      expect(funcoesModel.funcionarios[1].codFuncao, 1);
      expect(funcoesModel.funcionarios[1].descFuncao, 'AUXILIAR DE ENFERMAGEM');
    });

    test('toJson() should correctly convert the object to JSON', () {
      final funcoesModel = FuncoesModel(
        codFuncao: 00,
        descFuncao: 'Modelo de teste',
        funcionarios: [
          FuncionarioModel(
            codFuncionario: 3281,
            nomeFuncionario: 'ORISMAR VELOSO LOIOLA',
            codFuncao: 24,
            descFuncao: 'MOTORISTA SOCORRISTA',
          ),
          FuncionarioModel(
            codFuncionario: 5980,
            nomeFuncionario: 'ISRAEL DE CASTRO RAMOS',
            codFuncao: 1,
            descFuncao: 'AUXILIAR DE ENFERMAGEM',
          ),
        ],
      );

      final json = funcoesModel.toJson();
      final jsonFuncionario1 = json['funcionarios'][0].toJson();
      final jsonFuncionario2 = json['funcionarios'][1].toJson();

      expect(json['codFuncao'], 00);
      expect(json['descFuncao'], 'Modelo de teste');
      expect(json['funcionarios'].length, 2);
      expect(jsonFuncionario1['codFuncionario'], 3281);
      expect(jsonFuncionario1['nomeFuncionario'], 'ORISMAR VELOSO LOIOLA');
      expect(jsonFuncionario1['codFuncao'], 24);
      expect(jsonFuncionario1['descFuncao'], 'MOTORISTA SOCORRISTA');
      expect(jsonFuncionario2['codFuncionario'], 5980);
      expect(jsonFuncionario2['nomeFuncionario'], 'ISRAEL DE CASTRO RAMOS');
      expect(jsonFuncionario2['codFuncao'], 1);
      expect(jsonFuncionario2['descFuncao'], 'AUXILIAR DE ENFERMAGEM');
    });
  });

  group('FuncoesModel codFuncao: 31 descFuncao: TECNICO DE ENFERMAGEM', () {
    test('fromJson() should correctly convert JSON to object', () {
      final json = {
        'codFuncao': 00,
        'descFuncao': 'Modelo de teste',
        'funcionarios': [
          {
            "codFuncionario": 3281,
            "nomeFuncionario": "ORISMAR VELOSO LOIOLA",
            "codFuncao": 24,
            "descFuncao": "MOTORISTA SOCORRISTA"
          },
          {
            "codFuncionario": 5980,
            "nomeFuncionario": "ISRAEL DE CASTRO RAMOS",
            "codFuncao": 31,
            "descFuncao": "TECNICO DE ENFERMAGEM"
          }
        ],
      };

      final funcoesModel = FuncoesModel.fromJson(json);

      expect(funcoesModel.codFuncao, 00);
      expect(funcoesModel.descFuncao, 'Modelo de teste');
      expect(funcoesModel.funcionarios.length, 2);
      expect(funcoesModel.funcionarios[0].codFuncionario, 3281);
      expect(funcoesModel.funcionarios[0].nomeFuncionario,
          'ORISMAR VELOSO LOIOLA');
      expect(funcoesModel.funcionarios[0].codFuncao, 24);
      expect(funcoesModel.funcionarios[0].descFuncao, 'MOTORISTA SOCORRISTA');
      expect(funcoesModel.funcionarios[1].codFuncionario, 5980);
      expect(funcoesModel.funcionarios[1].nomeFuncionario,
          'ISRAEL DE CASTRO RAMOS');
      expect(funcoesModel.funcionarios[1].codFuncao, 31);
      expect(funcoesModel.funcionarios[1].descFuncao, 'TECNICO DE ENFERMAGEM');
    });

    test('toJson() should correctly convert the object to JSON', () {
      final funcoesModel = FuncoesModel(
        codFuncao: 00,
        descFuncao: 'Modelo de teste',
        funcionarios: [
          FuncionarioModel(
            codFuncionario: 3281,
            nomeFuncionario: 'ORISMAR VELOSO LOIOLA',
            codFuncao: 24,
            descFuncao: 'MOTORISTA SOCORRISTA',
          ),
          FuncionarioModel(
            codFuncionario: 5980,
            nomeFuncionario: 'ISRAEL DE CASTRO RAMOS',
            codFuncao: 31,
            descFuncao: 'TECNICO DE ENFERMAGEM',
          ),
        ],
      );

      final json = funcoesModel.toJson();

      final jsonFuncionario1 = json['funcionarios'][0].toJson();
      final jsonFuncionario2 = json['funcionarios'][1].toJson();

      expect(json['codFuncao'], 00);
      expect(json['descFuncao'], 'Modelo de teste');
      expect(json['funcionarios'].length, 2);
      expect(jsonFuncionario1['codFuncionario'], 3281);
      expect(jsonFuncionario1['nomeFuncionario'], 'ORISMAR VELOSO LOIOLA');
      expect(jsonFuncionario1['codFuncao'], 24);
      expect(jsonFuncionario1['descFuncao'], 'MOTORISTA SOCORRISTA');
      expect(jsonFuncionario2['codFuncionario'], 5980);
      expect(jsonFuncionario2['nomeFuncionario'], 'ISRAEL DE CASTRO RAMOS');
      expect(jsonFuncionario2['codFuncao'], 31);
      expect(jsonFuncionario2['descFuncao'], 'TECNICO DE ENFERMAGEM');
    });
  });
}

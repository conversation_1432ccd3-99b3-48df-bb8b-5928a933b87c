import 'package:flutter/material.dart';

class CircularButton extends StatelessWidget {
  final color;
  final icon;
  final Function onPressed;
  final child;
  const CircularButton(
      {Key? key, this.color, this.icon, required this.onPressed, this.child})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed as void Function()?,
      child: Padding(
        padding: EdgeInsets.only(top: 8),
        child: ClipOval(
          child: Container(
            alignment: Alignment.center,
            padding: EdgeInsets.all(4),
            color: Colors.white,
            child: ClipOval(
              child: Container(
                  padding: EdgeInsets.all(5),
                  color: color,
                  child: child ?? Icon(icon, color: Colors.white, size: 48)),
            ),
          ),
        ),
      ),
    );
  }
}

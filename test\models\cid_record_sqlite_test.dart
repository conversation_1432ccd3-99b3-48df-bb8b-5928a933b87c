import 'package:ambulancia_app/models/cid.model.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CidRecordSQLite Model Tests', () {
    test('Should create a valid CidRecordSQLite instance', () {
      final CidRecordSQLite cidRecord = CidRecordSQLite(
        descricao: 'Test Description',
        codCid: '123',
        dvCid: 'A',
      );

      expect(cidRecord.descricao, 'Test Description');
      expect(cidRecord.codCid, '123');
      expect(cidRecord.dvCid, 'A');
    });

    test('Should convert CidRecordSQLite to Map successfully', () {
      final CidRecordSQLite cidRecord = CidRecordSQLite(
        descricao: 'Test Description',
        codCid: '123',
        dvCid: 'A',
      );

      final Map<String, dynamic> map = cidRecord.toMap();

      expect(map[CidTableSQLiteColums.COLUMN_DESCRICAO], 'Test Description');
      expect(map[CidTableSQLiteColums.COLUMN_COD_CID], '123');
      expect(map[CidTableSQLiteColums.COLUMN_DV_CID], 'A');
    });

    test('Should create CidRecordSQLite from Map successfully', () {
      final Map<String, String> map = {
        CidTableSQLiteColums.COLUMN_DESCRICAO: 'Test Description',
        CidTableSQLiteColums.COLUMN_COD_CID: '123',
        CidTableSQLiteColums.COLUMN_DV_CID: 'A',
      };

      final CidRecordSQLite cidRecord = CidRecordSQLite.fromMap(map);

      expect(cidRecord.descricao, 'Test Description');
      expect(cidRecord.codCid, '123');
      expect(cidRecord.dvCid, 'A');
    });

    test('Should convert CidRecordSQLite to JSON string successfully', () {
      final cidRecord = CidRecordSQLite(
        descricao: 'Test Description',
        codCid: '123',
        dvCid: 'A',
      );

      final jsonString = cidRecord.toString();

      expect(jsonString, isA<String>());
      expect(jsonString.contains('Test Description'), true);
      expect(jsonString.contains('123'), true);
      expect(jsonString.contains('A'), true);
    });

    test('Should create CidRecordSQLite from CidModel successfully', () {
      final CidModel cidModel = CidModel(
        codCid: '123',
        descricao: 'Test Description',
        dvCid: 'A',
      );

      final CidRecordSQLite cidRecord = CidRecordSQLite.fromCidModel(cidModel);

      expect(cidRecord.descricao, 'Test Description');
      expect(cidRecord.codCid, '123');
      expect(cidRecord.dvCid, 'A');
    });
  });
}

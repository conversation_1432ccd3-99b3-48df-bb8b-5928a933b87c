import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  FuncionarioModel? testFuncionarioModel;
  Map<String, dynamic>? jsonFuncionarioModel;
  setUpAll(
    () {
      testFuncionarioModel = FuncionarioModel(
        codFuncao: 1,
        codFuncionario: 1,
        descFuncao: "desc",
        nomeFuncionario: "nome",
      );
      jsonFuncionarioModel = {
        "codFuncionario": 1,
        "nomeFuncionario": "nome",
        "codFuncao": 1,
        "descFuncao": "desc",
      };
    },
  );

  group(
    "isInstanceOf FuncionarioModel model tests",
    () {
      test("Should be return instance of FuncionarioModel", () {
        expect(testFuncionarioModel, isInstanceOf<FuncionarioModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of FuncionarioModel to json", () {
      expect(testFuncionarioModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of FuncionarioModel from json", () {
      expect(FuncionarioModel.fromJson(jsonFuncionarioModel!),
          isInstanceOf<FuncionarioModel>());
    });
  });

  group(
    "isInstanceOf FuncionarioModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonFuncionarioModel!["codFuncionario"], isInstanceOf<int>());
        expect(
            jsonFuncionarioModel!["nomeFuncionario"], isInstanceOf<String>());
        expect(jsonFuncionarioModel!["codFuncao"], isInstanceOf<int>());
        expect(jsonFuncionarioModel!["descFuncao"], isInstanceOf<String>());
      });
    },
  );
}

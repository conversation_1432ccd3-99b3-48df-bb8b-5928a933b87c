import 'package:ambulancia_app/models/type_reclassification_attendance.dart';
import 'package:ambulancia_app/shared/api/conduct/conduct_reclassification.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'reclassification_attendance_state_cubit.dart';

class ReclassificationtAttendanceCubit
    extends Cubit<ReclassificationtAttendanceState> {
  ReclassificationtAttendanceCubit() : super(ReclassificationtAttendanceInit());

  bool _reclassificationtAttendance = false;

  bool reclassificationtAttendance() => _reclassificationtAttendance;

  List<TypeReclassificationAttendanceModel> _listTypeReclassification = [];

  List<TypeReclassificationAttendanceModel> listTypeReclassification() =>
      _listTypeReclassification;

  TypeReclassificationAttendanceModel? _typeReclassificationAttendanceSelected;

  TypeReclassificationAttendanceModel?
      typeReclassificationAttendanceSelected() =>
          _typeReclassificationAttendanceSelected;

  Future<void> getListReclassificationItens() async {
    try {
      emit(ListReclassificationLoadingState());

      final List<TypeReclassificationAttendanceModel>
          listReclassificationAttendance = await Locator.instance.get<ConductReclassificationAPI>()
              .getListReclassificationItens();
      _listTypeReclassification = listReclassificationAttendance;

      emit(
        ListReclassificationSucessState(
            listTypeReclassification: listTypeReclassification()),
      );
    } catch (e) {
      emit(ListReclassificationErrorState('$e'));
    }
  }

  void setReclassificationtAttendance({required bool value}) {
    _reclassificationtAttendance = value;
    emit(
      SetReclassificationtAttendanceState(value: _reclassificationtAttendance),
    );
  }

  void setTypeReclassificationAttendanceSelected(
      {required TypeReclassificationAttendanceModel value}) {
    _typeReclassificationAttendanceSelected = value;
    emit(SetTypeReclassificationAttendanceSelected(value: value));
  }
}

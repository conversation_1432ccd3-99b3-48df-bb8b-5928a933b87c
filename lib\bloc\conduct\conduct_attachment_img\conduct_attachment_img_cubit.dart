import 'package:ambulancia_app/models/attachment_img_model.dart';
import 'package:ambulancia_app/shared/api/conduct/conduct_attachment.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'conduct_attachment_img_state.dart';

class ConductAttachmentImgCubit extends Cubit<ConductAttachmentImgState> {
  ConductAttachmentImgCubit() : super(ConductAttachmentImgInit());

  Future<dynamic> previewImg({
    required bool isNewAttachment,
    required int imgNumber,
    required String numAtendimento,
    required String imageBase64,
  }) async {
    try {
      emit(LoadingConductAttachmentImg());

      if (isNewAttachment) {
        return imageBase64;
      }
      final _response =
          await Locator.instance.get<ConductAttachmentAPI>().getDownloadsImg(
                imgNumber: imgNumber,
                numAtendimento: numAtendimento,
              );

      return _response.arquivoBase64;
    } catch (e) {
      rethrow;
    }
  }
}

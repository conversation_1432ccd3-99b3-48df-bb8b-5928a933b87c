name: ambulancia_app
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 2.0.5+205004

environment:
  sdk: ">=2.17.0 <4.0.0"
  flutter: "3.24.3"

dependencies:
  flutter:
    sdk: flutter

  http: ^1.1.2
  get_it: ^7.2.0
  logger: ^2.0.2+1
  flutter_bloc: ^8.0.0
  equatable: ^2.0.3
  crypto: ^3.0.1
  flutter_svg: ^2.0.0+1
  flutter_spinkit: ^5.1.0
  json_annotation: ^4.3.0
  mask_text_input_formatter: ^2.0.0
  geolocator: ^9.0.2

  cupertino_icons: ^1.0.5
  flare_flutter: ^3.0.2
  sqflite: ^2.0.0+4
  shared_preferences: ^2.0.12
  encrypt: 5.0.1 #foi necessario fixar a versao pois as demais (^5.0.0, ^5.0.2 e ^5.0.3) nao estao conseguindo decodificar impedindo o login

  wakelock_plus: ^1.2.10
  flutter_signature_pad: ^3.0.0
  path_provider: ^2.0.7
  flutter_image_compress: ^2.4.0
  url_launcher: ^6.3.1
  map_launcher: ^3.5.0
  package_info_plus: ^8.2.1
  camera: ^0.11.0
  flutter_i18n: ^0.35.0
  # Firebase
  firebase_core: ^3.11.0
  firebase_remote_config: ^5.4.0
  firebase_crashlytics: ^4.3.2
  platform: ^3.1.5
  permission_handler: ^11.3.1
  vibration: ^3.1.3

  intl: ^0.19.0

  remote_log_elastic:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: remote_log_elastic
      ref: remote_log_elastic-v3.0.2

  unimed_select:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: unimed_select
      ref: unimed_select-v2.0.0

  websocket_service:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: websocket_service
      ref: websocket_service-v1.0.0

  splash_unimed:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: splash_unimed
      ref: splash_unimed-v2.0.1

  check_connection:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: check_connection
      ref: check_connection-v1.1.0

  collection: ^1.15.0
  mocktail: ^1.0.0
  battery_plus: ^6.2.1
  google_maps_flutter: ^2.10.0
  graphql: ^5.2.0-beta.11

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  json_serializable: ^6.0.1 # null-safety
  build_runner: ^2.1.5 # null-safety
  flutter_launcher_icons: ^0.12.0

dependency_overrides:
  win32: ^5.5.4

flutter_icons:
  android: true
  image_path: "assets/icon/1024x1024.png"

flutter:
  uses-material-design: true

  assets:
    - assets/icon/
    - assets/images/
    - assets/images/baterry_imgs/
    - assets/images/pin_maps/
    - assets/images/roberta/
    - assets/i18n/
    - assets/animations/
    - assets/files/

  fonts:
    - family: UnimedSans
      fonts:
        - asset: assets/fonts/UnimedSans-Regular.otf
        - asset: assets/fonts/UnimedSans-RegularItalic.otf
        - asset: assets/fonts/UnimedSans-SemiBold.otf
          weight: 700
        - asset: assets/fonts/UnimedSans-Bold.otf
          weight: 900
        - asset: assets/fonts/UnimedSans-SemiBoldItalic.otf
          style: italic
          weight: 700
    - family: UnimedSlab
      fonts:
        - asset: assets/fonts/UnimedSlab-Regular.otf
        - asset: assets/fonts/UnimedSlab-Bold.otf
          weight: 900
    - family: AmbulanceIcons
      fonts:
        - asset: assets/fonts/ambulanceApp.ttf
    - family: Icomoon
      fonts:
        - asset: assets/fonts/icomoon.ttf

import 'dart:async';

import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:check_connection/check_connection_stream.dart';
import 'package:check_connection/constanst.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class ConnectivityService {
  final logger = UnimedLogger(className: 'ConnectivityService');

  late StreamSubscription<bool> _connectivitySubscription;

  Future<void> init() async {
    bool result = false;

    await CheckConnectionStream.init(
      appName: "Ambulância",
      intervalInSec: 6,
      enviroment: (const String.fromEnvironment('remoteLogEnv')) ==
              RemoteLogEnv.PROD.name
          ? EnviromentEnum.prod
          : EnviromentEnum.dev,
    );

    result = CheckConnectionStream.lastStatus;

    return _updateConnectionStatus(result);
  }

  Future<void> listen(Function callback(bool result)) async {
    _connectivitySubscription =
        CheckConnectionStream.stream.listen((result) async {
      _updateConnectionStatus(result);
      callback(result);
    });
  }

  Future<void> _updateConnectionStatus(bool result) async {
    logger.d("updateConnectionStatus: $result");
  }

  cancelListenSubscriptionConnectivity() {
    _connectivitySubscription.cancel();
  }

  Future<bool> checkConnection() async {
    bool connectivityResult = CheckConnectionStream.lastStatus;
    logger.d(" checkConnection ${connectivityResult}");
    _updateConnectionStatus(connectivityResult);
    return connectivityResult;
  }
}

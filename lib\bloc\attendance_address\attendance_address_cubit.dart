import 'package:ambulancia_app/models/address-infos.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:equatable/equatable.dart';

part 'attendance_address_state.dart';

class AttendanceAddressCubit extends Cubit<AttendanceAddressState> {
  AttendanceAddressCubit() : super(AttendanceAddressInitial());

  List<AddressInfos> addresses = [];

  AddressInfos? get addressesAtendance => _getAddressAttendance();
  AddressInfos? get addressesDestiny => _getAddressDestiny();

  Future sendAddresses(AttendanceModel attendanceModel) async {
    try {
      emit(LoadingAddressState());
      if (attendanceModel.codigoCarteira == null) {
        emit(SentAddressState());
        return;
      }
      final token = await Locator.instance.get<AuthApi>().getTokenPerfilApps();

      int addressCount = 0;
      if (attendanceModel.enderecoAtendimento != null) addressCount++;
      if (attendanceModel.enderecoDestino != null) addressCount++;

      addresses = await Locator.instance.get<AttendanceApi>()
          .getDataAddress(attendanceModel: attendanceModel, token: token, waitGeocode: false);

      if (addresses.isNotEmpty && addresses.length == addressCount) {
        if (addresses
                .firstWhere((element) => element.address!.isDestiny)
                .address!
                .logradouro !=
            attendanceModel.enderecoDestino?.logradouro) {
          emit(SendingAddressState());
          await Locator.instance.get<AttendanceApi>()
              .updateDestinyGeolocalization(
                  attendanceModel: attendanceModel, token: token);
          emit(SentAddressState());
        }

        emit(LoadedAddressState(addresses));
      } else if (addresses.isEmpty) {
        // caso não tenha nenhum endereço no perfilapps, vamos cadastrar
        emit(SendingAddressState());
        await Locator.instance.get<AttendanceApi>()
            .sendDataAddress(attendanceModel: attendanceModel, token: token);
        emit(SentAddressState());
      } else if (addresses.length != addressCount) {
        // caso possua só um, precisamos atualizar o novo endereço cadastrado no perfilapps
        emit(SendingAddressState());
        await Locator.instance.get<AttendanceApi>()
            .updateDestinyGeolocalization(
                attendanceModel: attendanceModel, token: token);

        emit(SentAddressState());
      } else {
        emit(SendingAddressState());
        await Locator.instance.get<AttendanceApi>()
            .sendDataAddress(attendanceModel: attendanceModel, token: token);
        emit(SentAddressState());
      }
    } catch (e) {
      emit(AdressErrorState('$e'));
    }
  }

  Future getAddresses(AttendanceModel attendanceModel) async {
    try {
      emit(LoadingAddressState());
      final token = await Locator.instance.get<AuthApi>().getTokenPerfilApps();

      addresses = await Locator.instance.get<AttendanceApi>()
          .getDataAddress(attendanceModel: attendanceModel, token: token, waitGeocode: true);

      addresses.isEmpty
          ? emit(EmptyAddressState())
          : emit(LoadedAddressState(addresses));
    } catch (e) {
      emit(AdressErrorState('$e'));
    }
  }

  AddressInfos? _getAddressAttendance() {
    return addresses.firstWhereOrNull((element) => element.address!.isAttendance);
  }

  AddressInfos? _getAddressDestiny() {
    return addresses.firstWhereOrNull((element) => element.address!.isDestiny);
  }
}

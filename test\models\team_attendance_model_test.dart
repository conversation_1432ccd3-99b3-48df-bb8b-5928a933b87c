import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/team_attendance_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  TeamAttendanceModel? testTeamAttendanceModel;
  Map<String, dynamic>? jsonTeamAttendanceModel;
  setUpAll(
    () {
      testTeamAttendanceModel = TeamAttendanceModel(
        equipe: [
          FuncionarioModel(
            codFuncionario: 1,
            nomeFuncionario: "nome",
            codFuncao: 1,
            descFuncao: "desc",
          )
        ],
      );
      jsonTeamAttendanceModel = {
        "equipe": [
          {
            "codFuncionario": 1,
            "nomeFuncionario": "nome",
            "codFuncao": 1,
            "descFuncao": "desc",
          }
        ]
      };
    },
  );

  group(
    "isInstanceOf TeamAttendanceModel model tests",
    () {
      test("Should be return instance of TeamAttendanceModel", () {
        expect(testTeamAttendanceModel, isInstanceOf<TeamAttendanceModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of TeamAttendanceModel to json", () {
      expect(testTeamAttendanceModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of TeamAttendanceModel from json", () {
      expect(TeamAttendanceModel.fromJson(jsonTeamAttendanceModel!),
          isInstanceOf<TeamAttendanceModel>());
    });
  });

  group(
    "isInstanceOf TeamAttendanceModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonTeamAttendanceModel!["equipe"], isInstanceOf<List>());
        expect(jsonTeamAttendanceModel!["equipe"][0], isInstanceOf<Map>());
      });

      test("Should be return type of the json", () {
        expect(jsonTeamAttendanceModel!["equipe"] == null, false);
      });
    },
  );
}

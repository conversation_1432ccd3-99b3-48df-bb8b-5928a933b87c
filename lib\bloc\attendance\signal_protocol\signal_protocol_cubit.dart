import 'package:ambulancia_app/models/signal_protocol_model.dart';
import 'package:ambulancia_app/shared/api/attendance/signal_protocol_api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'signal_protocol_state.dart';

class SignalProtocolCubit extends Cubit<SinalProtocolState> {
  SignalProtocolCubit() : super(SinalProtocolInitial());

  List<SignalProtocolModel>? _listSignalProtocols;
  List<SignalProtocolModel> getListSinalProtocols() =>
      _listSignalProtocols ?? [];

  bool _protocolConfirmation = true;
  bool get protocolConfirmation => _protocolConfirmation;

  SignalProtocolModel? _signalProtocolSelected;

  SignalProtocolModel? signalProtocolSelected() => _signalProtocolSelected;

  Future<void> getAttendanceListSinalProtocols() async {
    try {
      emit(LoadingSinalProtocolsState());

      _listSignalProtocols =
          await Locator.instance<SinalProtocolpi>().getSinalProtocol();

      emit(
          LoadedSinalProtocolsState(listSignalProtocols: _listSignalProtocols));
    } catch (ex) {
      emit(ErrorLoadSinalProtocolsState('$ex'));
    }
  }

  void setProtocolConfirmationtAttendance({required bool value}) {
    _protocolConfirmation = value;
    emit(
      SetProtocolConfirmationtAttendanceState(
        value: _protocolConfirmation,
      ),
    );
  }

  void setSignalProtocolAttendanceSelected(
      {required SignalProtocolModel value}) {
    _signalProtocolSelected = value;
    emit(SetSignalProtocolSelected(value: value));
  }
}

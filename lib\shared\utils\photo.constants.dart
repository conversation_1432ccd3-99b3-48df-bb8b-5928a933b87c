// ignore: camel_case_types
enum photoReasonAnnexEnum {
  conduta,
  avaliacao_clinica,
  assinatura,
  encerra_atendimento,
  anexo_conduta
}

extension photoReasonAnnex on photoReasonAnnexEnum {
  String get value {
    switch (this) {
      case photoReasonAnnexEnum.conduta:
        return '1';
      case photoReasonAnnexEnum.avaliacao_clinica:
        return '2';
      case photoReasonAnnexEnum.assinatura:
        return '3';
      case photoReasonAnnexEnum.encerra_atendimento:
        return '4';
      case photoReasonAnnexEnum.anexo_conduta:
        return '5';
    }
  }
}

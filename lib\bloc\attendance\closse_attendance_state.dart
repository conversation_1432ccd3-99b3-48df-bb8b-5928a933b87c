part of 'closse_attendance_cubit.dart';

abstract class CloseAttendanceState extends Equatable {
  const CloseAttendanceState();
}

class InitialAttendanceState extends CloseAttendanceState {
  @override
  List<Object> get props => [];
}

class LoadingCloseAttendanceState extends CloseAttendanceState {
  @override
  List<Object> get props => [];
}

class CloseAttendanceSuccesState extends CloseAttendanceState {
  @override
  List<Object> get props => [];
}

class ErrorCloseAttendanceState extends CloseAttendanceState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorCloseAttendanceState(this.message);
}

class ErrorCloseAttendanceDelayReasonState extends CloseAttendanceState {
  final String message;

  @override
  List<Object> get props => [message];
  ErrorCloseAttendanceDelayReasonState(this.message);
}

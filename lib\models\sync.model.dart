import 'package:ambulancia_app/shared/services/sync-offline.service.dart';

class SyncModel {
  late String customId;
  String? jsonRequest;
  Map<String, String>? header;
  late String url;
  late SYNC_TYPE_REQUEST typeRequest;
  late String? pathFile;
  late String? fileName;
  late String? authorization;
  late String? jsonFields;

  SyncModel({
    required this.customId,
    this.jsonRequest,
    this.header,
    required this.typeRequest,
    required this.url,
    this.pathFile,
    this.fileName,
    this.authorization,
    this.jsonFields,
  });

  SyncModel.fromJson(Map<String, dynamic> json) {
    customId = json['custom_id'];
    jsonRequest = json['json_request'];
    header = json['header'];
    url = json['url'];
    typeRequest = json['type_request'];
    pathFile = json['path_file'];
    fileName = json['file_name'];
    authorization = json['authorization'];
    jsonFields = json['json_fields'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['custom_id'] = this.customId;
    data['json_request'] = this.jsonRequest;
    data['header'] = this.header;
    data['url'] = this.url;
    data['type_request'] = this.typeRequest;
    data['path_file'] = this.pathFile;
    data['file_name'] = this.fileName;
    data['authorization'] = this.authorization;
    data['json_fields'] = this.jsonFields;

    return data;
  }
}

import 'package:ambulancia_app/models/reason_pre_hospital_discharge_model.dart';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'pre_hospital_discharge_state.dart';

class PreHospitalDischargeCubit extends Cubit<PreHospitalDischargeState> {
  PreHospitalDischargeCubit() : super(PreHospitalDischargeInitial());

  List<ReasonPreHospitalDischargeModel>? _reasons;
  List<ReasonPreHospitalDischargeModel>? getReasons() => _reasons;

  Future<void> getReasonPreHospitalDischarge({required int codUnimed}) async {
    try {
      emit(LoadingPreHospitalDischargesState());

      _reasons = await Locator.instance<GraphQlApi>()
          .getReasonPreHospitalDischarge(codUnimed: codUnimed);

      emit(LoadedPreHospitalDischargeState(
          listReasonPreHospitalDischargeModel: _reasons));
    } catch (ex) {
      emit(ErrorLoadPreHospitalDischargesState('$ex'));
    }
  }
}

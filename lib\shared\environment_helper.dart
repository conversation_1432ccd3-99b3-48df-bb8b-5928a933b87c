class EnvironmentHelper {
  static const String environment =
      String.fromEnvironment('environment', defaultValue: 'DEV');

  static bool isProd() {
    return environment == "PROD";
  }

  static String getPathCsv() {
    return 'assets/files/cid_20230710${isProd() ? '_prod' : '_dev'}.csv';
  }

  static String getCidTableSharedPreferences() {
    return 'PATH_CID${isProd() ? '_PROD' : '_DEV'}';
  }
}

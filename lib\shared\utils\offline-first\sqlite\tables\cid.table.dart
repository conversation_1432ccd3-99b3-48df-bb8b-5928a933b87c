import 'dart:async';
import 'dart:convert';

import 'package:ambulancia_app/models/cid.model.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/sqlite_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';

class CidTableSQLite<T extends CidRecordSQLite> extends SQLiteTableObject<T> {
  final logger = UnimedLogger(className: 'CidTableSQLite');

  static String createSql =
      'CREATE TABLE IF NOT EXISTS tbCid${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'} ' +
          '(' +
          '${CidTableSQLiteColums.COLUMN_KEY_ID} INTEGER AUTO_INCREMET PRIMARY KEY,' +
          '${CidTableSQLiteColums.COLUMN_COD_CID} TEXT ,' +
          '${CidTableSQLiteColums.COLUMN_DESCRICAO} TEXT NOT NULL,  ' +
          '${CidTableSQLiteColums.COLUMN_DV_CID} TEXT NOT NULL'
              ')';

  CidTableSQLite()
      : super(
            tableName:
                'tbCid${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}');

  Future<CidRecordSQLite?> get(
      String codCid, String dvCid, String descricao) async {
    Database _database = await database() as Database;

    final _listResults = await _database.query(tableName,
        where:
            '${CidTableSQLiteColums.COLUMN_DESCRICAO} = ? AND ${CidTableSQLiteColums.COLUMN_COD_CID} = ? AND ${CidTableSQLiteColums.COLUMN_DV_CID} = ?',
        whereArgs: [codCid, dvCid, descricao]);

    final _list = List.generate(_listResults.length, (i) {
      return CidRecordSQLite.fromMap(_listResults[i]);
    });

    await closeDatabase();

    return _list.isEmpty ? null : _list[0];
  }

  Future<bool> addByInsert(String insert, int numberLines) async {
    try {
      Database _database = await database() as Database;

      final lines = _database.delete(this.tableName);

      debugPrint('lines deleted $lines');
      debugPrint('lines will added -> $numberLines');
      final Stopwatch stopwatch = Stopwatch()..start();

      await _database.execute(insert);

      logger.d(
          'addByInsert => Insert batch - time: ${stopwatch.elapsed.inSeconds} in Seconds to add to database');

      stopwatch.stop();

      return true;

      /// _database.query("table")
    } catch (e) {
      debugPrint('$e');
      return false;
    }
  }

  /// Format line = INSERT INTO 'tablename' table VALUES ('data1', 'data2', 'data3');
  Future<bool> addByInsertTransactions(List<String> inserts) async {
    try {
      Database _database = await database() as Database;

      // final lines = _database.delete(this.tableName);

      await _database.transaction((txn) async {
        for (String insert in inserts) {
          txn.rawInsert(insert);
        }
      });

      return true;

      /// _database.query("table")
    } catch (e) {
      debugPrint('$e');

      return false;
    }
  }

  Future<List<CidRecordSQLite>> listAll() async {
    Database _database = await database() as Database;

    final List<Map<String, dynamic>> _maps = await _database.query(tableName);

    final _list = List.generate(_maps.length, (i) {
      return CidRecordSQLite.fromMap(_maps[i]);
    });

    await closeDatabase();

    return _list;
  }

  Future<List<CidRecordSQLite>> getCidByDesc(String descricao) async {
    Database _database = await database() as Database;

    final _listResults = await _database.rawQuery(
        "SELECT codCid, descricao FROM $tableName WHERE descricao LIKE '$descricao%' OR descricao LIKE '%$descricao' OR descricao LIKE '%$descricao%' OR descricao LIKE '$descricao' OR codCid LIKE '$descricao%' OR codCid LIKE '%$descricao' OR codCid LIKE '%$descricao%' OR codCid LIKE '$descricao';");
    final _list = List.generate(_listResults.length, (i) {
      return CidRecordSQLite.fromMap(_listResults[i]);
    });

    await closeDatabase();

    return _list;
  }

  @override
  Future<void> addOrUpdate(CidRecordSQLite record) async {
    try {
      Database _database = await database() as Database;

      final _listResults = await _database.query(tableName,
          where:
              '${CidTableSQLiteColums.COLUMN_COD_CID} = ? AND ${CidTableSQLiteColums.COLUMN_DV_CID} = ? AND ${CidTableSQLiteColums.COLUMN_DESCRICAO} = ?',
          whereArgs: [record.codCid, record.dvCid]);

      final _record = record.toMap();

      if (_listResults.length <= 0) {
        await _database.transaction((txn) async {
          int id1 = await txn.rawInsert(
              'INSERT INTO $tableName(${CidTableSQLiteColums.COLUMN_COD_CID}, ${CidTableSQLiteColums.COLUMN_DESCRICAO}, ${CidTableSQLiteColums.COLUMN_DV_CID}) VALUES("${record.codCid}", "${record.descricao}", "${record.dvCid}")');
          print('inserted1: $id1');
        });
        // await _database.insert(
        //   tableName,
        //   _record,
        //   conflictAlgorithm: ConflictAlgorithm.ignore,
        // );
      } else {
        await _database.update(tableName, _record,
            where: '${CidTableSQLiteColums.COLUMN_COD_CID} = ?',
            whereArgs: [record.codCid]);
      }

      await closeDatabase();

      return;
    } catch (e) {
      print(' Error on addOrUpdate => $e');
    }
  }
}

class CidRecordSQLite extends SQLiteRecordObject {
  final String? descricao;
  final String? codCid;
  final String? dvCid;

  CidRecordSQLite(
      {required this.descricao, required this.codCid, required this.dvCid});

  Map<String, dynamic> toMap() {
    return {
      '${CidTableSQLiteColums.COLUMN_DESCRICAO}': descricao,
      '${CidTableSQLiteColums.COLUMN_COD_CID}': codCid,
      '${CidTableSQLiteColums.COLUMN_DV_CID}': dvCid,
    };
  }

  /// Converte registro sqlite para object
  static CidRecordSQLite fromMap(Map<String, dynamic> map) {
    return CidRecordSQLite(
        descricao: map[CidTableSQLiteColums.COLUMN_DESCRICAO],
        codCid: map[CidTableSQLiteColums.COLUMN_COD_CID],
        dvCid: map[CidTableSQLiteColums.COLUMN_DV_CID]);
  }

  @override
  String toString() {
    return jsonEncode(toMap());
  }

  static CidRecordSQLite fromCidModel(CidModel cid) {
    return CidRecordSQLite(
        codCid: cid.codCid, descricao: cid.descricao, dvCid: cid.dvCid);
  }
}

abstract class CidTableSQLiteColums {
  static const COLUMN_KEY_ID = 'id';
  static const COLUMN_COD_CID = 'codCid';
  static const COLUMN_DESCRICAO = 'descricao';
  static const COLUMN_DV_CID = 'dvCid';
}

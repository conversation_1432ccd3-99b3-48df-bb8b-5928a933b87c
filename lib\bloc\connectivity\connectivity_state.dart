part of 'connectivity_cubit.dart';

abstract class ConnectivityState extends Equatable {
  const ConnectivityState();

  @override
  List<Object> get props => [];
}

class ConnectivityInitial extends ConnectivityState {}

class ConnectivityOnlineState extends ConnectivityState {
  @override
  List<Object> get props => [];
}

class ConnectivityOfflineState extends ConnectivityState {
  @override
  List<Object> get props => [];
}

import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/team_vehicle_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  TeamVehicle? testTeamVehicleModel;
  Map<String, dynamic>? jsonTeamVehicleModel;
  setUpAll(
    () {
      testTeamVehicleModel = TeamVehicle(
          codUnimed: 1,
          codPlantao: "1",
          codVeiculo: "1",
          nomeVeiculo: "nome",
          listaFuncionarioFuncao: [
            FuncionarioModel(
              codFuncao: 1,
              codFuncionario: 1,
              descFuncao: "desc",
              nomeFuncionario: "nome",
            )
          ]);
      jsonTeamVehicleModel = {
        "codUnimed": 1,
        "codPlantao": "1",
        "codVeiculo": "1",
        "nomeVeiculo": "nome",
        "listaFuncionarioFuncao": [
          {
            "codFuncionario": 1,
            "nomeFuncionario": "nome",
            "codFuncao": 1,
            "descFuncao": "desc",
          }
        ]
      };
    },
  );

  group(
    "isInstanceOf TeamVehicle model tests",
    () {
      test("Should be return instance of TeamVehicle", () {
        expect(testTeamVehicleModel, isInstanceOf<TeamVehicle>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of TeamVehicle to json", () {
      expect(testTeamVehicleModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of TeamVehicle from json", () {
      expect(TeamVehicle.fromJson(jsonTeamVehicleModel!),
          isInstanceOf<TeamVehicle>());
    });
  });

  group(
    "isInstanceOf TeamVehicle json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonTeamVehicleModel!["codPlantao"], isInstanceOf<String>());
        expect(jsonTeamVehicleModel!["codUnimed"], isInstanceOf<int>());
        expect(jsonTeamVehicleModel!["codVeiculo"], isInstanceOf<String>());
        expect(jsonTeamVehicleModel!["nomeVeiculo"], isInstanceOf<String>());
        expect(jsonTeamVehicleModel!["listaFuncionarioFuncao"],
            isInstanceOf<List<dynamic>>());
      });
    },
  );
}

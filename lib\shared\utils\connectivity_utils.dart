import 'package:ambulancia_app/shared/services/connectivity.service.dart';
import 'package:flutter/cupertino.dart';

class ConnectivityUtils {
  final ConnectivityService connectivityService;

  ConnectivityUtils({required this.connectivityService});

  Future<void> listenConnection({required Function callback}) async{
    debugPrint('--------- LISTEN UTIL ---------- ');
   await  connectivityService.listen((result) => callback(result));
  }

  void cancelConnection() {
    connectivityService.cancelListenSubscriptionConnectivity();
  }
}

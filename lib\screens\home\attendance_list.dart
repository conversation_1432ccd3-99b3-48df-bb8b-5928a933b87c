import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/last-synchronization-dates/last_synchronization_dates_cubit.dart';
import 'package:ambulancia_app/bloc/vehicle/vehicle_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/screens/home/<USER>/main.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/utils/constants.dart';
import 'package:ambulancia_app/shared/widgets/offline_data_sync_checker_pupup.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class AttendaceList extends StatefulWidget {
  @override
  _AttendaceListState createState() => _AttendaceListState();
}

class _AttendaceListState extends State<AttendaceList>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    _getAttendancesPage(1);

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint("===== RESUMED ");
        _getAttendancesPage(context.read<AttendanceListCubit>().currentPage);
        break;
      case AppLifecycleState.inactive:
        debugPrint("===== inactive ");
        break;
      case AppLifecycleState.paused:
        debugPrint("===== paused ");
        break;
      case AppLifecycleState.detached:
        debugPrint("===== detached ");
        break;
      case AppLifecycleState.hidden:
        debugPrint("===== hidden ");
        break;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  final String _baseTranslate = 'home.main.attendaceList';

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(15.0),
          child: BlocConsumer<AttendanceListCubit, AttendanceListState>(
            listener: (context, state) {
              if (state is LoadedAttendancesListState ||
                  state is NoDataListState ||
                  state is ErrorAttendancesListState) {
                context
                    .read<LastSynchronizationDatesCubit>()
                    .getLastSynchronizationDate(
                      page: context.read<AttendanceListCubit>().currentPage,
                    );
                _checkOfflineDataSync(context);
              }
            },
            builder: (context, state) {
              if (state is LoadingAttendancesListState) {
                return _loadingList();
              } else if (state is ErrorAttendancesListState) {
                return _errorWidget(state.message);
              } else if (state is LoadedAttendancesListState) {
                _getCheckPagination();
                return _attendanceList(state.attendances!);
              } else if (state is LoadedAttendancesListState) {
                final attendances =
                    BlocProvider.of<AttendanceListCubit>(context).attendances;
                _getCheckPagination();
                return _attendanceList(attendances);
              } else if (state is NoDataListState) {
                return _errorWidget(
                  I18nHelper.translate(
                    context,
                    '$_baseTranslate.doesNotHaveService',
                  ),
                );
              } else
                return Container();
            },
          ),
        ),
        _footStepPagination(),
      ],
    );
  }

  Widget _footStepPagination() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            icon: Icon(Icons.chevron_left_outlined),
            onPressed: _pageDown,
            color: AmbulanceColors.green,
            iconSize: 50,
          ),
          BlocBuilder<AttendanceListCubit, AttendanceListState>(
              builder: (context, state) {
            return Text(
              '${context.read<AttendanceListCubit>().currentPage}',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AmbulanceColors.green,
                fontSize: 22,
              ),
            );
          }),
          IconButton(
            icon: Icon(Icons.chevron_right_outlined),
            onPressed: _pageUp,
            color: AmbulanceColors.green,
            iconSize: 50,
          )
        ],
      ),
    );
  }

  Future<void> _pageUp() async {
    final currentPage =
        BlocProvider.of<AttendanceListCubit>(context).currentPage;
    final numberOfpages =
        BlocProvider.of<AttendanceListCubit>(context).numberOfpages;
    if (BlocProvider.of<AttendanceListCubit>(context).totalRecords <= 10)
      return;
    else {
      if (currentPage < numberOfpages)
        await _getAttendancesPage(currentPage + 1);
    }
  }

  Future<void> _pageDown() async {
    final currentPage =
        BlocProvider.of<AttendanceListCubit>(context).currentPage;
    if (currentPage <= 1) return;
    await _getAttendancesPage(currentPage - 1);
  }

  Future<void> _getAttendancesPage(mCurrentPage) async {
    await context.read<AttendanceListCubit>().getAttendances(
        codUnimed: Constants.COD_UNIMED,
        codVeiculo:
            BlocProvider.of<VehicleCubit>(context).selectedVehicle.codVeiculo,
        paginaAtual: mCurrentPage,
        quantidadeRegistros: Constants.NUM_ATTENDANCE_PAGE);
  }

  Future<void> _getCheckPagination() async {
    await context.read<AttendanceListCubit>().checkPagination();
  }

  Widget _loadingList() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SpinKitThreeBounce(
          color: AmbulanceColors.green,
        ),
      ],
    );
  }

  Widget _errorWidget(String message) {
    return Align(
      alignment: Alignment.center,
      child: RefreshIndicator(
        triggerMode: RefreshIndicatorTriggerMode.onEdge,
        onRefresh: () async {
          _getAttendancesPage(
              BlocProvider.of<AttendanceListCubit>(context).currentPage);
        },
        child: ListView(
          padding: EdgeInsets.only(top: 30),
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(message),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _attendanceList(List<AttendanceModel> attendances) {
    return RefreshIndicator(
      onRefresh: () async {
        _getAttendancesPage(
            BlocProvider.of<AttendanceListCubit>(context).currentPage);
      },
      child: Padding(
        padding: EdgeInsets.only(bottom: ConstantsTheme.doublePadding * 2.2),
        child: ListView.builder(
          key: Key('attendance_list'),
          physics: const AlwaysScrollableScrollPhysics(),
          shrinkWrap: true,
          primary: false,
          itemCount: attendances.length,
          itemBuilder: (context, index) => AttendanceItem(
            attendance: attendances[index],
          ),
        ),
      ),
    );
  }

  void _checkOfflineDataSync(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        final checker = OfflineDataSyncCheckerPopUp(
          context: context,
          baseTranslate: 'home.main.attendaceList',
        );
        checker.checkOfflineDataSync();
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ambulancia_app/main.dart' as app;

import '../../../shared/utils/initial_team_for_animation.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets("Realizar login com sucesso", (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await initialTeamForAnimation(tester);

    String selectVehicle = "UTI 15 POQ1G22";

    //Selecionar um veiculo
    final Finder dropdownVehicle = find.byKey(const Key('login_vehicle')).last;
    await tester.tap(dropdownVehicle);
    await tester.pumpAndSettle();

    final selectDropdownItem = find.text(selectVehicle).last;
    await tester.tap(selectDropdownItem);
    await tester.pumpAndSettle();

    // Digitar usuário com cpf invalido
    final Finder textFieldUsername = find.byKey(const Key('login_username'));
    await tester.enterText(textFieldUsername, '60866109323');
    await tester.pumpAndSettle();

    //Digitar senha de acesso
    final Finder textFieldPassword = find.byKey(const Key('login_password'));
    await tester.enterText(textFieldPassword, '123456');
    await tester.pumpAndSettle();

    //Clicar no botão de login
    final Finder elevatedButtonLogin = find.byKey(Key("login_button"));
    await tester.tap(elevatedButtonLogin);
    await tester.pumpAndSettle();

    // Widget de texto do veiculo selecionado
    final Finder textVehicleSelected = find.text("Veículo $selectVehicle");

    expect(textVehicleSelected, findsOneWidget);
  });
}

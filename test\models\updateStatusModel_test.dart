import 'package:ambulancia_app/models/updateStatusModel.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  UpdateStatusModel? testUpdateStatusModel;
  Map<String, dynamic>? jsonUpdateStatusModel;
  setUpAll(
    () {
      testUpdateStatusModel = UpdateStatusModel(
          numAtendimento: "1",
          codStatus: 0,
          codMotivoNaoAtendimento: "1",
          observacaoMotivoNaoAtendimento: "obs");
      jsonUpdateStatusModel = {
        "numAtendimento": "1",
        "codStatus": 0,
        "codMotivoNaoAtendimento": "1",
        "observacaoMotivoNaoAtendimento": "obs"
      };
    },
  );

  group(
    "isInstanceOf UpdateStatusModel model tests",
    () {
      test("Should be return instance of UpdateStatusModel", () {
        expect(testUpdateStatusModel, isInstanceOf<UpdateStatusModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of UpdateStatusModel to json", () {
      expect(testUpdateStatusModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of UpdateStatusModel from json", () {
      expect(UpdateStatusModel.fromJson(jsonUpdateStatusModel!),
          isInstanceOf<UpdateStatusModel>());
    });
  });

  group(
    "isInstanceOf UpdateStatusModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(
            jsonUpdateStatusModel!["numAtendimento"], isInstanceOf<String>());
        expect(jsonUpdateStatusModel!["codStatus"], isInstanceOf<int>());
        expect(jsonUpdateStatusModel!["codMotivoNaoAtendimento"],
            isInstanceOf<String>());
        expect(jsonUpdateStatusModel!["observacaoMotivoNaoAtendimento"],
            isInstanceOf<String>());
      });
    },
  );
}

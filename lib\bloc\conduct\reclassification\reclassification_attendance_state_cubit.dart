part of 'reclassification_attendance_bloc_cubit.dart';

abstract class ReclassificationtAttendanceState extends Equatable {
  const ReclassificationtAttendanceState();

  @override
  List<Object?> get props => [];
}

class ReclassificationtAttendanceInit
    extends ReclassificationtAttendanceState {}

class ListReclassificationLoadingState
    extends ReclassificationtAttendanceState {}

class ListReclassificationSucessState extends ReclassificationtAttendanceState {
  final List<TypeReclassificationAttendanceModel> listTypeReclassification;

  @override
  List<Object> get props => [listTypeReclassification];

  ListReclassificationSucessState({required this.listTypeReclassification});
}

class ListReclassificationErrorState extends ReclassificationtAttendanceState {
  final String message;
  @override
  List<Object> get props => [message];
  ListReclassificationErrorState(this.message);
}

class SetReclassificationtAttendanceState
    extends ReclassificationtAttendanceState {
  final bool value;
  @override
  List<Object> get props => [value];
  SetReclassificationtAttendanceState({required this.value});
}

class SetTypeReclassificationAttendanceSelected
    extends ReclassificationtAttendanceState {
  final TypeReclassificationAttendanceModel value;
  @override
  List<Object> get props => [value];
  SetTypeReclassificationAttendanceSelected({required this.value});
}

import 'dart:convert';

import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/vehicle/vehicle_cubit.dart';
import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/models/vehicle_model.dart';
import 'package:ambulancia_app/screens/home/<USER>';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/screen-transitions/fade.transition.dart';
import 'package:ambulancia_app/shared/utils/constants.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/validators.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginForm extends StatefulWidget {
  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final logger = UnimedLogger(className: 'LoginForm');
  TextEditingController _userController = TextEditingController();
  final loginFormatter = MaskTextInputFormatter(
      mask: '###.###.###-##', filter: {"#": RegExp(r'[0-9]')});
  final _baseTranslate = 'login';

  TextEditingController _passwordController = TextEditingController();
  bool _hidePassword = true;
  bool _isLocalAuth = false;
  final FocusNode _userFocus = FocusNode();
  final FocusNode _passwordFocus = FocusNode();
  final _formKey = GlobalKey<FormState>();
  VehicleModel? selectedVehicle;
  VehicleModel? _vehicleCache;
  @override
  void initState() {
    context.read<VehicleCubit>().loadVehicles(Constants.COD_UNIMED);
    verifyCachedLogin();
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _getVehicleCache();
    });
  }

  @override
  void dispose() {
    _userController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  _getVehicleCache() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if ((prefs.getString('vehicleSelected')) != null) {
      final model = VehicleModel.fromJson(
          jsonDecode(prefs.getString('vehicleSelected')!));
      setState(() {
        _vehicleCache = model;
      });
    }

    return _vehicleCache;
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(height: 20),
          Text(
            I18nHelper.translate(context, "$_baseTranslate.vehicle"),
            style: TextStyle(color: Colors.black54),
          ),
          BlocBuilder<VehicleCubit, VehicleState>(
            builder: (context, state) {
              if (state is LoadedVehicleState) {
                if (_vehicleCache == null) {
                  selectedVehicle ??= state.vehicles!.first;
                } else {
                  selectedVehicle ??= state.vehicles!.firstWhere((element) =>
                      element.nomeVeiculo == _vehicleCache!.nomeVeiculo);
                }
                state.vehicles!
                    .sort((a, b) => a.codVeiculo!.compareTo(b.codVeiculo!));
                state.vehicles!.sort((a, b) =>
                    a.codVeiculo!.length.compareTo(b.codVeiculo!.length));
                return DropdownButtonFormField<VehicleModel>(
                    key: Key('login_vehicle'),
                    value: selectedVehicle,
                    decoration: InputDecoration(isDense: true),
                    isExpanded: true,
                    hint: Text(I18nHelper.translate(
                        context, '$_baseTranslate.select')),
                    icon: Icon(Icons.arrow_drop_down),
                    iconSize: 24,
                    style: TextStyle(color: AmbulanceColors.grayDark),
                    isDense: true,
                    iconEnabledColor: AmbulanceColors.greenDark,
                    onChanged: (value) async {
                      logger.i('veículo ${value?.nomeVeiculo} selecionado');
                      selectedVehicle = value;
                    },
                    items: state.vehicles!
                        .map<DropdownMenuItem<VehicleModel>>((value) {
                      print(value.codVeiculo);

                      return DropdownMenuItem<VehicleModel>(
                        value: value,
                        child: Text(value.nomeVeiculo!),
                      );
                    }).toList());
              } else if (state is LoadingVehicleState) {
                return DropdownButtonFormField(
                  key: Key('login_vehicle'),
                  decoration: InputDecoration(
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: UnimedColors.grayLight2),
                    ),
                  ),
                  hint: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0, right: 8.0),
                        child: SizedBox(
                          height: 20.0,
                          width: 20.0,
                          child: CircularProgressIndicator(),
                        ),
                      ),
                      Text(
                        I18nHelper.translate(
                            context, '$_baseTranslate.loadingVehicles'),
                      ),
                    ],
                  ),
                  style: TextStyle(color: UnimedColors.green),
                  items: <DropdownMenuItem>[],
                  onChanged: (dynamic value) {},
                );
              } else if (state is ErrorVehicleState) {
                return Row(
                  children: <Widget>[
                    Flexible(
                        child: Text('${state.message}',
                            style: TextStyle(color: Colors.red))),
                    IconButton(
                      icon: Icon(Icons.refresh, color: unimedGreen),
                      onPressed: () => {
                        context
                            .read<VehicleCubit>()
                            .loadVehicles(Constants.COD_UNIMED)
                      },
                    )
                  ],
                );
              } else
                return Container();
            },
          ),
          Container(
            height: 20,
          ),
          Text(
            I18nHelper.translate(context, '$_baseTranslate.user'),
            style: TextStyle(color: Colors.black54),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: TextFormField(
              key: Key('login_username'),
              textAlign: TextAlign.left,
              textAlignVertical: TextAlignVertical.top,
              keyboardType: TextInputType.number,
              inputFormatters: [loginFormatter],
              controller: _userController,
              focusNode: _userFocus,
              onChanged: (value) {
                final cpf =
                    value.replaceAll(".", "").replaceAll("-", "").trim();

                if (cpf.length == 11) {
                  if (TextFieldValidators.cpf(value) == null) {
                    FocusScope.of(context).requestFocus(_passwordFocus);
                  }
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty)
                  return I18nHelper.translate(
                      context, '$_baseTranslate.validation.emptyUser');
                else
                  return null;
              },
              textInputAction: TextInputAction.next,
              onFieldSubmitted: (term) {
                FocusScope.of(context).requestFocus(_passwordFocus);
              },
              decoration: InputDecoration(
                contentPadding: EdgeInsets.fromLTRB(0, 15.0, 20.0, 15.0),
              ),
              style: TextStyle(color: AmbulanceColors.grayDark),
            ),
          ),
          Text(
            I18nHelper.translate(context, '$_baseTranslate.password'),
            style: TextStyle(color: Colors.black54),
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: TextFormField(
              key: Key('login_password'),
              textAlign: TextAlign.left,
              textAlignVertical: TextAlignVertical.top,
              controller: _passwordController,
              obscureText: _hidePassword,
              focusNode: _passwordFocus,
              textInputAction: TextInputAction.done,
              onFieldSubmitted: (term) {
                _pressLogin();
              },
              validator: (value) {
                if (!_isLocalAuth) {
                  if (value == null || value.isEmpty)
                    return I18nHelper.translate(
                        context, '$_baseTranslate.validation.empty');
                }

                return null;
              },
              decoration: InputDecoration(
                contentPadding: EdgeInsets.fromLTRB(0, 15.0, 20.0, 15.0),
                suffixIcon: IconButton(
                  icon: _hidePassword
                      ? Icon(
                          Icons.visibility_off,
                          color: AmbulanceColors.grayDark,
                        )
                      : Icon(
                          Icons.remove_red_eye,
                          color: AmbulanceColors.greenDark,
                        ),
                  onPressed: () {
                    setState(() => _hidePassword = !_hidePassword);
                  },
                ),
              ),
              style: TextStyle(color: AmbulanceColors.grayDark),
            ),
          ),
          _btnConfirmar(),
        ],
      ),
    );
  }

  Widget _btnConfirmar() {
    return BlocBuilder<AuthCubit, AuthState>(
      buildWhen: (previousState, state) {
        if (previousState is LoadingAuthState && state is ErrorAuthState) {
          showDialog(
            context: context,
            builder: (context) => AmbulanciaAlertDialog(
              textWidget: Text(
                state.message,
                textAlign: TextAlign.center,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          );
        } else if (previousState is LoadingAuthState &&
            state is LoadedAuthState) {
          logger.i('logou com o veículo ${selectedVehicle?.nomeVeiculo}');
          Navigator.pushReplacement(context, FadeRoute(page: HomeScreen()));
        }
        return true;
      },
      builder: (context, state) {
        if (state is LoadingAuthState) {
          return SpinKitThreeBounce(
            color: AmbulanceColors.green,
            size: 20,
          );
        } else {
          return Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AmbulanceColors.green,
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                  ),
                  key: Key('login_button'),
                  onPressed: _pressLogin,
                  child: Text(
                    I18nHelper.translate(context, '$_baseTranslate.signin'),
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  void _pressLogin() async {
    if (selectedVehicle == null) return;

    BlocProvider.of<VehicleCubit>(context).selectedVehicle = selectedVehicle!;
    FocusScope.of(context).requestFocus(new FocusNode());

    if (_formKey.currentState!.validate()) {
      await context.read<AuthCubit>().autenticate(
            LoginDataModel(
              vehicleModel: selectedVehicle,
              userCredentials: UserCredentials(
                  user: _userController.text,
                  password: _passwordController.text),
            ),
          );

      _passwordController.clear();
      setState(() {
        _isLocalAuth = false;
      });
    }
  }

  void verifyCachedLogin() async {
    final LoginDataModel? loginDataModel =
        await Locator.instance.get<AuthApi>().getCredentials();
    if (loginDataModel == null) return;

    selectedVehicle = loginDataModel.vehicleModel;

    BlocProvider.of<VehicleCubit>(context).selectedVehicle =
        loginDataModel.vehicleModel!;

    await context.read<AuthCubit>().autenticate(loginDataModel);
  }
}

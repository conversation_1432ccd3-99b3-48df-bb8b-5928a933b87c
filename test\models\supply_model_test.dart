import 'package:ambulancia_app/models/supply_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SupplyModel Tests', () {
    test('should create a SupplyModel instance', () {
      final supply = SupplyModel(code: 1, description: 'AAB');
      expect(supply.code, 1);
      expect(supply.description, 'AAB');
    });

    test('should convert SupplyModel to JSON', () {
      final SupplyModel supply = SupplyModel(code: 1, description: 'AAB');
      final Map<String, dynamic> json = supply.toJson();
      expect(json['code'], 1);
      expect(json['description'], 'AAB');
    });

    test('should create SupplyModel from JSON', () {
      final Map<String, dynamic> json = {'code': 1, 'description': 'AAB'};
      final SupplyModel supply = SupplyModel.fromJson(json);
      expect(supply.code, 1);
      expect(supply.description, 'AAB');
    });

    test('should handle null values in SupplyModel', () {
      final SupplyModel supply = SupplyModel(code: null, description: null);
      expect(supply.code, null);
      expect(supply.description, null);
    });

    test('should handle empty JSON in SupplyModel.fromJson', () {
      final Map<dynamic, dynamic> json = {};
      final SupplyModel supply = SupplyModel.fromJson(json);
      expect(supply.code, null);
      expect(supply.description, null);
    });
  });
}

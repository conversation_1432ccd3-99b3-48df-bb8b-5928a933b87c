import 'dart:convert';

import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:http/http.dart';

class VersionApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'VersionApi');
  VersionApi(this.httpClient);

  Future<Map<String, dynamic>?> getCheckVersion(version, build) async {
    Map<String, dynamic>? versionInfo;
    final url =
        "${const String.fromEnvironment('perfilAppsUrl')}/version/ambulancia/$version/$build";
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb()
    };
    try {
      Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);
      if (response.statusCode == 200) {
        final bodyReturn = jsonDecode(response.body);
        logger.i('getCheckVersion sucess - statusCode: ${response.statusCode}');
        print('======== bodyReturn $bodyReturn');
        versionInfo = {'message': bodyReturn['message'], 'isUpdated': true};
        // return versionInfo;
      } else if (response.statusCode == 500) {
        final bodyReturn = jsonDecode(response.body);
        logger.i('getCheckVersion sucess - statusCode: ${response.statusCode}');
        if (bodyReturn['lastVersion'] != null)
          versionInfo = {
            'message': bodyReturn['message'],
            'isUpdated': false,
            'lastVersion': bodyReturn['lastVersion'],
            'version': bodyReturn['version'],
            'force': bodyReturn['force']
          };
      } else {
        logger.e(
            'getCheckVersion error - statusCode ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      print('======= $e =======');
    }
    return versionInfo;
  }
}

import 'package:ambulancia_app/main.dart' as app;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import '../../shared/utils/initial_team_for_animation.dart';
import '../../shared/utils/login_sucess.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets("Realizar logout com sucesso", (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await initialTeamForAnimation(tester);

    await loginSucess(tester, '60866109323', '123456');

    // Verifica se está na tela principal após o login
    expect(find.byKey(Key('attendance_list')), findsOneWidget);

    // Encontra e clica no botão de logout
    final Finder logoutButton = find.byIcon(Icons.logout);
    await tester.tap(logoutButton);
    await tester.pumpAndSettle();

    // Clica no botão "Sair mesmo assim" no diálogo de confirmação
    final Finder confirmLogoutButton = find.text('SAIR');
    await tester.tap(confirmLogoutButton);
    await tester.pumpAndSettle();

    // Verifica se voltou para a tela de login
    expect(find.byKey(Key('login_page')), findsOneWidget);
    
    // Verifica se os campos de login estão visíveis
    expect(find.byKey(Key('login_username')), findsOneWidget);
    expect(find.byKey(Key('login_password')), findsOneWidget);
    expect(find.byKey(Key('login_button')), findsOneWidget);
  });
}

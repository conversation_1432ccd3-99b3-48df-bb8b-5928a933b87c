import 'dart:async';
import 'dart:io';

import 'package:ambulancia_app/bloc/alert_baterry/cubit/baterry_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_movemets/motive_attendance/motive_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_cubit.dart';
import 'package:ambulancia_app/bloc/permissions_force/permission_cubit.dart';
import 'package:ambulancia_app/bloc/permissions_force/permission_cubit_state.dart';
import 'package:ambulancia_app/bloc/update-version/update_version_cubit.dart';
import 'package:ambulancia_app/screens/home/<USER>';
import 'package:ambulancia_app/screens/login/main.dart';
import 'package:ambulancia_app/screens/permissions_screen/checkpermission_screen.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/screen-transitions/fade.transition.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/alert_baterry/alert_baterry.dart';
import 'package:ambulancia_app/shared/widgets/header/header.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vibration/vibration.dart';

import '../permissions_screen/gps_location_screen.dart';
import 'attendance_list.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with RouteAware {
  final _baseTranslate = 'home.main';
  static const URL_REDIRECTED = 'https://www.unimedfortaleza.com.br';
  final logger = UnimedLogger(className: 'HomeScreen');
  bool _alertShownBaterry = false;
  bool _alertShownCriticalBaterry = false;
  bool _disableButton = false;
  Timer? _batteryTimer;
  bool hasVibrator = false;
  bool _shouldVibrate = false;

  @override
  void initState() {
    context.read<PermissionCubit>().checkPermissions();
    //context.read<PermissionCubit>().isGpsEnabled();
    context.read<ConnectivityCubit>().verifyConnection();

    context.read<MotiveCubit>().getAttendanceMotives();

    context.read<UpdateVersionCubit>().checkVersionEvent();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      hasVibrator = await Vibration.hasVibrator();
    });
    _batteryTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      context.read<BatteryStatusCubit>().getBatteryLevel();
    });

    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await context.read<ConnectivityCubit>().verifyConnection();

      context.read<OfflineFirstCubit>().syncDatabase(
            connectedToTheInternet: context.read<ConnectivityCubit>().state
                is ConnectivityOnlineState,
          );
    });
  }

  @override
  void dispose() {
    _batteryTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        appBar: AmbulanceHeader(),
        body: MultiBlocListener(
          listeners: [
            BlocListener<PermissionCubit, PermissionCubitState>(
              listener: (context, state) {
                if (state is PermissionAppDeniedState) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => CheckPermissionsPage()),
                  );
                } else if (state is GPSdeativatedState) {
                  if (context.read<PermissionCubit>().onGpsAtivePage == false) {
                    print("Navigate to GPS LOCK SCREEN");
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => GpsLocationScreen()),
                    );
                  } else {
                    print("ja esta na GPS LOCK SCREEN");
                  }
                }
              },
            ),
            BlocListener<BatteryStatusCubit, BaterryMode>(
              listener: (context, state) {
                if (state.batteryStatusBloc == BatteryStatusBloc.attention &&
                    !_alertShownCriticalBaterry &&
                    !_alertShownBaterry) {
                  _alertShownCriticalBaterry = true;
                  _alertShownBaterry = true;
                  _disableButton = false;
                  _activateVibration();
                  AlertBaterry.open(
                    context,
                    level: state.level.toString(),
                    button: ElevatedButton(
                      style: ButtonStyle(
                        minimumSize: WidgetStateProperty.all(Size(200, 45)),
                        backgroundColor: WidgetStateProperty.all(
                          UnimedColors.green,
                        ),
                      ),
                      onPressed: _disableButton == true
                          ? null
                          : () {
                              _disableButton = !_disableButton;

                              if (_disableButton) {
                                _alertShownCriticalBaterry = false;
                                logger.d(
                                    'Alert Shown Baterry: onPressed: clicked, level: ${state.level}% state baterry: ${state.batteryStatusBloc.toString()}');
                                _activateVibration(cancelVibrator: true);
                                Navigator.pop(context);
                              }
                            },
                      child: Text("OK"),
                    ),
                  );
                } else if (state.batteryStatusBloc ==
                        BatteryStatusBloc.criticism &&
                    !_alertShownCriticalBaterry) {
                  _alertShownCriticalBaterry = true;
                  _activateVibration();
                  AlertBaterry.open(
                    context,
                    level: state.level.toString(),
                    criticalAlert: true,
                    button: BlocBuilder<BatteryStatusCubit, BaterryMode>(
                      builder: (context, state) {
                        if (state.batteryStatusBloc ==
                            BatteryStatusBloc.charging) {
                          _disableButton = false;
                          return ElevatedButton(
                            style: ButtonStyle(
                              minimumSize:
                                  WidgetStateProperty.all(Size(200, 45)),
                              backgroundColor: WidgetStateProperty.all(
                                AmbulanceColors.redClose,
                              ),
                            ),
                            onPressed: _disableButton == true
                                ? null
                                : () {
                                    _disableButton = !_disableButton;

                                    if (_disableButton) {
                                      _alertShownCriticalBaterry = false;
                                      _alertShownCriticalBaterry = false;
                                      logger.d(
                                          'Alert Shown Baterry: onPressed: clicked, level: ${state.level}% state baterry: ${state.batteryStatusBloc.toString()}');
                                      _activateVibration(cancelVibrator: true);
                                      Navigator.pop(context);
                                    }
                                  },
                            child: Text("FECHAR"),
                          );
                        }
                        return Container();
                      },
                    ),
                  );
                }
              },
            ),
            BlocListener<UpdateVersionCubit, UpdateVersionState>(
              listener: (context, state) {
                if (state is OutOfDateState) {
                  Alert.open(
                    context,
                    title: I18nHelper.translate(
                        context, '$_baseTranslate.version.alert'),
                    text: I18nHelper.translate(
                        context, '$_baseTranslate.version.text',
                        params: {
                          'remoteVersion': state.remoteVersion,
                          'localVersion': state.localVersion,
                          'br': '\n'
                        }),
                    textButtonClose: I18nHelper.translate(context,
                        state.forceUpdate ? 'common.exit' : 'common.close'),
                    callbackClose: () {
                      if (state.forceUpdate) {
                        _logout();
                      }
                    },
                    barrierDismissible: false,
                    actions: <Widget>[
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(5.0)),
                            backgroundColor: unimedOrange,
                            textStyle: TextStyle(
                              color: Colors.white,
                            )),
                        child: Text(
                          I18nHelper.translate(
                              context, '$_baseTranslate.version.update'),
                        ),
                        onPressed: () => _openStore(),
                      ),
                    ],
                  );
                }
              },
            ),
          ],
          child: BlocConsumer<AuthCubit, AuthState>(
            listener: (previousState, state) {
              if (state is DoneLogoutUserState) {
                context.read<AttendanceListCubit>().numberOfpages = 1;

                Navigator.pushReplacement(
                  context,
                  FadeRoute(page: LoginScreen()),
                );
              }
            },
            builder: (context, state) {
              if (state is LoadingAuthState ||
                  state is LoadingLogoutUserState) {
                return Center(
                  child: SpinKitThreeBounce(color: AmbulanceColors.green),
                );
              } else if (state is LoadedAuthState) {
                return Column(
                  children: [
                    TeamHearder(),
                    Expanded(child: AttendaceList()),
                  ],
                );
              } else
                return Container();
            },
          ),
        ),
      ),
    );
  }

  _logout() {
    context.read<AuthCubit>().signout();
    Navigator.popUntil(context, (route) => route.isFirst);
  }

  _openStore() async {
    late var url;
    if (Platform.isAndroid) {
      url = URL_REDIRECTED;
    } else if (Platform.isIOS) {
      url = URL_REDIRECTED;
    }
    await launchUrl(url);
  }

  Future<void> _activateVibration({bool cancelVibrator = false}) async {
    if (hasVibrator) {
      if (cancelVibrator) {
        _shouldVibrate = false;
        Vibration.cancel();
      } else {
        _shouldVibrate = true;
        while (_shouldVibrate) {
          Vibration.vibrate();
          await Future.delayed(Duration(milliseconds: 100));
        }
      }
    } else {
      if (cancelVibrator == false) {
        logger.i('_hasVibrator is: $hasVibrator');
      }
    }
  }
}

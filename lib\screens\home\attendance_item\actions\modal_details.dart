import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/shared/widgets/expandable-text.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

class DetailsAttendanceModal extends StatelessWidget {
  final AttendanceModel attendance;

  const DetailsAttendanceModal({Key? key, required this.attendance})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Detalhes do atendimento',
            style: TextStyle(
              color: AmbulanceColors.green,
              fontWeight: FontWeight.w600,
              fontSize: 22,
            ),
          ),
          SizedBox(height: 25),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _labelInfoAttendance(
                'Status',
                '${attendance.nomeStatus}',
              ),
              _labelInfoAttendance(
                'Cod. atendimento',
                '${attendance.codTipoAtendimento}',
              ),
            ],
          ),
          SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _labelInfoAttendance(
                'Cod. tipo',
                '${attendance.codTipoCliente}',
              ),
              _labelInfoAttendance(
                'Nome',
                '${attendance.nome}',
              ),
            ],
          ),
          SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _labelInfoAttendance(
                'Sintoma',
                '${attendance.sintomaAcontecendo}',
              ),
              _labelInfoAttendance(
                'Começou',
                '${attendance.sintomaComecou}',
              ),
            ],
          ),
          SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _labelInfoAttendance(
                'Sintoma Histórico',
                '${attendance.sintomaHistorico}',
              ),
              attendance.hipoteseDiagnostica.isEmpty
                  ? Container()
                  : _labelInfoAttendance(
                      'Hipótese Diagnostica',
                      '${attendance.hipoteseDiagnostica}',
                    ),
            ],
          ),
          SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _labelInfoAttendance(
                'Sintoma Quando',
                '${attendance.sintomaQuando}',
              ),
              attendance.cod_TipoAtend_Reclas.isEmpty
                  ? Container()
                  : _labelInfoAttendance(
                      'Cod. reclassificação',
                      '${attendance.cod_TipoAtend_Reclas}',
                    ),
            ],
          ),
          SizedBox(height: 10),
          attendance.observacaoMedicoRegulador.isEmpty
              ? Container()
              : Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Obs Médico',
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              color: Colors.black, fontWeight: FontWeight.bold),
                        ),
                        ExpandableText(
                            text: '${attendance.observacaoMedicoRegulador}'),
                      ],
                    ),
                    Container(),
                  ],
                )
        ],
      ),
    );
  }

  Widget _labelInfoAttendance(String info, String label, {Color? customColor}) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Wrap(
              children: [
                Text(info,
                    style: TextStyle(
                        color: customColor ?? Colors.black,
                        fontWeight: FontWeight.bold)),
              ],
            ),
            Wrap(
              children: [
                Text(label,
                    style: TextStyle(
                        color: customColor ?? Colors.black, fontSize: 13)),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

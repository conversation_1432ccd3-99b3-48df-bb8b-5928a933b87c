import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

class ClinicalRadioRow<T> extends StatelessWidget {
  final value;
  final groupValue;
  final Function(T?) onChanged;
  final text;
  final textColor;
  final keyRadio;
  ClinicalRadioRow(
      {Key? key,
      required this.value,
      required this.groupValue,
      required this.onChanged,
      this.keyRadio,
      this.text = '',
      this.textColor = AmbulanceColors.green})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          Transform.scale(
            scale: 1.6,
            child: Radio(
              key: keyRadio,
              activeColor: textColor,
              fillColor: WidgetStateProperty.all(textColor),
              value: value,
              groupValue: groupValue,
              onChanged: (dynamic value) => onChanged(value),
            ),
          ),
          Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: 20,
            ),
          ),
        ],
      ),
    );
  }
}

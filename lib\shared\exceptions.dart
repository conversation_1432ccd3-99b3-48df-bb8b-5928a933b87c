class UnimedException {
  final String? message;

  UnimedException(this.message);

  @override
  String toString() {
    return message!;
  }
}

class ServiceTimeoutException extends UnimedException {
  ServiceTimeoutException()
      : super(
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.');
}

class NoInternetException extends UnimedException {
  NoInternetException() : super('Você está sem internet no momento.');
}

class NotFoundException extends UnimedException {
  NotFoundException()
      : super('Serviço não encontrado no momento. Tente novamente mais tarde');
}

class InternalServerError extends UnimedException {
  InternalServerError(
      {String message = "Erro interno no servidor. Tente novamente mais tarde"})
      : super(message);
}

class NotFoundError extends UnimedException {
  NotFoundError()
      : super('Serviço não retornou dados. Tente novamente mais tarde');
}

class AuthException extends UnimedException {
  AuthException(String? message) : super(message);
}

class ConductException extends UnimedException {
  ConductException(String? message) : super(message);
}

class ClinicEvaluation extends UnimedException {
  ClinicEvaluation(String? message) : super(message);
}

class TeamException extends UnimedException {
  TeamException(String? message) : super(message);
}

class TeamEmptyException extends UnimedException {
  TeamEmptyException(
      {String message = "Nenhuma equipe está associada a essa ambulância."})
      : super(message);
}

class TeamNuloDriverAndTechnician extends UnimedException {
  TeamNuloDriverAndTechnician(
      {String message = "Nenhum condutor ou técnico associado à ambulância."})
      : super(message);
}

class VehicleException extends UnimedException {
  VehicleException(String message) : super(message);
}

class AttendanceException extends UnimedException {
  AttendanceException(String? message) : super(message);
}

class MapQuestException extends UnimedException {
  MapQuestException(
      {String message = 'Serviço MapQuest indisponível no momento.'})
      : super(message);
}

class SuppliesException extends UnimedException {
  SuppliesException(
      {String? message =
          'Serviço não encontrado no momento. Tente novamente mais tarde'})
      : super(message);
}

class MotiveException extends UnimedException {
  MotiveException(String message) : super(message);
}

class SignatureException extends UnimedException {
  SignatureException(String? message) : super(message);
}

class VerifySubscriptionException extends UnimedException {
  VerifySubscriptionException(String message) : super(message);
}

class CloseAttendanceException extends UnimedException {
  CloseAttendanceException(
      {String message =
          'Error ao finalizar atendimento, por favor tente novamente.'})
      : super(message);
}

class CloseAttendanceDelayReasonException extends UnimedException {
  CloseAttendanceDelayReasonException(
      {String message =
          'Error ao finalizar atendimento, por favor tente novamente.'})
      : super(message);
}

class OperatorPhotoException extends UnimedException {
  OperatorPhotoException(
      {String message = 'Error ao enviar a foto, por favor tente novamente.'})
      : super(message);
}

class SaveSignatureInDocumentsException extends UnimedException {
  SaveSignatureInDocumentsException(
      {String message = 'Erro ao tentar salvar a foto.'})
      : super(message);
}

class GetSignatureInDocumentsException extends UnimedException {
  GetSignatureInDocumentsException({String message = 'Arquivo não encontrado.'})
      : super(message);
}

class LogoutException extends UnimedException {
  LogoutException(
      {String message =
          'Serviço não encontrado no momento. Tente novamente mais tarde.'})
      : super(message);
}

class ProfilesException extends UnimedException {
  ProfilesException(String message) : super(message);
}

class VersionApiException extends UnimedException {
  VersionApiException(String message) : super(message);
}

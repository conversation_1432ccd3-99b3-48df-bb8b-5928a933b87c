# 🛠️ Integration Test App Ambulancia

## 📝 Começando

  - [Introdução a testes de integração no flutter;](https://docs.flutter.dev/cookbook/testing/integration/introduction)
  - [An<PERSON><PERSON><PERSON> de desempenho;](https://docs.flutter.dev/cookbook/testing/integration/profiling)

## Widgets de teste
  - [Uma introdução ao teste de widget;](https://docs.flutter.dev/cookbook/testing/widget/introduction)
  - [Find widgets;](https://docs.flutter.dev/cookbook/testing/widget/finders)
  - [Handle scrolling;](https://docs.flutter.dev/cookbook/testing/widget/scrolling)
  - [Tap, drag, and enter text;](https://docs.flutter.dev/cookbook/testing/widget/tap-drag)

#### 📁 **Screens**
  - screen: Onde se localiza todas as páginas com seus respectivos casos de teste.
  - test_cases: Os casos de teste das respectivas páginas

#### 📁 **Shared**
  - utils: Ideal para a criação de funções que serão compartilhadas em vários casos de teste, por exemplo a função de 'login_sucess' usado para realizar o login da aplicação.
  - messages: Ideal para centralizar mensagens que estão sendo usadas pela a validação dos testes, como por exemplo mensagens de informações, error ou validações.

## 📱 Emulador usado (Tablet)


Nome | Resolução| Imagem
:--------- | :------ | :-------
10.1 WXGA (Tablet) API 33 | 10.1 800x1280 mdpi  | Android Tiramisu x86_64


## 💻 Comandos
```dart
  //Executar todos os testes 
  flutter test integration_test/screens --dart-define-from-file=lib/hmg.env.json
```
```dart
  //Executar todos os casos teste de uma unica página (Login)
  flutter test integration_test/screens/login/test_cases/ --dart-define-from-file=lib/hmg.env.json
```

```dart
  //Executar somente um teste específico de uma página (Login)
  flutter test integration_test/screens/login/test_cases/login_sucess_test.dart --dart-define-from-file=lib/hmg.env.json
```

## Passo a passo execução

  | Passo N° | Descrição                                                                                                                                                 |
|--------|----------------------------------------------------------------------------------------------------------------------------------------------------------|
| Passo 1| Criar no Sabius alguns atendimentos para que seja possível realizar os testes.                                                                           |
| Passo 2| Verificar a classe `login/test_cases/login_success_test.dart`. Modificar o `selectVehicle`, que é o veículo onde serão realizados os testes. Na mesma classe, alterar o usuário e senha. |
| Passo 3| Escolher o caso de teste a ser testado e executar o seguinte comando: `flutter test integration_test/screens/login/test_cases/login_success_test.dart`. |
| Passo 4| Além de executar por linha de comando, também é possível utilizar o VSCode para realizar o teste. Clicar no ícone de teste na barra lateral direita e escolher o caso de teste ou o grupo de teste a ser executado.   |
  

>⚠️ Alguns test_cases não poderar ser executados todos de uma vez, nessa primeira versão dos testes de integração, pois como usamos o serviço do sabius os identificadores usados atualmente sofrem alterações. 

>⚠️ Alguns test_cases por exemplo **home_select_and_attendace_list_index_one_without_selectiong_teams_test.dart**, precisa ainda de intervenção na autorização do uso de localização, camaras etcs. Pois são recursos do nativo que o teste de integração do flutter ainda não consegue interagir. Nesse caso em especifico basta executar o comando do flutter drive:

```dart
  //Para conceder as permissões necessaria para a execução do teste
 flutter drive   --driver=test_driver/integration_test_driver.dart   --target=integration_test/screens/attendance/test_cases/closure/minimum_emergency_care_with_protocol_and_without_reclassification_test.dart \--dart-define-from-file=lib/hmg.env.json
```


import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ambulancia_app/main.dart' as app;

import '../../../shared/text/messages/messages_validation.dart';
import '../../../shared/utils/initial_team_for_animation.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets("Teste para realizar login com senha invalido",
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await initialTeamForAnimation(tester);

    //Selecionar um veiculo
    final Finder dropdownVehicle = find.byKey(const Key('login_vehicle')).last;
    await tester.tap(dropdownVehicle);
    await tester.pumpAndSettle();

    final Finder selectDropdownItem = find.text('UTI 15 POQ1G22').last;
    await tester.tap(selectDropdownItem);
    await tester.pumpAndSettle();

    // Digitar usuário com cpf invalido
    final Finder textFieldUsername = find.byKey(const Key('login_username'));
    await tester.enterText(textFieldUsername, '60866109323');
    await tester.pumpAndSettle();

    //Digitar senha de acesso
    final Finder textFieldPassword = find.byKey(const Key('login_password'));
    await tester.enterText(textFieldPassword, '1234567');
    await tester.pumpAndSettle();

    //Clicar no botão de login
    final Finder elevatedButtonLogin = find.byKey(Key("login_button"));
    await tester.tap(elevatedButtonLogin);
    await tester.pumpAndSettle();

    // Exibir mensagem de erro de credenciais
    final Finder messageErrorCredentials =
        find.text(textErrorCredentialsInvalid);

    expect(messageErrorCredentials, findsOneWidget);
  });
}
